#database setting
spring.jpa.database=mysql
spring.datasource.url=***********************************************************************************************************
spring.datasource.username=hongfund_os
spring.datasource.password=hongfund_os_5013
#disable automatic initialize for mysql
spring.jpa.hibernate.ddl-auto=none
spring.jpa.showSql=false
spring.datasource.initialization-mode=never
spring.datasource.sqlScriptEncoding=UTF-8
spring.flyway.enabled=false

#tomcat setting
server.tomcat.max-connections=0
server.tomcat.accept-count=0
server.tomcat.processor-cache=0
server.tomcat.background-processor-delay=30
server.tomcat.threads.max=0
server.tomcat.threads.min-spare=0

#custom settings
app.loginTimeoutDays=10
email.url=https://fctest.hongfund.com:8501
server.http.port=8088
redisson.host=fctest.hongfund.com
redisson.password=pupu!8dsn&plQJ&K

# logging settings
logging.file.name=/var/log/hongfund/qa/HongFundOSCloud.log
#logging.level.root=WARN

# /info endpoint
info.app.name=hongfund qa system
info.app.version=1.0

#https port
server.port = 8443
server.ssl.key-store = classpath:hongfund.com.pfx
server.ssl.key-store-password = zch7cu5j

# admin settings
spring.boot.admin.client.username=admin
spring.boot.admin.client.password=superadmin
spring.boot.admin.client.instance.name=qa
spring.boot.admin.client.url=http://fctest.hongfund.com:8899
spring.boot.admin.client.instance.service-url=https://fctest.hongfund.com:8501
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always