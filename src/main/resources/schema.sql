/*用户表*/
drop table if exists account;
create table account
(
    id             bigint      NOT NULL AUTO_INCREMENT,
    name           varchar(64) not null,
    email          varchar(128),
    phone          varchar(255),
    hash_password  varchar(255),
    role           varchar(32),
    manager        bigint,
    hidden         varchar(16) not null default 'n', /*n或空：未删除；y：回收站；d：彻底删除*/
    area           varchar(64), /*地区*/
    code           varchar(64),
    self_phone     varchar(64), /*个人手机（新加1）*/
    login_id       varchar(64), /*个人账号（新加1）*/
    join_time      varchar(64), /*员工加入时间（新加1）*/
    no             varchar(64), /*员工级次编码（新加1）*/
    pno            varchar(64), /*父级次编码（新加1）*/
    level          int(8), /*等级（新加1）*/
    leaf           int(8), /*是否叶子节点（新加1）*/
    party_name     varchar(64), /*加盟商名称*/
    party_id       bigint, /*加盟商id*/
    is_disable_tax int(8) not null default 0, /*加盟商是否付款 0：否  1：是*/
    worker_index   int(4) not null default 0,
    party_state    int(4) not null default 0,
    party_index    int(4) not null default 0,
    type_auth      int default 0 comment '验证：方式 0：网页 1：APP',
    software_type  int default 0 comment '软件类型 0：普通版 1：企业版（基础） 2：企业版（升级）',
    permission     varchar(255) comment '权限',
    primary key (id)
);
create index IDX_account_role on account (role);
create index IDX_account_no on account (no);
create index IDX_account_name_loginid on account (name, login_id);

/*科目表*/
drop table if exists subject;
create table subject
(
    id               bigint       NOT NULL AUTO_INCREMENT,
    no               varchar(32)  not null, /*科目编码*/
    text             varchar(128) not null, /*科目名称*/
    subtype_code     int          not null, /*类别  1:资产  2:负债  3:权益  4:成本  5:损益 6：共同 7：净资产 8:收入 9：费用*/
    direction        int          not null, /*借贷方向  1:借  2:贷*/
    level            int          not null, /*科目级次*/
    is_init          int          not null, /*是否预置*/
    measuring_unit   varchar(8), /*计量单位*/
    curr_symbol      varchar(3), /*外币符号*/
    dim_account_type varchar(64), /*辅助核算类型名称*/
    account_book_id  bigint, /*账套id*/
    status           int, /*是否禁用 0:启用，1：禁用*/
    pno              varchar(32), /*父科目编码*/
    leaf             int          not null, /*是否叶子节点*/
    hidden           varchar(16)  not null default 'n', /*n或空：未删除；y：回收站；d：彻底删除*/
    cash_item_in     varchar(255), /*现金流入项目*/
    cash_item_out    varchar(255), /*现金流出项目*/
    long_text        varchar(255), /*全级次名称*/
    primary key (id)
);
create unique index UK_subject_no on subject (account_book_id, no);
create unique index UK_subject_long_text on subject (account_book_id, long_text);
create index IDX_subject_book on subject (account_book_id);
create index IDX_subject_typename on subject (dim_account_type);

/*账套表*/
drop table if exists account_book;
create table account_book
(
    id                bigint      NOT NULL AUTO_INCREMENT,
    book_name         varchar(64), /*账套名称*/
    book_code         varchar(64), /*账套编码*/
    accounter_id      bigint, /*记账人*/
    start_period      varchar(8), /*建账年月*/
    addtax            varchar(255), /*增值税*/
    accounting_system varchar(255), /*会计制度*/
    account_status    int, /*记账状况*/
    tax_status        int, /*报税状况*/
    charge_status     int, /*收费状况*/
    hidden            varchar(16) not null default 'n', /*n或空：未删除；y：回收站；d：彻底删除*/
    close_period      varchar(8), /*结账期间*/
    current_period    varchar(8), /*当前期间*/
    remark            varchar(255), /*备注(易代账）*/
    reviewer          varchar(64), /*审核人*/
    maker             varchar(64), /*制单人*/
    max_income        int, /*最大收入*/
    short_name        varchar(64), /*客户简称*/
    first_case        varchar(8), /*客户简称首字母*/
    accounter_no      varchar(64), /*员工级次编码（新加1）*/
    area              varchar(64), /*地区*/
    label             varchar(16), /*标签*/
    type              int                  default 0 comment '类型 0:普通 1:托管',
    is_disable_tax    int(8) not null default 0, /*加盟商是否付款 0：否  1：是*/
    primary key (id)
);
create index IDX_book_accounter_addtax_hidden_status on account_book (accounter_no, addtax, hidden, account_status);

/*账套信息表*/
drop table if exists account_book_info;
create table account_book_info
(
    id                       bigint NOT NULL AUTO_INCREMENT,
    account_book_id          bigint, /*账套id*/
    account_book_name        varchar(255), /*账套名称*/
    tax_payer_code           varchar(255), /*纳税人识别号*/
    tax_payer_id             varchar(255), /*纳税人身份证号*/
    national_name            varchar(255), /*国税登录名*/
    national_login_way       int, /*国税登录方式*/
    national_pwd             varchar(255), /*国税密码*/
    tax_payer_name           varchar(255), /*国税登录名*/
    tax_payer_pwd            varchar(255), /*国税密码*/
    land_login_way           int, /*地税登录方式*/
    land_pwd                 varchar(255), /*地税密码*/
    personal_login_way       int, /*个税登录方式*/
    personal_pwd             varchar(255), /*个税密码*/
    real_declare_name        varchar(255), /*实名用户名*/
    real_declare_password    varchar(255), /*实名密码*/
    personal_bank_account    varchar(255), /*个税扣款银行账号*/
    legal_person             varchar(255), /*法人*/
    registered_capital       varchar(255), /*注册资本*/
    registered_no            varchar(255), /*注册号*/
    organization_code        varchar(255), /*组织机构代码*/
    social_credit_code       varchar(255), /*社会信用代码*/
    establish_date           varchar(255), /*成立日期*/
    company_type             varchar(255), /*公司类型*/
    industry_category        varchar(255), /*国标行业门类*/
    industry_large_category  varchar(255), /*国标行业大类*/
    industry_medium_category varchar(255), /*国标行业中类*/
    industry_small_category  varchar(255), /*国标行业小类*/
    business_scope           varchar(2048), /*经营范围*/
    business_status          varchar(255), /*经营状态*/
    address                  varchar(255), /*地址*/
    business_period          varchar(255), /*营业期限*/
    is_virtual_address       int, /*是否为虚拟地址*/
    is_inventory             int, /*是否启用库存模块*/
    is_month_tax             int, /*是否为月度增值税申报*/
    is_month_finance         int, /*是否为月度财务报表申报*/
    business_type            int, /*经营类型*/
    service_pwd              varchar(255), /*服务密码*/
    province                 varchar(24), /*所属省份*/
    city                     varchar(24), /*所属城市*/
    irrigation_works         int, /*水利建设*/
    union_funds              int, /*工会经费*/
    stamp_tax_basis          int, /*印花税计税依据（0：不含税 1：含税）*/
    tax_payer_role           varchar(8), /*办税人身份*/
    tax_payer_phone          varchar(11), /*办税人手机号*/
    tax_authorities          varchar(255) comment '主管税务机关',
    gsnb_id                  varchar(255) comment '工商联络员身份证号码',
    gsnb_password            varchar(255) comment '工商登录密码',
    gsnb_phone               varchar(11) comment '工商联络员手机号',
    gsnb_name                varchar(64) comment '工商联络员姓名',
    sub_office               varchar(255) comment '主管科所分局',
    department_no            varchar(255) comment '部门编号',
    login_way                int comment '电子税务局登录方式 1:旧版登录；2：新版登录；3：代理登录',
    national_name_new        varchar(255) comment '用户名',
    national_pwd_new         varchar(255) comment '个人用户密码',
    tax_payer_role_new       varchar(8) comment '新版：办税人身份',
    tax_payer_phone_new      varchar(11) comment '新版：办税人手机号',
    national_name_auth       varchar(255) comment '验证：用户名',
    national_pwd_auth        varchar(255) comment '验证：个人用户密码',
    tax_payer_phone_auth     varchar(11) comment '验证：办税人手机号',
    type_auth                int default 0 comment '验证：方式 0：网页 1：APP',
    social_credit_code_agent varchar(255) comment '代理：税号',
    national_name_agent      varchar(255) comment '代理：用户名',
    national_pwd_agent       varchar(255) comment '代理：个人用户密码',
    tax_payer_phone_agent    varchar(11) comment '代理：办税人手机号',
    national_bank_account    varchar(255) comment '电子税局扣款银行账号',
    national_name_final      varchar(255) comment '登录最终用户名（用于连表查询）',
    ai_helper_state          int default 0 comment 'ai助手状态 0：未启用，1：启用，2：停用',
    primary key (id)
);
create index IDX_bookinfo_book on account_book_info (account_book_id);

drop table if exists tax_form;
create table tax_form
(
    id                        BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create                datetime default current_timestamp comment '创建时间',
    gmt_modified              datetime default current_timestamp comment '修改时间',
    account_book_id           bigint not null comment '账套id' unique,
    is_vat                    int comment '是否申报增值税',
    is_vat_month              int comment '是否增值税月度申报',
    is_surtax                 int comment '是否申报附加税',
    is_stamp                  int comment '是否申报印花税',
    is_stamp_month            int comment '是否印花税月度申报',
    stamp_basis               int comment '印花税计税依据（0：不含税 1：含税）',
    stamp_basis_jx            int comment '印花税计税依据进项（0：不含税 1：含税）',
    is_finance                int comment '是否申报财务报表',
    is_finance_month          int comment '是否财务报表月度申报',
    is_income                 int comment '是否申报企业所得税',
    is_excise                 int comment '是否申报消费税',
    is_water                  int comment '是否申报水利建设',
    is_water_zero             int comment '是否水利建设零申报',
    is_union                  int comment '是否申报工会经费',
    is_union_zero             int comment '是否工会经费零申报',
    is_culture                int comment '是否申报文化建设',
    is_culture_zero           int comment '是否文化建设零申报',
    is_disabled               int comment '是否申报残疾人保障金',
    is_disabled_month         int comment '是否残疾人保障金月度申报',
    disabled_balance          decimal(14, 2) comment '残疾人保障金金额',
    disabled_num              int comment '上年在职员工人数',
    disabled_num2             int comment '上年安排残疾人就职人数',
    is_income_operating       int      default 1 comment '是否生产经营所得税 0：否，1:是',
    is_income_operating_month int      default 0 comment '是否生产经营所得税月度申报 0:季报，1：月报',
    is_social_security        int      default 0 comment '是否申报社保 0:否、1：是',
    primary key (id)
);
create index IDX_tax_form_book on tax_form (account_book_id);

/*账套联系人信息表*/
drop table if exists account_book_contact;
create table account_book_contact
(
    id              bigint NOT NULL AUTO_INCREMENT,
    account_book_id bigint, /*账套id*/
    name            varchar(64), /*联系人姓名*/
    phone           varchar(64), /*联系人电话*/
    primary key (id)
);

/*余额表*/
drop table if exists balance;
create table balance
(
    id              bigint NOT NULL AUTO_INCREMENT,
    initbalance     decimal(14, 2), /*期初余额*/
    initbalance_org decimal(14, 2), /*期初余额原币*/
    initnumber      decimal(16, 4), /*期初数量*/
    jfbalance       decimal(14, 2), /*借方余额*/
    jfbalance_org   decimal(14, 2), /*借方余额原币*/
    jfnumber        decimal(16, 4), /*借方数量*/
    dfbalance       decimal(14, 2), /*贷方余额*/
    dfbalance_org   decimal(14, 2), /*贷方余额原币*/
    dfnumber        decimal(16, 4), /*贷方数量*/
    subject_id      bigint, /*科目id*/
    subject_no      varchar(64), /*科目编号*/
    period varchar (64), /*期间*/
    endbalance      decimal(14, 2), /*期末余额*/
    endbalance_org  decimal(14, 2), /*期末余额原币*/
    endnumber       decimal(16, 4), /*期末数量*/
    account_book_id bigint, /*账套id*/
    primary key (id)
);
create index IDX_balance_subject on balance (subject_id);
create index IDX_balance_book on balance (account_book_id);
create index IDX_balance_period on balance (period);
create unique index UK_balance on balance (subject_id, period);

/*科目预置表*/
drop table if exists subject_preset;
create table subject_preset
(
    id                bigint      NOT NULL AUTO_INCREMENT,
    no                varchar(32) not null, /*科目编码*/
    text              varchar(64) not null, /*科目名称*/
    accounting_system varchar(255), /*会计制度  1:2013小企业会计准则  2:2007企业会计准则 3：民间非营利组织会计制度*/
    subtype_code      int         not null, /*类别  1:资产  2:负债  3:权益  4:成本  5:损益*/
    direction         int         not null, /*借贷方向  1:借  2:贷*/
    level             int         not null, /*科目级次*/
    is_init           int         not null, /*是否预置*/
    pno               varchar(32), /*父科目编码*/
    leaf              int         not null, /*是否叶子节点*/
    cash_item_in      varchar(255), /*现金流入项目*/
    cash_item_out     varchar(255), /*现金流出项目*/
    long_text         varchar(64), /*全级次名称*/
    primary key (id)
);

/*期间表*/
drop table if exists period;
create table period
(
    id              bigint NOT NULL AUTO_INCREMENT,
    account_book_id bigint,
    period varchar (6),
    primary key (id)
);
create unique index UK_period on period (account_book_id, period);
create index IDX_period_book on period (account_book_id);

/*凭证号表*/
drop table if exists voucher_no;
create table voucher_no
(
    id              bigint NOT NULL AUTO_INCREMENT,
    account_book_id bigint, /*账套id*/
    current_no      varchar(255), /*当前序号*/
    period varchar (64), /*会计期间*/
    primary key (id)
);
create unique index UK_voucher_no on voucher_no (account_book_id, period, current_no);
create index IDX_voucherno_book on voucher_no (account_book_id);

/*凭证表*/
drop table if exists voucher;
create table voucher
(
    id              bigint   NOT NULL AUTO_INCREMENT,
    account_book_id bigint, /*帐套id*/
    accessory_no    int, /*附件数*/
    audit_date      datetime, /*审核日期*/
    audit_person    varchar(32), /*审核人*/
    cash_flow_flag  int, /*是否自动分配现金流量 0:否，1：是*/
    category        int, /*凭证分类 1:记账凭证*/
    dftotal         decimal(14, 2), /*贷方合计*/
    isassigned      int, /*是否分配现金流量（没值）0:否，1：是*/
    jftotal         decimal(14, 2), /*借方合计*/
    operator        int, /*操作状态  0:未操作，1：已操作*/
    period varchar (64), /*期间*/
    remark          varchar(255), /*备注*/
    state           int, /*凭证状态 0：未审核，1：已审核*/
    voucher_date    datetime NOT NULL, /*凭证日期*/
    voucher_no      varchar(64), /*凭证编号*/
    written_date    TIMESTAMP default CURRENT_TIMESTAMP, /*凭证创建日期*/
    written_person  varchar(32), /*制单人*/
    primary key (id)
);
create unique index UK_voucher on voucher (account_book_id, period, voucher_no);

/*凭证分录表*/
drop table if exists voucher_record;
create table voucher_record
(
    id                      bigint NOT NULL AUTO_INCREMENT,
    account_book_id         bigint, /*帐套id*/
    subject_dim_relation_id bigint, /*科目与辅助核算关联关系*/
    currencyexchange        decimal(14, 2), /*外币汇率*/
    dfcurrency              decimal(14, 2), /*贷方外币金额*/
    dfmoney                 decimal(14, 2), /*贷方金额*/
    dfmoney_org             decimal(14, 2), /*贷方原币金额*/
    dfnumber                decimal(16, 4), /*贷方数量*/
    exchange_rate           decimal(14, 5), /*汇率*/
    foresub_text            varchar(255), /*科目全级次名称*/
    jfcurrency              decimal(14, 2), /*借方外币金额*/
    jfmoney                 decimal(14, 2), /*借方金额*/
    jfmoney_org             decimal(14, 2), /*借方原币金额*/
    jfnumber                decimal(16, 4), /*借方数量*/
    subject_id              bigint, /*subject*/
    summary                 varchar(255), /*凭证摘要*/
    unit_price              decimal(14, 4), /*单价*/
    voucher_id              bigint, /*voucher*/
    primary key (id)
);
create index IDX_voucherrecord_book_voucherid on voucher_record (account_book_id, voucher_id);
create index IDX_voucherrecord_voucherid on voucher_record (voucher_id);
create index IDX_voucherrecord_subject on voucher_record (subject_id);

/*固定资产表*/
drop table if exists fixed_assets;
create table fixed_assets
(
    id                 bigint      NOT NULL AUTO_INCREMENT,
    account_book_id    bigint, /*帐套ID*/
    assets_name        varchar(128), /*资产名称*/
    buy_period         varchar(128), /*购买日期*/
    clean_period       varchar(128), /*清理期间*/
    depreciate_subject varchar(128), /*折旧科目*/
    fee_subject        varchar(128), /*费用科目*/
    is_init            int, /*是否期初*/
    month_depreciate   decimal(14, 2), /*月折旧额*/
    this_depreciate    decimal(14, 2), /*本月折旧*/
    old_value_rate     decimal(14, 2), /*残值率*/
    original_value     decimal(14, 2), /*原值*/
    total_depreciate   decimal(14, 2), /*累计折旧*/
    use_year           int, /*使用年限*/
    use_month          int, /*使用期限（月）*/
    method             int, /*折旧方法：1、年限平均；2、双倍余额递减；3、不折旧*/
    fixed_assets_type  VARCHAR(128), /*类别名称*/
    hidden             varchar(16) not null default 'n', /*n或空：未删除；y：回收站；d：彻底删除*/
    write_period       varchar(128), /*录入期间*/
    asset_type         int                  default 1 comment '资产类型 1:固定资产；2:无形资产；3:长期待摊费用',
    number             decimal(14, 2)       default 0 comment '数量',
    remark             varchar(255) comment '备注',
    primary key (id)
);
create index IDX_fixedassets_book_writeperiod on fixed_assets (account_book_id, write_period);

/*现金流量项目预置表*/
drop table if exists cash_flow_item_preset;
create table cash_flow_item_preset
(
    id                bigint NOT NULL AUTO_INCREMENT,
    code              varchar(255), /*现金流量项目编码*/
    name              varchar(255), /*现金流量项目名称*/
    rowindex          int, /*行次*/
    flowdir           int, /*方向*/
    leaf              int, /*是否叶子节点*/
    pid               bigint, /*父节点ID*/
    formula           varchar(255), /*计算公式*/
    accounting_system varchar(255), /*会计制度*/
    isinit            int, /*是否允许录入期初*/
    space             int, /*空格个数*/
    is_switch         int, /*是否允许切换项目*/
    primary key (id)
);

/*现金流量项目表*/
drop table if exists cash_flow_item;
create table cash_flow_item
(
    id              bigint NOT NULL AUTO_INCREMENT,
    account_book_id bigint, /*账套id*/
    code            varchar(64), /*现金流量项目编码*/
    flowdir         int, /*方向*/
    formula         varchar(255), /*计算公式*/
    initbalance     decimal(14, 2) DEFAULT 0, /*期初余额*/
    isinit          int, /*是否可以录入期初*/
    is_switch       int, /*"是否允许切换项目（现金流量明细表中是否可以选择该项）"*/
    leaf            int, /*是否叶子节点*/
    name            varchar(255), /*现金流量项目名称*/
    pid             bigint, /*父节点ID*/
    rowindex        int, /*行次*/
    space           int, /*空格个数*/
    primary key (id)
);
create index IDX_cashflowitem_book on cash_flow_item (account_book_id);

/*现金流量明细表*/
drop table if exists cash_flow_detail;
create table cash_flow_detail
(
    id                bigint NOT NULL AUTO_INCREMENT,
    account_book_id   bigint, /*帐套ID*/
    cash_flow_item_id bigint, /*现金流量项目id*/
    flow_cash         decimal(14, 2), /*现金流量金额*/
    flow_dir          int, /*方向*/
    period varchar (64), /*期间*/
    subject_id        bigint, /*对方科目ID*/
    voucher_id        bigint, /*凭证*/
    voucher_record_id bigint, /*凭证分录*/
    primary key (id)
);
create index IDX_cashflowdetail_book_period on cash_flow_detail (account_book_id, period);
create index IDX_cashflowdetail_voucherid on cash_flow_detail (voucher_id);
create index IDX_cashflowdetail_subjectid on cash_flow_detail (subject_id);

/*辅助客户表*/
drop table if exists dim_customer;
create table dim_customer
(
    id              bigint      NOT NULL AUTO_INCREMENT, /*主键*/
    account_book_id bigint, /*账簿*/
    bank_account    varchar(256), /*银行账号*/
    code            varchar(256), /*速记码*/
    contact         varchar(256), /*联系人*/
    fullcode        varchar(256), /*全拼*/
    hidden          varchar(16) not null default 'n', /*n或空：未删除；y：回收站；d：彻底删除*/
    name            varchar(256), /*客户名称*/
    no              varchar(4), /*客户编码*/
    phone           varchar(256), /*联系方式*/
    tax_no          varchar(256), /*客户税号*/
    primary key (id)
);
create unique index UK_dim_customer on dim_customer (account_book_id, no);
create index IDX_dim_customer_book on dim_customer (account_book_id);

/*辅助部门表*/
drop table if exists dim_department;
create table dim_department
(
    id              bigint      NOT NULL AUTO_INCREMENT, /*主键*/
    account_book_id bigint, /*账簿*/
    code            varchar(256), /*速记码*/
    fullcode        varchar(256), /*全拼*/
    hidden          varchar(16) not null default 'n', /*n或空：未删除；y：回收站；d：彻底删除*/
    leaf            int, /*是否是末级部门*/
    name            varchar(256), /*部门名称*/
    no              varchar(4), /*部门编码*/
    pid             bigint, /*上级部门id*/
    pno             varchar(256), /*上级部门编码*/
    primary key (id)
);
create unique index UK_dim_department on dim_department (account_book_id, no);
create index IDX_dim_department_book on dim_department (account_book_id);

/*辅助员工表*/
drop table if exists dim_employee;
create table dim_employee
(
    id                 bigint      NOT NULL AUTO_INCREMENT, /*主键*/
    gmt_create         datetime             default current_timestamp comment '创建时间',
    gmt_modified       datetime             default current_timestamp comment '修改时间',
    account_book_id    bigint, /*账簿*/
    code               varchar(256), /*速记码*/
    fullcode           varchar(256), /*全拼*/
    hidden             varchar(16) not null default 'n', /*n或空：未删除；y：回收站；d：彻底删除*/
    name               varchar(256), /*员工名称*/
    no                 varchar(4), /*员工编码*/
    phone              varchar(11), /*手机号*/
    license_type       int, /*证照类型*/
    license_no         varchar(64), /*证照号码*/
    employ_type        int, /*任职受雇从业类型 0：雇员 1：其他*/
    start_date         varchar(10), /*受雇日期*/
    end_date           varchar(10), /*离职日期*/
    dim_department_id  bigint, /*所属部门*/
    country            varchar(64), /*国籍(地区)*/
    gender             int, /*性别*/
    birthday           varchar(10), /*出生日期*/
    birth_country      varchar(64), /*出生国家(地区)*/
    entry_date         varchar(10), /*首次入境时间*/
    departure_date     varchar(10), /*预计离境时间*/
    declare_type       int, /*申报类型 0：综合申报 1：非居民申报*/
    is_six             int(4) NOT NULL default 0, /*减除费用是否按六万扣除 0：否 1：是*/
    is_base_deduction  int(4) NOT NULL default 1, /*是否扣除减除费用 0：否 1：是*/
    other_license_type varchar(255), /*其他证照类型*/
    other_license_no   varchar(255), /*其他证照号码*/
    primary key (id)
);
create unique index UK_dim_employee on dim_employee (account_book_id, no);
create index IDX_dim_employee_book on dim_employee (account_book_id);

/*辅助存货表*/
drop table if exists dim_inventory;
create table dim_inventory
(
    id              bigint      NOT NULL AUTO_INCREMENT, /*主键*/
    account_book_id bigint, /*账簿*/
    code            varchar(256), /*速记码*/
    fullcode        varchar(256), /*全拼*/
    hidden          varchar(16) not null default 'n', /*n或空：未删除；y：回收站；d：彻底删除*/
    measure_unit    varchar(256), /*计量单位*/
    name            varchar(256), /*存货名称*/
    no              varchar(4), /*存货编码*/
    model           varchar(256), /*规格型号*/
    type            int, /*商品类型*/
    main_id         bigint, /*主存货id*/
    main_per        decimal(16, 6), /*主存货转换比例*/
    primary key (id)
);
create unique index UK_dim_inventory on dim_inventory (account_book_id, no);
create index IDX_dim_inventory_book on dim_inventory (account_book_id);

/*辅助余额表*/
drop table if exists dim_balance;
create table dim_balance
(
    id                      bigint NOT NULL AUTO_INCREMENT, /*主键*/
    account_book_id         bigint, /*账簿*/
    dfbalance               decimal(14, 2), /*贷方余额，本位币*/
    dfbalance_org           decimal(14, 2), /*贷方余额，原币*/
    dfnumber                decimal(16, 4), /*贷方累计数量*/
    endbalance              decimal(14, 2), /*期末余额，本位币*/
    endbalance_org          decimal(14, 2), /*期末余额，原币*/
    endnumber               decimal(16, 4), /*期末数量*/
    initbalance             decimal(14, 2), /*期初余额，本位币*/
    initbalance_org         decimal(14, 2), /*期初余额，原币*/
    initnumber              decimal(16, 4), /*期初数量*/
    jfbalance               decimal(14, 2), /*借方余额，本位币*/
    jfbalance_org           decimal(14, 2), /*借方余额，原币*/
    jfnumber                decimal(16, 4), /*借方累计数量*/
    period varchar (256), /*期间*/
    subject_id              bigint, /*科目*/
    subject_no              varchar(255), /*科目编码*/
    subject_dim_relation_id bigint, /*科目辅助核算组合关联表*/
    primary key (id)
);
create index IDX_dim_balance_book on dim_balance (account_book_id);
create index IDX_dim_balance_relation on dim_balance (subject_dim_relation_id);
create index IDX_dim_balance_period on dim_balance (period);
create index IDX_dim_balance_subject on dim_balance (subject_id);
create unique index UK_dim_balance on dim_balance (subject_dim_relation_id, period);

/*辅助项目表*/
drop table if exists dim_item;
create table dim_item
(
    id              bigint      NOT NULL AUTO_INCREMENT, /*主键*/
    account_book_id bigint, /*账簿*/
    code            varchar(255), /*速记码*/
    fullcode        varchar(255), /*全拼*/
    hidden          varchar(16) not null default 'n', /*n或空：未删除；y：回收站；d：彻底删除*/
    name            varchar(256), /*项目名称*/
    no              varchar(4), /*项目编码*/
    primary key (id)
);
create unique index UK_dim_item on dim_item (account_book_id, no);
create index IDX_dim_item_book on dim_item (account_book_id);

/*辅助供应商表*/
drop table if exists dim_provider;
create table dim_provider
(
    id              bigint      NOT NULL AUTO_INCREMENT, /*主键*/
    account_book_id bigint, /*账簿*/
    bank_account    varchar(256), /*银行账号*/
    code            varchar(256), /*速记码*/
    contact         varchar(256), /*联系人*/
    fullcode        varchar(256), /*全拼*/
    hidden          varchar(16) not null default 'n', /*n或空：未删除；y：回收站；d：彻底删除*/
    name            varchar(256), /*供应商名称*/
    no              varchar(4), /*供应商编码*/
    phone           varchar(256), /*联系方式*/
    tax_no          varchar(256), /*供应商税号*/
    primary key (id)
);
create unique index UK_dim_provider on dim_provider (account_book_id, no);
create index IDX_dim_provider_book on dim_provider (account_book_id);

/*辅助关系表*/
drop table if exists subject_dim_relation;
create table subject_dim_relation
(
    id                bigint NOT NULL AUTO_INCREMENT, /*主键*/
    account_book_id   bigint, /*账簿*/
    dim_customer_id   bigint, /*辅助核算客户表*/
    dim_department_id bigint, /*辅助核算部门表*/
    dim_employee_id   bigint, /*辅助核算员工表*/
    dim_inventory_id  bigint, /*辅助核算存货表*/
    dim_item_id       bigint, /*辅助核算项目表*/
    dim_provider_id   bigint, /*辅助核算供应商表*/
    subject_id        bigint, /*科目*/
    primary key (id)
);
create index IDX_relation_subject on subject_dim_relation (subject_id);
create index IDX_relation_book on subject_dim_relation (account_book_id);

/*历史收入表*/
drop table if exists history_income;
create table history_income
(
    id                bigint NOT NULL AUTO_INCREMENT, /*主键*/
    account_book_id   bigint, /*账簿*/
    income_type       int, /*收入类型*/
    goods_income      decimal(14, 2), /*货物收入*/
    industrial_income decimal(14, 2), /*工业收入*/
    service_income    decimal(14, 2), /*服务收入*/
    period varchar (64), /*期间*/
    primary key (id)
);
create unique index UK_history_income on history_income (account_book_id, period, income_type);
create index IDX_historyincome_book on history_income (account_book_id);
create index IDX_historyincome_period on history_income (period);

/*征期表*/
drop table if exists collection_period;
create table collection_period
(
    id        bigint NOT NULL AUTO_INCREMENT, /*主键*/
    area      varchar(64), /*地区*/
    year      int, /*年*/
    month     int, /*月*/
    start_day int, /*开始时间*/
    end_day   int, /*结束时间*/
    primary key (id)
);

/*操作记录表*/
drop table if exists operation_log;
create table operation_log
(
    id              bigint NOT NULL AUTO_INCREMENT, /*主键*/
    operate_type    varchar(32), /*操作类型*/
    operate_time    TIMESTAMP default CURRENT_TIMESTAMP, /*操作时间*/
    account_book_id bigint, /*账套id*/
    period varchar (64), /*期间*/
    accounter_id    bigint, /*操作人*/
    accounter_no    varchar(64), /*员工级次编码（新加1）*/
    primary key (id)
);
create index IDX_operationlog_accounterno on operation_log (accounter_no);
create index IDX_operationlog_bookid on operation_log (account_book_id);
create index IDX_operationlog_operatetype on operation_log (operate_type);
create index IDX_operationlog_operatetime on operation_log (operate_time);

/*区域表*/
drop table if exists region;
create table region
(
    id          bigint NOT NULL AUTO_INCREMENT, /*主键*/
    code        varchar(16), /*区域编号*/
    region_name varchar(16), /*区域名称*/
    primary key (id)
);

/*系统配置表*/
drop table if exists system_setting;
create table system_setting
(
    id              bigint NOT NULL AUTO_INCREMENT, /*主键*/
    account_book_id bigint, /*账套id*/
    keyword         varchar(64), /*系统配置键*/
    text            varchar(64), /*系统配置描述*/
    value           varchar(64), /*系统配置值*/
    primary key (id)
);

/*收费表*/
drop table if exists charge;
create table charge
(
    id              bigint NOT NULL AUTO_INCREMENT, /*主键*/
    account_book_id bigint, /*账套id*/
    charge_date     varchar(10), /*收费日期*/
    payment_method  varchar(10), /*付款方式*/
    total_charge    decimal(14, 2), /*合计费用*/
    actual_charge   decimal(14, 2), /*实收费用*/
    charge_type     varchar(32), /*收费类型*/
    charge_no       varchar(12) unique, /*流水号*/
    is_del          int, /*是否删除*/
    written_person  varchar(32), /*制单人*/
    primary key (id)
);
create index IDX_charge_date on charge (charge_date);
create index IDX_charge_book on charge (account_book_id);

/*收费明细表*/
drop table if exists charge_detail;
create table charge_detail
(
    id              bigint NOT NULL AUTO_INCREMENT, /*主键*/
    charge_id       bigint, /*收费记录id*/
    charge_type     varchar(10), /*收费类型*/
    start_period    varchar(6), /*开始期间*/
    end_period      varchar(6), /*结束期间*/
    count_charge    decimal(14, 2), /*应收费用*/
    discount_charge decimal(14, 2), /*优惠费用*/
    remark          varchar(255), /*备注*/
    period VARCHAR (10), /*期间*/
    accounter_no    VARCHAR(64), /*员工编码*/
    is_del          int, /*是否删除*/
    primary key (id)
);
create index IDX_chargedetail_accounter on charge_detail (accounter_no);
create index IDX_chargedetail_period on charge_detail (period);

/*收费设置表*/
drop table if exists charge_scale;
create table charge_scale
(
    id              bigint NOT NULL AUTO_INCREMENT, /*主键*/
    account_book_id bigint, /*账套id*/
    charge_balance  decimal(14, 2), /*收费余额*/
    charge_method   varchar(8), /*收费周期（年付/月付）*/
    monthly_charge  decimal(14, 2), /*月记账费*/
    monthly_period  varchar(8), /*月记账费截止期间*/
    annual_charge   decimal(14, 2), /*年账本费*/
    annual_period   varchar(8), /*年账本费截止期间*/
    start_period    varchar(8), /*服务起始月份*/
    primary key (id)
);
create index IDX_chargescale_book on charge_scale (account_book_id);
create index IDX_chargescale_method on charge_scale (charge_method);

/*报税表*/
drop table if exists tax_claim;
create table tax_claim
(
    id              bigint NOT NULL AUTO_INCREMENT, /*主键*/
    account_book_id bigint, /*账套id*/
    period varchar (8), /*期间*/
    income          decimal(14, 2), /*收入*/
    vadded_tax      decimal(14, 2), /*增指税*/
    bus_tax         decimal(14, 2), /*营业税*/
    cul_fee         decimal(14, 2), /*文化建设费*/
    con_tax         decimal(14, 2), /*城建税*/
    edu_surtax      decimal(14, 2), /*教育费附加*/
    loc_edu_tax     decimal(14, 2), /*地方教育费附加*/
    ps_inc_tax      decimal(14, 2), /*个人所得税*/
    income_tax      decimal(14, 2), /*所得税*/
    sale_tax        decimal(14, 2), /*消费税*/
    stamp_tax       decimal(14, 2), /*印花税*/
    copy_taxes      int, /*抄税*/
    report          int, /*可清卡*/
    finished        int, /*完成*/
    primary key (id)
);
create unique index UK_tax_claim on tax_claim (account_book_id, period);
create index IDX_tax_claim_period on tax_claim (period);
create index IDX_tax_claim_book on tax_claim (account_book_id);

/*账套备注表*/
drop table if exists account_book_remark;
create table account_book_remark
(
    id              bigint NOT NULL AUTO_INCREMENT, /*主键*/
    account_book_id bigint, /*账套id*/
    written_date    TIMESTAMP default CURRENT_TIMESTAMP, /*备注时间*/
    written_person  varchar(32), /*备注人*/
    type            int, /*备注类型*/
    content         varchar(1024), /*备注内容*/
    primary key (id)
);

/*凭证模版表*/
drop table if exists voucher_template;
create table voucher_template
(
    id              bigint NOT NULL AUTO_INCREMENT, /*主键*/
    account_book_id bigint, /*账套id*/
    content         VARCHAR, /*其他信息的json字符串*/
    is_init         int    not null, /*是否预置*/
    show_name       VARCHAR(32), /*前端显示名称*/
    frequency       int, /*频率*/
    cate            int, /*分类*/
    primary key (id)
);
create index IDX_template_book on voucher_template (account_book_id);
create index IDX_template_cate on voucher_template (cate);
create index IDX_template_showname on voucher_template (show_name);

/*凭证模板预置表*/
drop table if exists voucher_template_preset;
create table voucher_template_preset
(
    id                bigint NOT NULL AUTO_INCREMENT, /*主键*/
    accounting_system varchar(255), /*会计制度  1:2013小企业会计准则  2:2007企业会计准则 3：民间非营利组织会计制度*/
    content           VARCHAR, /*其他信息的json字符串*/
    show_name         VARCHAR(32), /*前端显示名称*/
    cate              int, /*分类*/
    addtax            varchar(255), /*增值税*/
    primary key (id)
);

/*固定资产类别表*/
drop table if exists fixed_assets_category;
create table fixed_assets_category
(
    id                   bigint      NOT NULL AUTO_INCREMENT, /*主键*/
    account_book_id      bigint, /*帐套id*/
    pno                  VARCHAR(32), /*父类别编码*/
    no                   VARCHAR(32) not null, /*编码*/
    name                 VARCHAR(128), /*名称*/
    use_month            int, /*使用期限（月）*/
    old_value_rate       decimal(14, 2), /*残值率*/
    depreciate_subject   varchar(128), /*折旧科目*/
    fee_subject          varchar(128), /*费用科目*/
    depreciate_long_text varchar(255) comment '折旧科目longText',
    fee_long_text        varchar(255) comment '费用科目longText',
    method               int, /*折旧方法：1、年限平均；2、双倍余额递减；3、不折旧*/
    is_init              int         not null, /*是否预置*/
    asset_type           int default 1 comment '资产类型 1:固定资产；2:无形资产；3:长期待摊费用',
    primary key (id)
);
create index IDX_fixedassetscategory_book on fixed_assets_category (account_book_id);

/*固定资产类别预置表*/
drop table if exists fixed_assets_category_preset;
create table fixed_assets_category_preset
(
    id                   bigint      NOT NULL AUTO_INCREMENT, /*主键*/
    accounting_system    varchar(255), /*会计制度  1:2013小企业会计准则  2:2007企业会计准则 3：民间非营利组织会计制度*/
    no                   VARCHAR(32) not null, /*编码*/
    name                 VARCHAR(128), /*名称*/
    use_month            int, /*使用期限（月）*/
    old_value_rate       decimal(14, 2), /*残值率*/
    method               int, /*折旧方法：1、年限平均；2、双倍余额递减；3、不折旧*/
    depreciate_long_text varchar(255) comment '折旧科目longText',
    fee_long_text        varchar(255) comment '费用科目longText',
    is_init              int         not null, /*是否预置*/
    asset_type           int default 1 comment '资产类型 1:固定资产；2:无形资产；3:长期待摊费用',
    primary key (id)
);

/*增值税票表*/
drop table if exists invoice;
CREATE TABLE invoice
(
    id                      BIGINT      NOT NULL AUTO_INCREMENT,
    account_book_id         bigint, /*帐套*/
    voucher_id              bigint, /*凭证id*/
    voucher_template_id     bigint, /* 凭证模板id */
    invoice_type            int, /*发票类型*/
    create_type             int, /*开票方式（0税控器，1税务代开）*/
    invoice_date            VARCHAR(32), /*发票日期*/
    period varchar (8), /* 期间 */
    is_measure              int, /*是否进行过认证测算*/
    invoice_no              VARCHAR(32) not null, /*发票编号*/
    provider                VARCHAR(255), /*供应商*/
    customer                VARCHAR(255), /*客户*/
    money_amounts           decimal(14, 2), /*总金额*/
    tax_amounts             decimal(14, 2), /*总税额*/
    auth_date               VARCHAR(32), /*认证期间*/
    expire_date             VARCHAR(32), /*到期日期*/
    tax_code                VARCHAR(32), /*客户或供应商纳税人识别号*/
    invoice_code            VARCHAR(32), /*发票代码*/
    status                  int, /*发票状态*/
    url                     VARCHAR(255), /*发票图片链接*/
    pdf_url                 VARCHAR(255), /*发票pdf链接*/
    type                    int, /*发票种类*/
    bank_no                 VARCHAR(255), /*开户行及账号*/
    remark                  VARCHAR(255), /* 备注 */
    business_type           int, /* 业务类型 */
    department              int, /* 部门 0：管理部门 1：销售部门 2：研发部门 3：其他部门 */
    is_provider_or_customer int, /*0：客户（销售发票）,1：供应商(采购发票)*/
    primary key (id)
);
create index IDX_invoice_book on invoice (account_book_id);
create index IDX_invoice_period on invoice (period);
create index IDX_invoice_voucher on invoice (voucher_id);

/*增值税票明细表*/
drop table if exists invoice_detail;
CREATE TABLE invoice_detail
(
    id              BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id bigint, /*帐套*/
    invoice_id      bigint, /* 发票id */
    product_code    VARCHAR(255), /* 商品编码 */
    product_name    VARCHAR(255), /* 商品名称 */
    number          decimal(16, 4), /* 数量 */
    unit_price      decimal(14, 4), /* 单价 */
    money_amount    decimal(14, 2), /* 金额 */
    tax_rate        decimal(14, 2), /* 税率 */
    tax_amount      decimal(14, 2), /* 税额 */
    specification   VARCHAR(255), /* 规格 */
    remark          VARCHAR(255), /* 备注 */
    unit            VARCHAR(32), /* 单位 */
    primary key (id)
);
create index IDX_invoicedetail_book on invoice_detail (account_book_id);
create index IDX_invoicedetail_invoice on invoice_detail (invoice_id);

/*日记账表*/
drop table if exists journal;
CREATE TABLE journal
(
    id                    BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id       bigint, /* 帐套id */
    voucher_id            bigint, /* 凭证id */
    voucher_template_id   bigint, /* 凭证模板id */
    journal_date          VARCHAR(32), /* 日期 */
    summary               VARCHAR(255), /* 摘要 */
    income                decimal(14, 2), /* 收入 */
    pay                   decimal(14, 2), /* 支出 */
    period varchar (8), /* 期间 */
    journal_type          int, /* 类型 */
    income_pay_type       int, /* 收支类型 */
    opposite_account_name VARCHAR(64), /* 对方户名 */
    opening_bank          VARCHAR(64), /* 开户行 */
    status                int, /* 状态 */
    receipt_no            varchar(64), /* 回单编号（流水号） */
    receipt_url           varchar(255), /* 回单地址 */
    primary key (id)
);
create index IDX_journal_book on journal (account_book_id);
create index IDX_journal_period on journal (period);
create index IDX_journal_voucher on journal (voucher_id);

drop table if exists other_invoice;
CREATE TABLE other_invoice
(
    id                  BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id     bigint, /* 帐套id */
    voucher_id          bigint, /* 凭证id */
    voucher_template_id bigint, /* 凭证模板id */
    written_date        VARCHAR(32), /* 日期 */
    money               decimal(14, 2), /* 金额 */
    accessory_no        int, /* 附件数 */
    written_person      VARCHAR(32), /* 填表人 */
    period VARCHAR (8), /* 期间 */
    invoice_type        int, /* 票据类型 0：其他 1：停车费 2：市内交通 3：出租车 4：长途车票 5：高铁、火车 6：航空服务 7：高速公路 */
    department          int, /* 部门 0：管理部门 1：销售部门 2：研发部门 3：其他部门 */
    remark              VARCHAR(32), /* 备注 */
    primary key (id)
);
/*其他票据表*/
create index IDX_otherinvoice_book on other_invoice (account_book_id);
create index IDX_otherinvoice_period on other_invoice (period);
create index IDX_otherinvoice_voucher on other_invoice (voucher_id);

/*报表接口参数加密表*/
drop table if exists information_encryption;
CREATE TABLE information_encryption
(
    id           BIGINT NOT NULL AUTO_INCREMENT,
    book_code    varchar(64), /*账套编码*/
    book_id      bigint, /*账套id*/
    period varchar (64), /*期间*/
    token        varchar(64), /*token*/
    operate_time TIMESTAMP default CURRENT_TIMESTAMP, /*操作时间*/
    primary key (id)
);

/*绩效表*/
drop table if exists performance;
CREATE TABLE performance
(
    id                          BIGINT NOT NULL AUTO_INCREMENT,
    period varchar (8), /*期间*/
    account_no                  varchar(64), /*员工编码*/
    account_name                varchar(64), /*区域+记账人姓名*/
    small_should_finished_num   bigint, /*员工所建小规模的应完成账套数量*/
    small_have_finished_num     bigint, /*员工所建小规模的已完成账套数量*/
    small_not_finished_num      bigint, /*员工所建小规模的未完成账套数量*/
    general_should_finished_num bigint, /*员工所建一般纳税人的应完成账套数量*/
    general_have_finished_num   bigint, /*员工所建一般纳税人的已完成账套数量*/
    general_not_finished_num    bigint, /*员工所建一般纳税人的未完成账套数量*/
    total_num                   bigint, /*账套总数*/
    voucher_num                 bigint, /*已完成凭证数*/
    receive_accounts            varchar(64), /*应收账款*/
    month_account               varchar(64), /*单月回款*/
    total_account               varchar(64), /*总回款*/
    month_get                   varchar(64), /*本月提成*/
    primary key (id)
);
create index IDX_performance_period on performance (period);

/*增值税纳税申报表*/
drop table if exists vat_declaration;
CREATE TABLE vat_declaration
(
    id              BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id bigint, /* 帐套id */
    start_period    varchar(8), /*start期间*/
    end_period      varchar(8), /*end期间*/
    row             int, /*栏次*/
    goods           decimal(14, 2), /*货物及劳务*/
    service         decimal(14, 2), /*服务、不动产和无形资产*/
    primary key (id)
);
create unique index UK_vatdeclaration_book on vat_declaration (account_book_id, start_period, end_period, row);

/*减税项目表*/
drop table if exists vat_cut_declaration;
CREATE TABLE vat_cut_declaration
(
    id              BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id bigint, /* 帐套id */
    start_period    varchar(8), /*start期间*/
    end_period      varchar(8), /*end期间*/
    no              int, /*序号*/
    type            int, /*减免类型*/
    init_balance    decimal(14, 2) default 0, /*期初余额*/
    balance         decimal(14, 2), /*发生额*/
    deduct_balance  decimal(14, 2) default 0, /*调减额*/
    actual_balance  decimal(14, 2), /*实际抵减额*/
    primary key (id)
);
create unique index UK_vatcutdeclaration_book on vat_cut_declaration (account_book_id, start_period, end_period, type);

/*销售明细表*/
drop table if exists sales_detail;
CREATE TABLE sales_detail
(
    id                   BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id      bigint, /* 帐套id */
    start_period         varchar(8), /*start期间*/
    end_period           varchar(8), /*end期间*/
    no                   varchar(8), /*栏次*/
    invoice_sales        decimal(14, 2), /*开具增值税专用发票的销售额*/
    invoice_tax          decimal(14, 2), /*开具增值税专用发票的销项（应纳）税额*/
    other_invoice_sales  decimal(14, 2), /*开具其他发票的销售额*/
    other_invoice_tax    decimal(14, 2), /*开具其他发票的销项（应纳）税额*/
    no_invoice_sales     decimal(14, 2), /*未开具发票的销售额*/
    no_invoice_tax       decimal(14, 2), /*未开具发票的销项（应纳）税额*/
    tax_inspection_sales decimal(14, 2), /*纳税检查调整的销售额*/
    tax_inspection_tax   decimal(14, 2), /*纳税检查调整的销项（应纳）税额*/
    deduct_balance       decimal(14, 2), /*本期实际扣除金额*/
    after_deduct_tax     decimal(14, 2), /*扣除后销项（应纳）税额*/
    primary key (id)
);
create unique index UK_salesdetail_book on sales_detail (account_book_id, start_period, end_period, no);

/*本期进项税额明细表*/
drop table if exists input_tax_details;
CREATE TABLE input_tax_details
(
    id              BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id bigint, /*帐套id*/
    period varchar (8), /*期间*/
    row             varchar(8), /*栏次*/
    quantity        int, /*份数*/
    amount          decimal(14, 2), /*金额*/
    tax             decimal(14, 2), /*税额*/
    primary key (id)
);
create unique index UK_input_tax_details_book on input_tax_details (account_book_id, period, row);

/*工资表*/
drop table if exists pay_sheet;
CREATE TABLE pay_sheet
(
    id                           BIGINT NOT NULL AUTO_INCREMENT,
    type                         int(4) NOT NULL default 0, /*工资-工资类型-0 正常工资，1 劳务工资*/
    account_book_id              bigint, /*帐套id*/
    salary                       decimal(14, 2), /*收入额*/
    dim_employee_id              bigint, /*员工辅助核算id*/
    voucher_id                   bigint, /*凭证id*/
    voucher_template_id          bigint, /*凭证模板id*/
    personal_pension             decimal(14, 2), /*养老保险费（个人）*/
    personal_medical             decimal(14, 2), /*医疗保险费（个人）*/
    personal_unemployment        decimal(14, 2), /*失业保险费（个人）*/
    personal_housing_fund        decimal(14, 2), /*住房公积金（个人）*/
    child_education_fee          decimal(14, 2), /*子女教育费*/
    continue_education_fee       decimal(14, 2), /*继续教育费*/
    mortgage_interest            decimal(14, 2), /*房贷利息*/
    rent                         decimal(14, 2), /*住房租金*/
    raise_elderly                decimal(14, 2), /*赡养老人*/
    company_pension              decimal(14, 2), /*养老保险费（企业）*/
    company_medical              decimal(14, 2), /*医疗保险费（企业）*/
    company_unemployment         decimal(14, 2), /*失业保险费（企业）*/
    company_housing_fund         decimal(14, 2), /*住房公积金（企业）*/
    injury_insurance             decimal(14, 2), /*工伤保险*/
    maternity_insurance          decimal(14, 2), /*生育保险*/
    taxable_income               decimal(14, 2), /*应纳税所得额*/
    personal_income_tax          decimal(14, 2), /*个人所得税*/
    period varchar (8), /*期间*/
    tax_free_income              decimal(14, 2) DEFAULT 0, /*免税所得*/
    other_deduction              decimal(14, 2) DEFAULT 0, /*其他扣除*/
    remark                       varchar(64), /*备注*/
    base_decution                decimal(14, 2) DEFAULT 5000, /*基础扣除*/
    annual_bonus                 decimal(14, 2) DEFAULT 0, /*年终奖*/
    annual_bonus_tax             decimal(14, 2) DEFAULT 0, /*年终奖税额*/
    is_six                       int(4) NOT NULL default 0, /*减除费用是否按六万扣除 0：否 1：是*/
    total_salary                 decimal(14, 2) DEFAULT 0 COMMENT '累计收入额',
    total_special_deduction      decimal(14, 2) DEFAULT 0 COMMENT '累计专项扣除',
    total_child_education_fee    decimal(14, 2) DEFAULT 0 COMMENT '累计子女教育费',
    total_continue_education_fee decimal(14, 2) DEFAULT 0 COMMENT '累计继续教育费',
    total_mortgage_interest      decimal(14, 2) DEFAULT 0 COMMENT '累计房贷利息',
    total_rent                   decimal(14, 2) DEFAULT 0 COMMENT '累计住房租金',
    total_raise_elderly          decimal(14, 2) DEFAULT 0 COMMENT '累计赡养老人',
    total_tax_free_income        decimal(14, 2) DEFAULT 0 COMMENT '累计免税所得',
    total_other_deduction        decimal(14, 2) DEFAULT 0 COMMENT '累计其他扣除',
    total_base_decution          decimal(14, 2) DEFAULT 0 COMMENT '累计基础扣除',
    payable                      decimal(14, 2) DEFAULT 0 COMMENT '应纳税额',
    tax_rate                     decimal(14, 2) DEFAULT 0 COMMENT '税率',
    quick_deduction              decimal(14, 2) DEFAULT 0 COMMENT '速算扣除数',
    tax_paid                     decimal(14, 2) DEFAULT 0 COMMENT '已缴税额',
    baby_care                    decimal(14, 2) DEFAULT 0 COMMENT '婴幼儿照护',
    total_baby_care              decimal(14, 2) DEFAULT 0 COMMENT '累计婴幼儿照护',
    personal_fund                decimal(14, 2) DEFAULT 0 COMMENT '个人养老金',
    total_personal_fund          decimal(14, 2) DEFAULT 0 COMMENT '累计个人养老金',
    is_base_deduction            int(4) NOT NULL default 1, /*是否扣除减除费用 0：否 1：是*/
    primary key (id)
);
create unique index UK_paysheet_book on pay_sheet (account_book_id, period, dim_employee_id);
create index IDX_paysheet_period on pay_sheet (period);
create index IDX_paysheet_book on pay_sheet (account_book_id);
create index IDX_paysheet_employee on pay_sheet (dim_employee_id);

DROP TABLE IF EXISTS `company_declare`;
CREATE TABLE `company_declare`
(
    `id`                          int(11) NOT NULL AUTO_INCREMENT,
    `company_id`                  int(11) DEFAULT NULL,
    `tax_no`                      varchar(20)    NOT NULL,
    `name`                        varchar(50)    NOT NULL,
    `declare_date`                varchar(6)     NOT NULL,
    `declare_number`              int(4) NOT NULL,
    `salary_total`                decimal(10, 2) NOT NULL,
    `personal_tax_total`          decimal(10, 2) NOT NULL,
    `state`                       int(4) NOT NULL,
    `pic_name`                    varchar(255)   DEFAULT NULL,
    `area_id`                     int(11) NOT NULL,
    `add_time`                    datetime       NOT NULL,
    `share_num`                   int(4) DEFAULT '0',
    `state1`                      int(4) DEFAULT NULL,
    `ai_user_id`                  int(4) DEFAULT NULL,
    `update_time`                 datetime       DEFAULT NULL,
    `customer_no`                 varchar(20)    DEFAULT NULL,
    `declare_name`                varchar(50)    DEFAULT NULL,
    `declare_password`            varchar(32)    DEFAULT NULL,
    `assistant_name`              varchar(10)    DEFAULT NULL,
    `assistant_id`                int(4) DEFAULT NULL,
    `ai_mac_id`                   varchar(50)    DEFAULT NULL,
    `ai_mac_ip`                   varchar(50)    DEFAULT NULL,
    `confirm_time`                datetime       DEFAULT NULL,
    `confirm_immediate_exec`      int(4) DEFAULT NULL,
    `ai_exec_time`                datetime       DEFAULT NULL,
    `from_id`                     int(11) DEFAULT NULL,
    `is_declare_success`          int(11) DEFAULT NULL,
    `anew_revoked_state`          int(11) DEFAULT NULL,
    `check_newdata_state`         int(11) DEFAULT NULL,
    `ai_mac_username`             varchar(50)    DEFAULT NULL,
    `deal_num`                    int(11) DEFAULT '0' COMMENT '处理次数',
    `employee_default_mobile`     varchar(20)    DEFAULT NULL COMMENT '雇员默认手机号',
    `remark_state1`               varchar(255)   DEFAULT NULL COMMENT '状态1备注',
    `person_baosong_num`          int(11) DEFAULT '0' COMMENT '人员报送次数（每次作废或重新申报时，重新计算）',
    `person_baosong_total_num`    int(11) DEFAULT '0' COMMENT '人员报送总次数',
    `report_baosong_num`          int(11) DEFAULT '0' COMMENT '申报表报送次数（每次作废或重新申报时，重新计算）',
    `report_baosong_total_num`    int(11) DEFAULT '0' COMMENT '申报表报送总次数',
    `ai_exec_order_id`            int(11) DEFAULT '0' COMMENT 'ai执行排序id（从大到小执行）',
    `person_baosong_pic`          varchar(255)   DEFAULT NULL COMMENT '人员报送截图',
    `person_baosong_time`         datetime       DEFAULT NULL COMMENT '人员报送时间',
    `report_baosong_pic`          varchar(255)   DEFAULT NULL COMMENT '申报表报送截图',
    `report_baosong_time`         datetime       DEFAULT NULL COMMENT '报表报送时间',
    `ai_salary_zhengcheng_is_add` int(11) DEFAULT NULL COMMENT 'ai是填写过正常工资，再一个征期添加过一次，以后每次都要添加，不管有没有数据',
    `ai_salary_zhengcheng_pic`    varchar(255)   DEFAULT NULL COMMENT 'ai填写正常工资后截图',
    `ai_salary_laowu_is_add`      int(11) DEFAULT NULL COMMENT 'ai是填写过劳务工资，再一个征期添加过一次，以后每次都要添加，不管有没有数据',
    `ai_salary_laowu_pic`         varchar(255)   DEFAULT NULL COMMENT 'ai填写劳务工资后截图',
    `ai_salary_qnycxsr_is_add`    int(11) DEFAULT NULL COMMENT 'ai是填写过全年一次性收入，再一个征期添加过一次，以后每次都要添加，不管有没有数据',
    `ai_salary_qnycxsr_pic`       varchar(255)   DEFAULT NULL COMMENT 'ai是填写过全年一次性收入截图',
    `ai_is_dikou`                 int(11) DEFAULT '0' COMMENT '是否抵扣 0不是，1是',
    `ai_salary_skjs_excel`        varchar(255)   DEFAULT NULL,
    `ai_deal_state`               int(11) DEFAULT NULL COMMENT 'ai状态（已确认/处理中/申报成功）',
    `ai_deal_state1`              int(11) DEFAULT NULL,
    `tax_state`                   int(11) DEFAULT NULL COMMENT '缴税状态 无需缴税，需要缴税等待审核，等待扣税，扣税成功，扣税失败',
    `tax_amount`                  decimal(10, 2) DEFAULT NULL COMMENT '税额',
    `cancel_anew_state`           int(11) DEFAULT NULL COMMENT '作废/更正状态',
    `error_state`                 int(11) DEFAULT NULL COMMENT '错误状态',
    `tax_confirm_date`            datetime       DEFAULT NULL COMMENT '税助理确认时间',
    PRIMARY KEY (`id`)
);

DROP TABLE IF EXISTS `employee_declare`;
CREATE TABLE `employee_declare`
(
    `id`                                         int(11) NOT NULL AUTO_INCREMENT,
    `name`                                       varchar(10)    NOT NULL,
    `idcard`                                     varchar(18)    NOT NULL,
    `salary`                                     decimal(10, 2) NOT NULL,
    `endowment`                                  decimal(10, 2) DEFAULT NULL,
    `medical`                                    decimal(10, 2) DEFAULT NULL,
    `unemployment`                               decimal(10, 2) DEFAULT NULL,
    `reserved`                                   decimal(10, 2) DEFAULT NULL,
    `communication`                              decimal(10, 2) DEFAULT NULL,
    `other`                                      decimal(10, 2) DEFAULT NULL,
    `type`                                       int(4) NOT NULL,
    `declare_id`                                 int(11) NOT NULL,
    `add_time`                                   datetime       NOT NULL,
    `update_time`                                datetime       DEFAULT NULL,
    `declare_date`                               varchar(6)     NOT NULL,
    `tax_deductible`                             decimal(10, 2) DEFAULT '0.00',
    `person_num`                                 int(11) DEFAULT NULL COMMENT '人员-人员序号（只为了在助理客户端进行人员排序）',
    `person_gonghao`                             int(11) DEFAULT NULL COMMENT '人员-工号，用来排序',
    `person_zhengzhao_leixing`                   varchar(50)    DEFAULT NULL COMMENT '人员-证照类型 \r\n证照类型填写范围\r\n居民身份证\r\n军官证\r\n士兵证\r\n武警警官证\r\n港澳居民来往内地通行证\r\n台湾居民来往大陆通行证\r\n中国护照\r\n外国护照\r\n香港永久性居民身份证\r\n台湾身份证\r\n澳门特别行政区永久性居民身份证\r\n外国人永久居留身份证（外国人永久居留证）\r\n',
    `person_guoji`                               varchar(50)    DEFAULT NULL COMMENT '人员-国籍（地区）\r\n国籍（地区）\r\n阿尔巴尼亚\r\n阿尔及利亚\r\n阿富汗\r\n阿根廷\r\n阿联酋\r\n阿鲁巴\r\n阿曼\r\n阿塞拜疆\r\n埃及\r\n埃塞俄比亚\r\n爱尔兰\r\n爱沙尼亚\r\n安道尔\r\n安哥拉\r\n安圭拉\r\n安提瓜和巴布达\r\n奥地利\r\n澳大利亚\r\n中国澳门\r\n巴巴多斯\r\n巴布亚新几内亚\r\n巴哈马\r\n巴基斯坦\r\n巴拉圭\r\n巴勒斯坦\r\n巴林\r\n巴拿马\r\n巴西\r\n白俄罗斯\r\n百慕大\r\n保加利亚\r\n北马里亚纳\r\n贝宁\r\n比利时\r\n冰岛\r\n波多黎各\r\n波黑\r\n波兰\r\n玻利维亚\r\n伯利兹\r\n博茨瓦纳\r\n不丹\r\n布基纳法索\r\n布隆迪\r\n布维岛\r\n朝鲜\r\n赤道几内亚\r\n丹麦\r\n德国\r\n东帝汶\r\n多哥\r\n多米尼加\r\n多米尼克\r\n俄罗斯联邦\r\n厄瓜多尔\r\n厄立特里亚\r\n法国\r\n法罗群岛\r\n法属波利尼西亚\r\n法属圭亚那\r\n法属南部领地\r\n梵蒂冈\r\n菲律宾\r\n斐济\r\n芬兰\r\n佛得角\r\n福克兰群岛（马尔维纳斯）\r\n冈比亚\r\n刚果（布）\r\n刚果（金）\r\n哥伦比亚\r\n哥斯达黎加\r\n格林纳达\r\n格陵兰\r\n格鲁吉亚\r\n古巴\r\n瓜德罗普\r\n关岛\r\n圭亚那\r\n哈萨克斯坦\r\n海地\r\n韩国\r\n荷兰\r\n荷属安的列斯\r\n赫德岛和麦克唐纳岛\r\n黑山\r\n洪都拉斯\r\n基里巴斯\r\n吉布提\r\n吉尔吉斯斯坦\r\n几内亚\r\n几内亚比绍\r\n加拿大\r\n加纳\r\n加蓬\r\n柬埔寨\r\n捷克\r\n津巴布韦\r\n喀麦隆\r\n卡塔尔\r\n开曼群岛\r\n科科斯（基林）群岛\r\n科摩罗\r\n科特迪瓦\r\n科威特\r\n克罗地亚\r\n肯尼亚\r\n库克群岛\r\n拉脱维亚\r\n莱索托\r\n老挝\r\n黎巴嫩\r\n立陶宛\r\n利比里亚\r\n利比亚\r\n列支敦士登\r\n留尼汪\r\n卢森堡\r\n卢旺达\r\n罗马尼亚\r\n马达加斯加\r\n马尔代夫\r\n马耳他\r\n马拉维\r\n马来西亚\r\n马里\r\n马绍尔群岛\r\n马提尼克\r\n马约特\r\n毛里求斯\r\n毛里塔尼亚\r\n美国\r\n美国本土外小岛屿\r\n美属萨摩亚\r\n美属维尔京群岛\r\n蒙古\r\n蒙特塞拉特\r\n孟加拉国\r\n秘鲁\r\n密克罗尼西亚联邦\r\n缅甸\r\n摩尔多瓦\r\n摩洛哥\r\n摩纳哥\r\n莫桑比克\r\n墨西哥\r\n纳米比亚\r\n南非\r\n南极洲\r\n南乔治亚岛和南桑德韦奇岛\r\n南斯拉夫\r\n瑙鲁\r\n尼泊尔\r\n尼加拉瓜\r\n尼日尔\r\n尼日利亚\r\n纽埃\r\n挪威\r\n诺福克岛\r\n帕劳\r\n皮特凯恩\r\n葡萄牙\r\n前南马其顿\r\n日本\r\n瑞典\r\n瑞士\r\n萨尔瓦多\r\n萨摩亚\r\n塞尔维亚\r\n塞拉利昂\r\n塞内加尔\r\n塞浦路斯\r\n塞舌尔\r\n沙特阿拉伯\r\n圣诞岛\r\n圣多美和普林西比\r\n圣赫',
    `person_sex`                                 varchar(10)    DEFAULT NULL COMMENT '人员-性别 男，女',
    `person_chusheng_nianyue`                    varchar(20)    DEFAULT NULL COMMENT '人员-出生年月',
    `person_state`                               varchar(20)    DEFAULT NULL COMMENT '人员-人员状态 \r\n正常\r\n非正常\r\n',
    `person_is_guyuan`                           varchar(10)    DEFAULT NULL COMMENT '人员-是否雇员 \r\n是\r\n否',
    `person_mobile`                              varchar(20)    DEFAULT NULL COMMENT '人员-手机号（请输入11位手机号码。必填）',
    `person_is_canji`                            varchar(10)    DEFAULT NULL COMMENT '人员-是否残疾 是，否',
    `person_is_lieshu`                           varchar(10)    DEFAULT NULL COMMENT '人员-是否列属 是，否',
    `person_is_gulao`                            varchar(10)    DEFAULT NULL COMMENT '人员-是否孤老 是，否',
    `person_canji_zhenghao`                      varchar(50)    DEFAULT NULL COMMENT '人员-残疾证号',
    `person_lieshu_zhenghao`                     varchar(50)    DEFAULT NULL COMMENT '人员-烈属证号',
    `person_renzhi_shougu_riqi`                  datetime       DEFAULT NULL COMMENT '人员-任职受雇日期（请严格按照如下格式填写：\n2015-12-12）',
    `person_lizhi_riqi`                          datetime       DEFAULT NULL COMMENT '人员-离职日期（请严格按照如下格式填写：\n2015-12-12）',
    `person_email`                               varchar(100)   DEFAULT NULL COMMENT '人员-电子邮箱',
    `person_xueli`                               varchar(50)    DEFAULT NULL COMMENT '人员-学历 研究生，大学本科，大学本科以下',
    `person_zhiye`                               varchar(100)   DEFAULT NULL COMMENT '人员-职业',
    `person_kaihu_yinhang`                       varchar(100)   DEFAULT NULL COMMENT '人员-开户银行',
    `person_yinhang_zhanghu`                     varchar(100)   DEFAULT NULL COMMENT '人员-银行账户',
    `person_is_teding_hangye`                    varchar(10)    DEFAULT NULL COMMENT '人员-是否特定行业 是，否',
    `person_is_gudong_touzizhe`                  varchar(10)    DEFAULT NULL COMMENT '人员-是否股东、投资者 是，否',
    `person_geren_guben_touzie`                  decimal(10, 2) DEFAULT NULL COMMENT '人员-个人股本（投资）额',
    `person_huji_suozai_shengfen`                varchar(50)    DEFAULT NULL COMMENT '人员-户籍所在省份',
    `person_huji_suozai_chengshi`                varchar(50)    DEFAULT NULL COMMENT '人员-户籍所在城市',
    `person_huji_suozai_quxian`                  varchar(50)    DEFAULT NULL COMMENT '人员-户籍所在区（县）',
    `person_huji_suozai_xiangxidizhi`            varchar(255)   DEFAULT NULL COMMENT '人员-户籍所在详细地址',
    `person_juzhu_shengfen`                      varchar(50)    DEFAULT NULL COMMENT '人员-居住省份',
    `person_juzhu_chengshi`                      varchar(50)    DEFAULT NULL COMMENT '人员-居住城市',
    `person_juzhu_suozai_quxian`                 varchar(50)    DEFAULT NULL COMMENT '人员-居住所在区（县）',
    `person_juzhu_xiangxidizhi`                  varchar(255)   DEFAULT NULL COMMENT '人员-居住详细地址',
    `person_remark`                              varchar(255)   DEFAULT NULL COMMENT '人员-备注',
    `person_is_jingwai_renyuan`                  varchar(10)    DEFAULT NULL COMMENT '人员-是否境外人员 是，否',
    `person_xingming_zhongwen`                   varchar(50)    DEFAULT NULL COMMENT '人员-姓名（中文）（身份证件类型为外国护照、外国人永久居留身份证（外国人永久居留证）证件时，可填）',
    `person_jingnei_youwu_zhusuo`                varchar(255)   DEFAULT NULL COMMENT '人员-境内有无住所（境外人员，境内有无住所是必填项） 有，无',
    `person_jingwai_nashuire_shibiehao`          varchar(100)   DEFAULT NULL COMMENT '人员-境外纳税人识别号',
    `person_chushengdi`                          varchar(100)   DEFAULT NULL COMMENT '人员-出生地',
    `person_shouci_rujing_shijian`               datetime       DEFAULT NULL COMMENT '人员-首次入境时间（请严格按照如下格式填写：\n2015-12-12）',
    `person_laihu_shijian`                       datetime       DEFAULT NULL COMMENT '人员-来华时间（境内无住所个人，来华时间是必填项\n格式按照如下格式填写：2015-12-12）',
    `person_renzhi_qixian`                       varchar(20)    DEFAULT NULL COMMENT '人员-任职期限',
    `person_yuji_lijing_shijian`                 datetime       DEFAULT NULL COMMENT '人员-预计离境时间（请严格按照如下格式填写：\n2015-12-12）',
    `person_yuji_lijing_didian`                  varchar(100)   DEFAULT NULL COMMENT '人员-预计离境地点',
    `person_jingnei_zhiwu`                       varchar(50)    DEFAULT NULL COMMENT '人员-境内职务',
    `person_jingwai_zhiwu`                       varchar(50)    DEFAULT NULL COMMENT '人员-境外职务',
    `person_zhifudi`                             varchar(100)   DEFAULT NULL COMMENT '人员-支付地（境内无住所个人，支付地是必填项）',
    `person_jingwai_zhifudi`                     varchar(100)   DEFAULT NULL COMMENT '人员-境外支付地（国别/地区）',
    `salary_shuikuan_fudan_fangshi`              varchar(20)    DEFAULT NULL COMMENT '工资-税款负担方式 自行负担，雇主全额负担',
    `salary_mianshui_suode`                      decimal(10, 2) DEFAULT NULL COMMENT '工资-免税所得',
    `salary_nianjin`                             decimal(10, 2) DEFAULT NULL COMMENT '工资-年金 （只有正常工资有此列，劳务报酬没有）',
    `salary_shangye_jiankang_baoxian`            decimal(10, 2) DEFAULT NULL COMMENT '工资-商业健康保险费',
    `salary_shunyan_yanglao_baoxianfei`          decimal(10, 2) DEFAULT NULL COMMENT '工资-税延养老保险费',
    `salary_jianchu_feiyong`                     decimal(10, 2) DEFAULT NULL COMMENT '工资-减除费用（远洋运输业减除费用为4800，采掘业、远洋捕捞业减除费用为3500元\n）（只有正常工资有此列，劳务报酬没有）',
    `salary_shiji_juanzenge`                     decimal(10, 2) DEFAULT NULL COMMENT '工资-实际捐赠额',
    `salary_yunxu_liezhide_juanzeng_bili`        decimal(10, 2) DEFAULT NULL COMMENT '工资-允许列支的捐赠比例',
    `salary_zhunyu_kouchude_juanzenge`           decimal(10, 2) DEFAULT NULL COMMENT '工资-准予扣除的捐赠额（允许列支的捐赠比例为“混合”时，需填写；允许扣除的捐赠比例为“0.3或1.0”时，不需填写。）',
    `salary_jianmian_shuie`                      decimal(10, 2) DEFAULT NULL COMMENT '工资-减免税额',
    `salary_yikoujiao_shuie`                     decimal(10, 2) DEFAULT NULL COMMENT '工资-已扣缴税额',
    `salary_remark`                              varchar(255)   DEFAULT NULL COMMENT '工资-备注 根据政策要求，你已填写[其他扣除]金额：30.00，请在[备注]栏目填写具体扣除项目名称！',
    `salary_shuie`                               decimal(10, 2) DEFAULT NULL COMMENT '税额-自动计算或者助理填写的',
    `zrr_xingming`                               varchar(10)    DEFAULT NULL COMMENT '自然人-姓名',
    `zrr_zhengzhaoleixing`                       varchar(10)    DEFAULT NULL COMMENT '自然人-证照类型',
    `zrr_zhengzhaohaoma`                         varchar(18)    DEFAULT NULL COMMENT '自然人-证照号码',
    `zrr_shuikuansuoshuqi_qi`                    varchar(10)    DEFAULT NULL COMMENT '自然人-税款所属期起',
    `zrr_shuikuansuoshuqi_zhi`                   varchar(10)    DEFAULT NULL COMMENT '自然人-税款所属期止',
    `zrr_shuodexiangmu`                          varchar(10)    DEFAULT NULL COMMENT '自然人-所得项目',
    `zrr_benqi_shouru`                           decimal(10, 2) DEFAULT NULL COMMENT '自然人-本期收入',
    `zrr_benqi_feiyong`                          decimal(10, 2) DEFAULT NULL COMMENT '自然人-本期费用',
    `zrr_benqi_mianshuishouru`                   decimal(10, 2) DEFAULT NULL COMMENT '自然人-本期免税收入',
    `zrr_benqi_yanglaobaoxian`                   decimal(10, 2) DEFAULT NULL COMMENT '自然人-本期基本养老保险费',
    `zrr_benqi_yiliaobaoxian`                    decimal(10, 2) DEFAULT NULL COMMENT '自然人-本期基本医疗保险费',
    `zrr_benqi_shiyebaoxian`                     decimal(10, 2) DEFAULT NULL COMMENT '自然人-本期失业保险费',
    `zrr_benqi_zufanggongjijin`                  decimal(10, 2) DEFAULT NULL COMMENT '自然人-本期住房公积金',
    `zrr_benqi_qiyenianjin`                      decimal(10, 2) DEFAULT NULL COMMENT '自然人-本期企业(职业)年金',
    `zrr_benqi_shangyejiankangbaoxian`           decimal(10, 2) DEFAULT NULL COMMENT '自然人-本期商业健康保险费',
    `zrr_benqi_shuiyanyanglaobaoxian`            decimal(10, 2) DEFAULT NULL COMMENT '自然人-本期税延养老保险费',
    `zrr_benqi_qita_kouchu`                      decimal(10, 2) DEFAULT NULL COMMENT '自然人-本期其他扣除(其他)',
    `zrr_leiji_shourue`                          decimal(10, 2) DEFAULT NULL COMMENT '自然人-累计收入额',
    `zrr_leiji_jianchufeiyong`                   decimal(10, 2) DEFAULT NULL COMMENT '自然人-累计减除费用',
    `zrr_leiji_zhuanxiang_kouchu`                decimal(10, 2) DEFAULT NULL COMMENT '自然人-累计专项扣除',
    `zrr_leiji_zinvjiaoyu_zhichu_kouchu`         decimal(10, 2) DEFAULT NULL COMMENT '自然人-累计子女教育支出扣除',
    `zrr_leiji_shanyanglaoren_zhichu_kouchu`     decimal(10, 2) DEFAULT NULL COMMENT '自然人-累计赡养老人支出扣除',
    `zrr_leiji_jixujiaoyu_zhichu_kouchu`         decimal(10, 2) DEFAULT NULL COMMENT '自然人-累计继续教育支出扣除',
    `zrr_leiji_zhufangdaikuanlixi_zhichu_kouchu` decimal(10, 2) DEFAULT NULL COMMENT '自然人-累计住房贷款利息支出扣除',
    `zrr_leiji_zhufangzujin_zhichu_kouchu`       decimal(10, 2) DEFAULT NULL COMMENT '自然人-累计住房租金支出扣除',
    `zrr_leiji_qitakou_kouchu`                   decimal(10, 2) DEFAULT NULL COMMENT '自然人-累计其他扣除',
    `zrr_leiji_zhunyukouchudejuankuan`           decimal(10, 2) DEFAULT NULL COMMENT '自然人-累计准予扣除的捐赠',
    `zrr_leiji_yingnashuisuodee`                 decimal(10, 2) DEFAULT NULL COMMENT '自然人-累计应纳税所得额',
    `zrr_shuilv`                                 decimal(10, 2) DEFAULT NULL COMMENT '自然人-税率',
    `zrr_susuankouchushu`                        decimal(10, 2) DEFAULT NULL COMMENT '自然人-速算扣除数',
    `zrr_leiji_yingna_shuie`                     decimal(10, 2) DEFAULT NULL COMMENT '自然人-累计应纳税额',
    `zrr_leiji_jianmian_shuie`                   decimal(10, 2) DEFAULT NULL COMMENT '自然人-累计减免税额',
    `zrr_leiji_yingkouchu_shuie`                 decimal(10, 2) DEFAULT NULL COMMENT '自然人-累计应扣缴税额',
    `zrr_leiji_yiyujiao_shuie`                   decimal(10, 2) DEFAULT NULL COMMENT '自然人-累计已预缴税额',
    `zrr_leiji_yinbutui_shuie`                   decimal(10, 2) DEFAULT NULL COMMENT '自然人-累计应补(退)税额',
    `zrr_beizhu`                                 varchar(255)   DEFAULT NULL COMMENT '自然人-备注',
    PRIMARY KEY (`id`)
);

/*一般纳税人增值税纳税申报表*/
drop table if exists general_vat_declaration;
CREATE TABLE general_vat_declaration
(
    id              BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id bigint, /* 帐套id */
    period varchar (8), /*期间*/
    row             int, /*栏次*/
    data            decimal(14, 2), /*数据*/
    primary key (id)
);
create unique index UK_general_vat_declaration_book on general_vat_declaration (account_book_id, period, row);

/*所得税申报表主表*/
drop table if exists enterprise_income_tax_quarter_main;
CREATE TABLE enterprise_income_tax_quarter_main
(
    id                  BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id     bigint, /* 帐套id */
    start_period        varchar(8), /*起始期间*/
    end_period          varchar(8), /*结束期间*/
    written_date        TIMESTAMP      default CURRENT_TIMESTAMP, /*填报日期*/
    declare_date        TIMESTAMP      default CURRENT_TIMESTAMP, /*申报日期*/
    pay_type            int            default 0, /*预缴方式 0：按照实际利润额预缴 1：按照上一纳税年度应纳税得额平均额预缴 2：按照税务机关确定的其他方法预缴*/
    company_type        int            default 0, /*企业类型 0：一般企业 1：跨地区经营汇总纳税企业总机构 2：跨地区经营汇总纳税企业分支机构*/
    is_tech_small       int            default 0, /*科技型中小企业 默认否*/
    is_tech_high        int            default 0, /*高新技术企业 默认否*/
    is_tech_share       int            default 0, /*技术入股递延纳税事项 默认否*/
    start_person_num    int, /*季初从业人数*/
    end_person_num      int, /*季末从业人数*/
    start_assets        decimal(14, 2), /*季初资产总额（万元）*/
    end_assets          decimal(14, 2), /*季末资产总额（万元）*/
    start_person_num1   int, /*1季度季初从业人数*/
    end_person_num1     int, /*1季度季末从业人数*/
    start_assets1       decimal(14, 2), /*1季度季初资产总额（万元）*/
    end_assets1         decimal(14, 2), /*1季度季末资产总额（万元）*/
    start_person_num2   int, /*2季度季初从业人数*/
    end_person_num2     int, /*2季度季末从业人数*/
    start_assets2       decimal(14, 2), /*2季度季初资产总额（万元）*/
    end_assets2         decimal(14, 2), /*2季度季末资产总额（万元）*/
    start_person_num3   int, /*3季度季初从业人数*/
    end_person_num3     int, /*3季度季末从业人数*/
    start_assets3       decimal(14, 2), /*3季度季初资产总额（万元）*/
    end_assets3         decimal(14, 2), /*3季度季末资产总额（万元）*/
    start_person_num4   int, /*4季度季初从业人数*/
    end_person_num4     int, /*4季度季末从业人数*/
    start_assets4       decimal(14, 2), /*4季度季初资产总额（万元）*/
    end_assets4         decimal(14, 2), /*4季度季末资产总额（万元）*/
    is_state_limit      int            default 0, /*国家限制或禁止行业 默认否*/
    is_small_low_profit int            default 1, /*小型微利企业 默认是*/
    previous_loss       decimal(14, 2) DEFAULT 0, /*以前年度亏损额*/
    row_1               decimal(14, 2),
    row_2               decimal(14, 2),
    row_3               decimal(14, 2),
    row_4               decimal(14, 2),
    row_5               decimal(14, 2),
    row_6               decimal(14, 2),
    row_7               decimal(14, 2),
    row_8               decimal(14, 2),
    row_9               decimal(14, 2),
    row_10              decimal(14, 2),
    row_11              decimal(14, 2),
    row_12              decimal(14, 2),
    row_13              decimal(14, 2),
    row_14              decimal(14, 2),
    row_l15             decimal(14, 2),
    row_15              decimal(14, 2),
    row_16              decimal(14, 2),
    row_17              decimal(14, 2),
    row_18              decimal(14, 2),
    row_19              decimal(14, 2),
    row_20              decimal(14, 2),
    row_21              decimal(14, 2),
    primary key (id)
);
create unique index UK_enterprise_income_tax_quarter_main on enterprise_income_tax_quarter_main (account_book_id, start_period, end_period);

/*所得税申报表附表二*/
drop table if exists enterprise_income_tax_quarter_one;
CREATE TABLE enterprise_income_tax_quarter_one
(
    id              BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id bigint, /* 帐套id */
    start_period    varchar(8), /*起始期间*/
    end_period      varchar(8), /*结束期间*/
    row             varchar(8), /*行次*/
    balance         decimal(10, 2), /*本年累计金额*/
    primary key (id)
);
create unique index UK_enterprise_income_tax_quarter_one on enterprise_income_tax_quarter_one (account_book_id, start_period, end_period, row);

/*所得税申报表附表三*/
drop table if exists enterprise_income_tax_quarter_two;
CREATE TABLE enterprise_income_tax_quarter_two
(
    id                          BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id             bigint, /* 帐套id */
    start_period                varchar(8), /*起始期间*/
    end_period                  varchar(8), /*结束期间*/
    row                         int, /*行次*/
    original_value              decimal(14, 2), /*资产原值*/
    account_depreciation_amount decimal(14, 2), /*账载折旧金额(会计做账的折旧金额)*/
    tax_depreciation_amount     decimal(14, 2), /*按照税收一般规定计算的折旧金额（国家税收规定的折旧金额）*/
    quicken_depreciation_amount decimal(14, 2), /*享受加速折旧优惠计算的折旧金额(折旧额)*/
    tax_reduction_amount        decimal(14, 2), /*纳税调减金额*/
    discount_amount             decimal(14, 2), /*享受加速折旧优惠金额(可抵减利润的金额，满足一定条件才有)*/
    primary key (id)
);
create unique index UK_enterprise_income_tax_quarter_two on enterprise_income_tax_quarter_two (account_book_id, start_period, end_period, row);

/*所得税申报表附表三*/
drop table if exists enterprise_income_tax_quarter_three;
CREATE TABLE enterprise_income_tax_quarter_three
(
    id              BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id bigint, /* 帐套id */
    start_period    varchar(8), /*起始期间*/
    end_period      varchar(8), /*结束期间*/
    row             varchar(8), /*行次*/
    balance         decimal(10, 2), /*本年累计金额*/
    primary key (id)
);
create unique index UK_enterprise_income_tax_quarter_three on enterprise_income_tax_quarter_three (account_book_id, start_period, end_period, row);

/*所得税申报表附表四*/
drop table if exists enterprise_income_tax_quarter_four;
CREATE TABLE enterprise_income_tax_quarter_four
(
    id                              BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id                 bigint, /*帐套*/
    start_period                    varchar(8), /*起始期间*/
    end_period                      varchar(8), /*结束期间*/
    company_name                    varchar(64), /*企业名称*/
    tax_payer_code                  varchar(64), /*纳税人识别号/社会信用代码*/
    foreign_company_name_chinese    varchar(64), /*外国企业名称（中文）*/
    foreign_company_name_english    varchar(64), /*外国企业名称（外文）*/
    foreign_tax_payer_code          varchar(64), /*所在国纳税识别号*/
    establish_place_chinese         varchar(64), /*成立地（中文）*/
    establish_place_english         varchar(64), /*成立地（外文）*/
    main_business_type              varchar(64), /*主营业务类型*/
    reporter_shareholding_ratio     varchar(64), /*报告人持股比例*/
    shareholder_name_chinese        varchar(64), /*持股股东名称（中文）*/
    shareholder_name_english        varchar(64), /*持股股东名称（外文）*/
    residence_place_chinese         varchar(64), /*居住地或成立地（中文）*/
    residence_place_english         varchar(64), /*居住地或成立地（外文）*/
    shareholding_type               varchar(64), /*持股类型*/
    shareholding_ratio              varchar(64), /*持股比例*/
    achieve_ten_percent_date        varchar(12), /*达到10%以上权益份额的起始日期*/
    chinese_personal_name           varchar(64), /*中国居民个人姓名*/
    usual_residence_in_china        varchar(64), /*中国境内常住地*/
    id_type                         varchar(64), /*身份证件类型*/
    id_number                       varchar(64), /*身份证件号码*/
    position                        varchar(64), /*职务*/
    office_date                     varchar(12), /*任职日期起*/
    resignation_date                varchar(12), /*任职日期止*/
    acquired_share_type             varchar(64), /*被收购股份类型*/
    transaction_date                varchar(64), /*交易日期*/
    acquisition_method              varchar(64), /*收购方式*/
    before_acquisition_shareholding varchar(64), /*收购前报告人在外国企业持股份额*/
    after_acquisition_shareholding  varchar(64), /*收购后报告人在外国企业持股份额*/
    disposed_share_type             varchar(64), /*被处置股份类型*/
    disposal_date                   varchar(12), /*处置日期*/
    disposal_method                 varchar(64), /*处置方式*/
    before_disposal_shareholding    varchar(64), /*处置前报告人在外国企业持股份额*/
    after_disposal_shareholding     varchar(64), /*被收购股份类型*/
    primary key (id)
);
create unique index UK_enterprise_income_tax_quarter_four on enterprise_income_tax_quarter_four (account_book_id, start_period, end_period);

/*所得税申报表附表五*/
drop table if exists enterprise_income_tax_quarter_five;
CREATE TABLE enterprise_income_tax_quarter_five
(
    id               BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id  bigint, /* 帐套id */
    start_period     varchar(8), /*起始期间*/
    end_period       varchar(8), /*结束期间*/
    name             varchar(64), /*技术成果名称*/
    type             varchar(32), /*技术成果类型*/
    code             varchar(64), /*技术成果编号*/
    fair_value       decimal(14, 2), /*公允价值*/
    tax_base         decimal(14, 2), /*计税基础*/
    acquisition_date varchar(10), /*取得股权时间（年月日）*/
    deferred_income  decimal(14, 2), /*递延所得*/
    company_name     varchar(64), /*企业名称*/
    tax_payer_code   varchar(18), /*纳税人识别号*/
    tax_authorities  varchar(64), /*主管税务机关*/
    investor         varchar(64), /*与投资方是否为*/
    remark           varchar(255), /*备注*/
    write_period     varchar(8), /*填表日期（年月）*/
    primary key (id)
);

/*生产经营所得税申报表*/
drop table if exists income_tax_declaration_operating;
CREATE TABLE income_tax_declaration_operating
(
    id                    BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id       bigint, /* 帐套id */
    start_period          varchar(8), /*起始期间*/
    end_period            varchar(8), /*结束期间*/
    is_month_operating    int            default 1, /*是否月报*/
    collection_type       int            default 0, /*征收方式 0:核定征收  1:查账征收*/
    written_date          TIMESTAMP      default CURRENT_TIMESTAMP, /*填报日期*/
    declare_date          TIMESTAMP      default CURRENT_TIMESTAMP, /*申报日期*/
    income                decimal(14, 2), /*收入总额*/
    cost                  decimal(14, 2), /*成本费用*/
    profit                decimal(14, 2), /*利润总额*/
    offset_previous_loss  decimal(14, 2) default 0, /*弥补以前年度亏损*/
    employee_list_content VARCHAR, /*人员信息*/
    remark           VARCHAR, /*备注*/
    primary key (id)
);
create unique index UK_income_tax_declaration_operating on income_tax_declaration_operating (account_book_id, start_period, end_period);

/*商品和服务税收分类与编码*/
drop table if exists product_type;
CREATE TABLE product_type
(
    id   BIGINT NOT NULL AUTO_INCREMENT,
    code varchar(20), /*编码*/
    name varchar(64), /*名称*/
    primary key (id)
);

/*企业所得税年度纳税申报表填报表单*/
drop table if exists enterprise_income_tax_annual_total;
CREATE TABLE enterprise_income_tax_annual_total
(
    id              BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id bigint, /* 帐套id */
    year            varchar(8), /*年份*/
    no              varchar(8), /*表单编号*/
    is_select       int, /*是否勾选 0：否  1：是*/
    primary key (id)
);
create unique index UK_enterprise_income_tax_annual_total on enterprise_income_tax_annual_total (account_book_id, year, no);

/*A000000 企业基础信息表*/
drop table if exists enterprise_income_tax_annual_info;
CREATE TABLE enterprise_income_tax_annual_info
(
    id                 BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id    bigint,
    year               varchar(4),

    social_credit_code varchar(64) comment '纳税人识别号（统一社会信用代码）',
    account_book_name  varchar(64) comment '纳税人名称',
    start_date         varchar(10) comment '税款所属期起',
    end_date           varchar(10) comment '税款所属期止',
    declare_date       varchar(10) comment '申报日期',
    written_date       varchar(10) comment '填表日期',

    row101             varchar(64) comment '基本经营类型（必填项目） 101汇总纳税企业',
    row102             varchar(64) comment '102分支机构就地纳税比例（%）',
    row103             varchar(64) comment '103资产总额(填写平均值，单位：万元)',
    row104             int comment '104从业人数(填写平均值，单位：人)',
    row105             varchar(64) comment '105所属国民经济行业（填写代码）',
    row106             int comment '106从事国家限制或禁止行业',
    row107             varchar(64) comment '107适用会计准则或会计制度（填写代码）',
    row108             int comment '108采用一般企业财务报表格式（2019年版）',
    row109             int comment '109小型微利企业',
    row110             varchar(64) comment '110上市公司（多选） 境内     境外  否',

    row201             int comment '有关涉税事项情况（存在或者发生下列事项时必填）201从事股权投资业务',
    row202             int comment '202存在境外关联交易',
    row203             int comment '203选择采用的境外所得税收抵免方式 0 否 1 不分国（地区）不分项 2 分国（地区）不分项',
    row204             int comment '204有限合伙制创业投资企业的法人合伙人',
    row205             int comment '205创业投资企业',
    row206             varchar(64) comment '206技术先进型服务企业类型（填写代码）',
    row207             int comment '207非营利组织',
    row208             varchar(64) comment '208软件、集成电路企业类型（填写代码）',
    row209             varchar(64) comment '209集成电路生产项目类型',
    row2101            varchar(64) comment '210科技型中小企业 210-1 2019年（申报所属期年度）入库编号1',
    row2102            varchar(64) comment '210-2入库时间1',
    row2103            varchar(64) comment '210-3 2020年（所属期下一年度）入库编号2',
    row2104            varchar(64) comment '210-4入库时间2',
    row2111            varchar(64) comment '211高新技术企业申报所属期年度有效的高新技术企业证书 211-1 证书编号1',
    row2112            varchar(64) comment '211-2发证时间1',
    row2113            varchar(64) comment '211-3 证书编号2',
    row2114            varchar(64) comment '211-4发证时间2',
    row212             varchar(64) comment '212重组事项税务处理方式',
    row213             varchar(64) comment '213重组交易类型（填写代码）',
    row214             varchar(64) comment '214重组当事方类型（填写代码）',
    row215             varchar(64) comment '215政策性搬迁开始时间',
    row216             int comment '216发生政策性搬迁且停止生产经营无所得年度',
    row217             int comment '217政策性搬迁损失分期扣除年度',
    row218             int comment '218发生非货币性资产对外投资递延纳税事项',
    row219             int comment '219非货币性资产对外投资转让所得递延纳税年度',
    row220             int comment '220发生技术成果投资入股递延纳税事项',
    row221             int comment '221技术成果投资入股递延纳税年度',
    row222             int comment '222发生资产（股权）划转特殊性税务处理事项',
    row223             int comment '223债务重组所得递延纳税年度',

    row300             varchar(1024) comment '300企业主要股东及分红情况',
    row3001            varchar(64) comment '其余股东合计投资比例',
    row3002            varchar(64) comment '其余股东合计当年（决议日）分配的股息、红利等权益性投资收益金额',
    primary key (id)
);
create unique index UK_enterprise_income_tax_annual_info on enterprise_income_tax_annual_info (account_book_id, year);

/*A100000中华人民共和国企业所得税年度纳税申报表（A类）*/
drop table if exists enterprise_income_tax_annual_one;
CREATE TABLE enterprise_income_tax_annual_one
(
    id              BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id bigint, /* 帐套id */
    year            varchar(8), /* 年份 */
    row1            decimal(10, 2), /* 一、营业收入(填写A101010\101020\103000) */
    row2            decimal(10, 2), /* 减：营业成本(填写A102010\102020\103000) */
    row3            decimal(10, 2), /* 减：税金及附加 */
    row4            decimal(10, 2), /* 减：销售费用(填写A104000) */
    row5            decimal(10, 2), /* 减：管理费用(填写A104000) */
    row6            decimal(10, 2), /* 减：财务费用(填写A104000) */
    row7            decimal(10, 2), /* 减：资产减值损失 */
    row8            decimal(10, 2), /* 加：公允价值变动收益 */
    row9            decimal(10, 2), /* 加：投资收益 */
    row10           decimal(10, 2), /* 二、营业利润(1-2-3-4-5-6-7+8+9) */
    row11           decimal(10, 2), /* 加：营业外收入(填写A101010\101020\103000) */
    row12           decimal(10, 2), /* 减：营业外支出(填写A102010\102020\103000) */
    row13           decimal(10, 2), /* 三、利润总额（10+11-12） */
    row14           decimal(10, 2), /* 减：境外所得（填写A108010） */
    row15           decimal(10, 2), /* 加：纳税调整增加额（填写A105000） */
    row16           decimal(10, 2), /* 减：纳税调整减少额（填写A105000） */
    row17           decimal(10, 2), /* 减：免税、减计收入及加计扣除（填写A107010） */
    row18           decimal(10, 2), /* 加：境外应税所得抵减境内亏损（填写A108000） */
    row19           decimal(10, 2), /* 四、纳税调整后所得（13-14+15-16-17+18） */
    row20           decimal(10, 2), /* 减：所得减免（填写A107020） */
    row21           decimal(10, 2), /* 减：弥补以前年度亏损（填写A106000） */
    row22           decimal(10, 2), /* 减：抵扣应纳税所得额（填写A107030） */
    row23           decimal(10, 2), /* 五、应纳税所得额（19-20-21-22） */
    row24           decimal(10, 2), /* 税率（25%） */
    row25           decimal(10, 2), /* 六、应纳所得税额（23×24） */
    row26           decimal(10, 2), /* 减：减免所得税额（填写A107040） */
    row27           decimal(10, 2), /* 减：抵免所得税额（填写A107050） */
    row28           decimal(10, 2), /* 七、应纳税额（25-26-27） */
    row29           decimal(10, 2), /* 加：境外所得应纳所得税额（填写A108000） */
    row30           decimal(10, 2), /* 减：境外所得抵免所得税额（填写A108000） */
    row31           decimal(10, 2), /* 八、实际应纳所得税额（28+29-30） */
    row32           decimal(10, 2), /* 减：本年累计实际已缴纳的所得税额 */
    row33           decimal(10, 2), /* 九、本年应补（退）所得税额（31-32） */
    row34           decimal(10, 2), /* 其中：总机构分摊本年应补（退）所得税额(填写A109000) */
    row35           decimal(10, 2), /* 财政集中分配本年应补（退）所得税额(填写A109000) */
    row36           decimal(10, 2), /* 总机构主体生产经营部门分摊本年应补（退）所得税额(填写A109000) */
    primary key (id)
);
create unique index UK_enterprise_income_tax_annual_one on enterprise_income_tax_annual_one (account_book_id, year);

/*A101010一般企业收入明细表*/
drop table if exists enterprise_income_tax_annual_two;
CREATE TABLE enterprise_income_tax_annual_two
(
    id              BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id bigint, /* 帐套id */
    year            varchar(8), /* 年份 */
    row1            decimal(10, 2), /* 一、营业收入（2+9） */
    row2            decimal(10, 2), /* （一）主营业务收入（3+5+6+7+8） */
    row3            decimal(10, 2), /* 1.销售商品收入 */
    row4            decimal(10, 2), /* 其中：非货币性资产交换收入 */
    row5            decimal(10, 2), /* 2.提供劳务收入 */
    row6            decimal(10, 2), /* 3.建造合同收入 */
    row7            decimal(10, 2), /* 4.让渡资产使用权收入 */
    row8            decimal(10, 2), /* 5.其他 */
    row9            decimal(10, 2), /* （二）其他业务收入（10+12+13+14+15） */
    row10           decimal(10, 2), /* 1.销售材料收入 */
    row11           decimal(10, 2), /* 其中：非货币性资产交换收入 */
    row12           decimal(10, 2), /* 2.出租固定资产收入 */
    row13           decimal(10, 2), /* 3.出租无形资产收入 */
    row14           decimal(10, 2), /* 4.出租包装物和商品收入 */
    row15           decimal(10, 2), /* 5.其他 */
    row16           decimal(10, 2), /* 二、营业外收入（17+18+19+20+21+22+23+24+25+26） */
    row17           decimal(10, 2), /* （一）非流动资产处置利得 */
    row18           decimal(10, 2), /* （二）非货币性资产交换利得 */
    row19           decimal(10, 2), /* （三）债务重组利得 */
    row20           decimal(10, 2), /* （四）政府补助利得 */
    row21           decimal(10, 2), /* （五）盘盈利得 */
    row22           decimal(10, 2), /* （六）捐赠利得 */
    row23           decimal(10, 2), /* （七）罚没利得 */
    row24           decimal(10, 2), /* （八）确实无法偿付的应付款项 */
    row25           decimal(10, 2), /* （九）汇兑收益 */
    row26           decimal(10, 2), /* （十）其他 */
    primary key (id)
);
create unique index UK_enterprise_income_tax_annual_two on enterprise_income_tax_annual_two (account_book_id, year);

/*A101010一般企业收入明细表*/
drop table if exists enterprise_income_tax_annual_three;
CREATE TABLE enterprise_income_tax_annual_three
(
    id              BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id bigint, /* 帐套id */
    year            varchar(8), /* 年份 */
    row1            decimal(10, 2), /* 一、营业成本（2+9） */
    row2            decimal(10, 2), /*    （一）主营业务成本（3+5+6+7+8） */
    row3            decimal(10, 2), /*        1.销售商品成本 */
    row4            decimal(10, 2), /*          其中:非货币性资产交换成本 */
    row5            decimal(10, 2), /*        2.提供劳务成本 */
    row6            decimal(10, 2), /*        3.建造合同成本 */
    row7            decimal(10, 2), /*        4.让渡资产使用权成本 */
    row8            decimal(10, 2), /*        5.其他 */
    row9            decimal(10, 2), /*    （二）其他业务成本（10+12+13+14+15） */
    row10           decimal(10, 2), /*        1. 销售材料成本 */
    row11           decimal(10, 2), /*          其中:非货币性资产交换成本 */
    row12           decimal(10, 2), /*        2.出租固定资产成本 */
    row13           decimal(10, 2), /*        3.出租无形资产成本 */
    row14           decimal(10, 2), /*        4.包装物出租成本 */
    row15           decimal(10, 2), /*        5.其他 */
    row16           decimal(10, 2), /* 二、营业外支出（17+18+19+20+21+22+23+24+25+26） */
    row17           decimal(10, 2), /*    （一）非流动资产处置损失 */
    row18           decimal(10, 2), /*    （二）非货币性资产交换损失 */
    row19           decimal(10, 2), /*    （三）债务重组损失 */
    row20           decimal(10, 2), /*    （四）非常损失 */
    row21           decimal(10, 2), /*    （五）捐赠支出 */
    row22           decimal(10, 2), /*    （六）赞助支出 */
    row23           decimal(10, 2), /*    （七）罚没支出 */
    row24           decimal(10, 2), /*    （八）坏账损失 */
    row25           decimal(10, 2), /*    （九）无法收回的债券股权投资损失 */
    row26           decimal(10, 2), /*    （十）其他 */
    primary key (id)
);
create unique index UK_enterprise_income_tax_annual_three on enterprise_income_tax_annual_three (account_book_id, year);

/*企业所得税年度纳税申报表附表四-期间费用明细表*/
drop table if exists enterprise_income_tax_annual_four;
CREATE TABLE enterprise_income_tax_annual_four
(
    id                      BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id         bigint, /* 帐套id */
    year                    varchar(8), /*年份*/
    row                     varchar(8), /*行次*/
    sales_fee               decimal(10, 2), /*销售费用*/
    sales_overseas_fee      decimal(10, 2), /*销售费用-境外支出*/
    management_fee          decimal(10, 2), /*管理费用*/
    management_overseas_fee decimal(10, 2), /*管理费用-境外支出*/
    finance_fee             decimal(10, 2), /*财务费用*/
    finance_overseas_fee    decimal(10, 2), /*财务费用-境外支出*/
    primary key (id)
);
create unique index UK_enterprise_income_tax_annual_four on enterprise_income_tax_annual_four (account_book_id, year, row);

/*企业所得税年度纳税申报表附表五-纳税调整项目明细表*/
drop table if exists enterprise_income_tax_annual_five;
CREATE TABLE enterprise_income_tax_annual_five
(
    id                    BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id       bigint, /* 帐套id */
    year                  varchar(8), /*年份*/
    row                   varchar(8), /*行次*/
    account_record_amount decimal(10, 2), /*账载金额*/
    tax_amount            decimal(10, 2), /*税收金额*/
    increase_amount       decimal(10, 2), /*调增金额*/
    reduce_amount         decimal(10, 2), /*调减金额*/
    primary key (id)
);
create unique index UK_enterprise_income_tax_annual_five on enterprise_income_tax_annual_five (account_book_id, year, row);

/*企业所得税年报附表六-A105050职工薪酬支出及纳税调整明细表*/
drop table if exists enterprise_income_tax_annual_six;
CREATE TABLE enterprise_income_tax_annual_six
(
    id                        BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id           bigint, /* 帐套id */
    year                      varchar(8), /*年份*/
    row                       varchar(8), /*行次*/
    account_record_amount     decimal(10, 2), /*账载金额*/
    actual_amount             decimal(10, 2), /*实际金额*/
    tax_deduction_rate        decimal(10, 2), /*税收规定扣除率*/
    previous_total_deductions decimal(10, 2), /*以前年度累计结转扣除额*/
    tax_amount                decimal(10, 2), /*税收金额*/
    tax_adjustment_amount     decimal(10, 2), /*纳税调整金额*/
    after_total_deductions    decimal(10, 2), /*累计结转以后年度扣除额*/
    primary key (id)
);
create unique index UK_enterprise_income_tax_annual_six on enterprise_income_tax_annual_six (account_book_id, year, row);

/*企业所得税年报附表七-A105060广告费和业务宣传费跨年度纳税调整明细表*/
drop table if exists enterprise_income_tax_annual_seven;
CREATE TABLE enterprise_income_tax_annual_seven
(
    id              BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id bigint, /* 帐套id */
    year            varchar(8), /*年份*/
    row1            decimal(10, 2), /* 一、本年广告费和业务宣传费支出*/
    row2            decimal(10, 2), /* 减：不允许扣除的广告费和业务宣传费支出*/
    row3            decimal(10, 2), /* 二、本年符合条件的广告费和业务宣传费支出（1-2）*/
    row4            decimal(10, 2), /* 三、本年计算广告费和业务宣传费扣除限额的销售（营业）收入*/
    row5            decimal(10, 2), /* 乘：税收规定扣除率*/
    row6            decimal(10, 2), /* 四、本企业计算的广告费和业务宣传费扣除限额（4×5）*/
    row7            decimal(10, 2), /* 五、本年结转以后年度扣除额（3＞6，本行=3-6；3≤6，本行=0）*/
    row8            decimal(10, 2), /* 加：以前年度累计结转扣除额*/
    row9            decimal(10, 2), /* 减：本年扣除的以前年度结转额[3＞6，本行=0；3≤6，本行=8与（6-3）孰小值]*/
    row10           decimal(10, 2), /* 六、按照分摊协议归集至其他关联方的广告费和业务宣传费（10≤3与6孰小值）*/
    row11           decimal(10, 2), /* 按照分摊协议从其他关联方归集至本企业的广告费和业务宣传费*/
    row12           decimal(10, 2), /* 七、本年广告费和业务宣传费支出纳税调整金额*/
    row13           decimal(10, 2), /* 八、累计结转以后年度扣除额（7+8-9）*/
    primary key (id)
);
create unique index UK_enterprise_income_tax_annual_seven on enterprise_income_tax_annual_seven (account_book_id, year);

/*企业所得税年报附表八-A105080资产折旧、摊销及纳税调整明细表*/
drop table if exists enterprise_income_tax_annual_eight;
CREATE TABLE enterprise_income_tax_annual_eight
(
    id                               BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id                  bigint, /* 帐套id */
    year                             varchar(8), /*年份*/
    row                              varchar(8), /*行次*/
    original_value                   decimal(10, 2), /*资产原值*/
    this_year_depreciate             decimal(10, 2), /*本年折旧、摊销额*/
    account_record_total_depreciate  decimal(10, 2), /*【账载金额】累计折旧、摊销额*/
    assets_tax_base                  decimal(10, 2), /*资产计税基础*/
    tax_depreciate                   decimal(10, 2), /*税收折旧额*/
    provision_calculate_depreciate   decimal(10, 2), /*享受加速折旧政策的资产按税收一般规定计算的折旧、摊销额*/
    accelerate_depreciate_statistics decimal(10, 2), /*加速折旧统计额*/
    tax_total_depreciate             decimal(10, 2), /*【税收金额】累计折旧、摊销额*/
    tax_adjustment_amount            decimal(10, 2), /*纳税调整金额*/
    primary key (id)
);
create unique index UK_enterprise_income_tax_annual_eight on enterprise_income_tax_annual_eight (account_book_id, year, row);

/*企业所得税年报附表九-A106000企业所得税弥补亏损明细表*/
drop table if exists enterprise_income_tax_annual_nine;
CREATE TABLE enterprise_income_tax_annual_nine
(
    id                      BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id         bigint, /* 帐套id */
    year                    varchar(8), /*年份*/
    row                     varchar(8), /*行次*/
    annual                  varchar(4), /*年度*/
    current_domestic_income decimal(10, 2), /*当年境内所得额*/
    transfer_losses         decimal(10, 2), /*分立转出的亏损额*/
    offset_five             decimal(10, 2), /*可弥补年限5年*/
    offset_ten              decimal(10, 2), /*可弥补年限10年*/
    type                    varchar(64), /*弥补亏损企业类型*/
    current_losses          decimal(10, 2), /*当年亏损额*/
    current_wait_losses     decimal(10, 2), /*当年待弥补的亏损额*/
    use_domestic_income     decimal(10, 2), /*使用境内所得弥补*/
    use_overseas_income     decimal(10, 2), /*使用境外所得弥补*/
    adjusting_offset_loss   decimal(10, 2), /*当年可结转以后年度弥补的亏损额*/
    primary key (id)
);
create unique index UK_enterprise_income_tax_annual_nine on enterprise_income_tax_annual_nine (account_book_id, year, row);

/*A107040减免所得税优惠明细表*/
drop table if exists enterprise_income_tax_annual_ten;
CREATE TABLE enterprise_income_tax_annual_ten
(
    id              BIGINT NOT NULL AUTO_INCREMENT,
    account_book_id bigint, /* 帐套id */
    year            varchar(8), /* 年份 */
    row1            decimal(10, 2), /* 一、符合条件的小型微利企业减免企业所得税*/
    row2            decimal(10, 2), /* 二、国家需要重点扶持的高新技术企业减按15%的税率征收企业所得税（填写A107041）*/
    row3            decimal(10, 2), /* 三、经济特区和上海浦东新区新设立的高新技术企业在区内取得的所得定期减免企业所得税（填写A107041）*/
    row4            decimal(10, 2), /* 四、受灾地区农村信用社免征企业所得税（4.1+4.2）*/
    row4a           decimal(10, 2), /* （一）芦山受灾地区农村信用社免征企业所得税*/
    row4b           decimal(10, 2), /* （二）鲁甸受灾地区农村信用社免征企业所得税*/
    row5            decimal(10, 2), /* 五、动漫企业自主开发、生产动漫产品定期减免企业所得税*/
    row6            decimal(10, 2), /* 六、线宽小于0.8微米（含）的集成电路生产企业减免企业所得税（填写A107042）*/
    row7            decimal(10, 2), /* 七、线宽小于0.25微米的集成电路生产企业减按15%税率征收企业所得税（填写A107042）*/
    row8            decimal(10, 2), /* 八、投资额超过80亿元的集成电路生产企业减按15%税率征收企业所得税（填写A107042）*/
    row9            decimal(10, 2), /* 九、线宽小于0.25微米的集成电路生产企业减免企业所得税（填写A107042）*/
    row10           decimal(10, 2), /* 十、投资额超过80亿元的集成电路生产企业减免企业所得税（填写A107042）*/
    row11           decimal(10, 2), /* 十一、新办集成电路设计企业减免企业所得税（填写A107042）*/
    row12           decimal(10, 2), /* 十二、国家规划布局内集成电路设计企业可减按10%的税率征收企业所得税（填写A107042）*/
    row13           decimal(10, 2), /* 十三、符合条件的软件企业减免企业所得税（填写A107042）*/
    row14           decimal(10, 2), /* 十四、国家规划布局内重点软件企业可减按10%的税率征收企业所得税（填写A107042）*/
    row15           decimal(10, 2), /* 十五、符合条件的集成电路封装、测试企业定期减免企业所得税（填写A107042）*/
    row16           decimal(10, 2), /* 十六、符合条件的集成电路关键专用材料生产企业、集成电路专用设备生产企业定期减免企业所得税（填写A107042）*/
    row17           decimal(10, 2), /* 十七、经营性文化事业单位转制为企业的免征企业所得税*/
    row18           decimal(10, 2), /* 十八、符合条件的生产和装配伤残人员专门用品企业免征企业所得税*/
    row19           decimal(10, 2), /* 十九、技术先进型服务企业减按15%的税率征收企业所得税*/
    row20           decimal(10, 2), /* 二十、服务贸易创新发展试点地区符合条件的技术先进型服务企业减按15%的税率征收企业所得税*/
    row21           decimal(10, 2), /* 二十一、设在西部地区的鼓励类产业企业减按15%的税率征收企业所得税*/
    row22           decimal(10, 2), /* 二十二、新疆困难地区新办企业定期减免企业所得税*/
    row23           decimal(10, 2), /* 二十三、新疆喀什、霍尔果斯特殊经济开发区新办企业定期免征企业所得税*/
    row24           decimal(10, 2), /* 二十四、广东横琴、福建平潭、深圳前海等地区的鼓励类产业企业减按15%税率征收企业所得税*/
    row25           decimal(10, 2), /* 二十五、北京冬奥组委、北京冬奥会测试赛赛事组委会免征企业所得税*/
    row26           decimal(10, 2), /* 二十六、享受过渡期税收优惠定期减免企业所得税*/
    row27           decimal(10, 2), /* 二十七、其他*/
    row28           decimal(10, 2), /* 二十八、减：项目所得额按法定税率减半征收企业所得税叠加享受减免税优惠*/
    row29           decimal(10, 2), /* 二十九、支持和促进重点群体创业就业企业限额减征企业所得税(29.1+29.2)*/
    row29a          decimal(10, 2), /* （一）下岗失业人员再就业*/
    row29b          decimal(10, 2), /* （二）高校毕业生就业*/
    row30           decimal(10, 2), /* 三十、扶持自主就业退役士兵创业就业企业限额减征企业所得税*/
    row31           decimal(10, 2), /* 三十一、民族自治地方的自治机关对本民族自治地方的企业应缴纳的企业所得税中属于地方分享的部分减征或免征（£免征     £减征:减征幅度____%  ）*/
    row32           decimal(10, 2), /* 合计（1+2+…+26+27-28+29+30+31）*/
    primary key (id)
);
create unique index enterprise_income_tax_annual_ten on enterprise_income_tax_annual_ten (account_book_id, year);

drop table if exists file_history;
create table file_history
(
    id                bigint not null auto_increment comment '主键id',
    gmt_create        timestamp default current_timestamp comment '创建时间',
    gmt_modified      timestamp default current_timestamp comment '修改时间',
    account_book_id   bigint comment '账套id',
    account_id        bigint comment '操作人id',
    file_name         varchar(255) comment '文件名称',
    oss_object_key    varchar(64) comment '阿里云oss的objectKey ********/****************.xls',
    account_book_name varchar(64) comment '账套名称',
    account_name      varchar(64) comment '操作人名称',
    area              varchar(64) comment '区域',
    type              int comment '文件类型',
    primary key (id)
);

drop table if exists invoice_log;
create table invoice_log
(
    id              bigint not null auto_increment comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '账套id',
    account_id      bigint comment '操作人id',
    period varchar (8) comment '期间',
    request_content varchar(1024) comment '请求参数的json',
    result_size     int comment '返回结果条数',
    primary key (id)
);

drop table if exists tax_record;
create table tax_record
(
    id               bigint not null auto_increment comment '主键id',
    gmt_create       datetime default current_timestamp comment '创建时间',
    gmt_modified     datetime default current_timestamp comment '修改时间',
    version          int      default 0 comment '版本号',
    start_time       datetime comment '任务开始时间',
    account_book_id  bigint comment '账套id',
    account_id       bigint comment '操作人id',
    tax_type         int comment '税种',
    area_id          int comment '区域编号',
    action_id        int comment '操作编号',
    source_id        int comment '申报源（1：电子税务局 2：自然人系统）',
    worker_index     int(4) not null default 0,
    order_level      int(4) not null default 0,
    period varchar (8) comment '期间',
    tax_state        int comment '申报状态',
    pay_balance      decimal(14, 2) comment '申报税额',
    result_message   varchar(255) comment '结果信息',
    result_url       varchar(255) comment '结果地址',
    result_image_url varchar(255) comment '结果图片地址',
    is_last_month    int      default 0 comment '个税是否按上月申报',
    tax_payer_phone  varchar(11) comment '手机号',
    national_name    varchar(255) comment '税局登录名',
    ip               varchar(32) comment 'ip地址',
    primary key (id)
);
create unique index UK_tax_record on tax_record (account_book_id, period, area_id, action_id);

drop table if exists tax_record_count;
create table tax_record_count
(
    id              bigint not null auto_increment comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '账套id',
    source_id       int comment '申报源（1：电子税务局 2：自然人系统）',
    period varchar (8) comment '期间',
    submit_state    int comment '提交状态',
    tax_state       int comment '申报状态',
    pay_state       int comment '扣款状态',
    check_state     int comment '检查状态',
    pay_balance     decimal(14, 2) comment '申报税额',
    primary key (id)
);
create unique index UK_tax_record_count on tax_record_count (account_book_id, period, source_id);

drop table if exists tax_record_cloud;
create table tax_record_cloud
(
    id              bigint not null auto_increment comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '账套id',
    tax_code        varchar(32) comment '税号',
    cloud_url       varchar(255) comment '数据地址',
    cloud_state     int comment '数据状态',
    primary key (id)
);
create unique index UK_tax_record_cloud on tax_record_cloud (tax_code);

drop table if exists uibot_setting;
create table uibot_setting
(
    id           bigint not null auto_increment comment '主键id',
    gmt_create   datetime default current_timestamp comment '创建时间',
    gmt_modified datetime default current_timestamp comment '修改时间',
    flow_type    int comment '流程类型',
    area_id      int comment '区域编号',
    action_id    int comment '操作编号',
    source_id    int comment '申报源（1：电子税务局 2：自然人系统）',
    flow_comment varchar(255) comment '流程描述',
    base_url     varchar(255) comment '接口地址',
    static_str   varchar(255) comment '固定字符串',
    app_key      varchar(255) comment 'AppKey',
    app_secret   varchar(255) comment 'AppSecret',
    flow_code    varchar(255) comment '流程code',
    flow_id      bigint comment '流程id',
    worker_name  varchar(255) comment 'worker名称',
    worker_id    varchar(32) comment 'workerId',
    is_online    int comment '是否上线',
    is_send      int comment '是否发送请求',
    primary key (id)
);

drop table if exists surtax;
create table surtax
(
    id                 BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create         datetime default current_timestamp comment '创建时间',
    gmt_modified       datetime default current_timestamp comment '修改时间',
    account_book_id    bigint comment '帐套id',
    start_period       varchar(8) comment '起始期间',
    end_period         varchar(8) comment '结束期间',
    is_reduction1      int comment '本期是否适用增值税小规模纳税人减征政策',
    reduction_per_con  decimal(5, 2) comment '减征比例_城市维护建设税（%）',
    reduction_per_edu  decimal(5, 2) comment '减征比例_教育费附加（%）',
    reduction_per_loc  decimal(5, 2) comment '减征比例_地方教育附加（%）',
    is_reduction2      int comment '本期是否适用试点建设培育产教融合型企业抵免政策',
    invest             decimal(14, 2) comment '当期新增投资额',
    previous_reduction decimal(14, 2) comment '上期留抵可抵免金额',
    next_reduction     decimal(14, 2) comment '结转下期可抵免金额',
    content_con        varchar(1024) comment '城市维护建设税的json字符串',
    content_edu        varchar(1024) comment '教育费附加的json字符串',
    content_loc        varchar(1024) comment '地方教育附加的json字符串',
    agent_name         varchar(10) comment '经办人名称',
    agent_id           varchar(20) comment '经办人身份证号',
    primary key (id)
);
create unique index UK_surtax on surtax (account_book_id, start_period, end_period);

drop table if exists stamp_tax;
create table stamp_tax
(
    id                  BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create          datetime default current_timestamp comment '创建时间',
    gmt_modified        datetime default current_timestamp comment '修改时间',
    account_book_id     bigint comment '帐套id',
    start_period        varchar(8) comment '起始期间',
    end_period          varchar(8) comment '结束期间',
    is_reduction        int comment '本期是否适用增值税小规模纳税人减征政策',
    reduction_per_stamp decimal(5, 2) comment '减征比例（%）',
    content             varchar(2048) comment '其他信息的json字符串',
    agent_name          varchar(10) comment '经办人名称',
    agent_id            varchar(20) comment '经办人身份证号',
    main_body           int comment '减征政策适用主体',
    primary key (id)
);
create unique index UK_stamp_tax on stamp_tax (account_book_id, start_period, end_period);

drop table if exists stamp_tax_year;
create table stamp_tax_year
(
    id                  BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create          datetime default current_timestamp comment '创建时间',
    gmt_modified        datetime default current_timestamp comment '修改时间',
    account_book_id     bigint comment '帐套id',
    year                varchar(8) comment '年',
    is_reduction        int comment '本期是否适用增值税小规模纳税人减征政策',
    reduction_per_stamp decimal(5, 2) comment '减征比例（%）',
    content             varchar(2048) comment '其他信息的json字符串',
    agent_name          varchar(10) comment '经办人名称',
    agent_id            varchar(20) comment '经办人身份证号',
    main_body           int comment '减征政策适用主体',
    primary key (id)
);
create unique index UK_stamp_tax_year on stamp_tax_year (account_book_id, year);

drop table if exists zero_period;
create table zero_period
(
    id              BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '帐套id',
    period varchar (8) comment '期间',
    primary key (id)
);
create unique index UK_zero_period on zero_period (account_book_id, period);

drop table if exists inventory_order;
create table inventory_order
(
    id                 BIGINT   NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create         datetime default current_timestamp comment '创建时间',
    gmt_modified       datetime default current_timestamp comment '修改时间',
    no                 varchar(4) comment '单据号',
    account_book_id    bigint comment '帐套id',
    period varchar (8) comment '期间',
    voucher_id         bigint comment '凭证id',
    invoice_id         bigint comment '票据id',
    dim_inventory_id   bigint comment '存货辅助核算id',
    order_date         datetime NOT NULL comment '单据日期',
    measuring_unit     varchar(8) comment '计量单位',
    number             decimal(16, 4) comment '数量',
    money              decimal(14, 2) comment '金额',
    source             int comment '来源',
    type               int comment '类型',
    opposite_inventory bigint comment '对应产品',
    price              decimal(16, 4) comment '单价',
    receipt_type       int comment '单据类型',
    tax_rate           decimal(14, 2) comment '税率',
    tax_money          decimal(14, 2) comment '税额',
    supplier           bigint comment '供应商(对方科目的辅助核算名称)',
    opposite_subject   bigint comment '对方科目',
    tax_subject        bigint comment '税金科目',
    dim_inventory_type int comment '存货类型',
    primary key (id)
);

drop table if exists inventory_order_detail;
create table inventory_order_detail
(
    id                 BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create         datetime default current_timestamp comment '创建时间',
    gmt_modified       datetime default current_timestamp comment '修改时间',
    account_book_id    bigint comment '帐套id',
    inventory_order_id bigint comment '单据id',
    period varchar (8) comment '期间',
    dim_inventory_id   bigint comment '存货ID',
    dim_inventory_name varchar(255) comment '存货名称',
    specification      varchar(255) comment '规格',
    measuring_unit     varchar(8) comment '计量单位',
    number             decimal(16, 4) comment '数量',
    price              decimal(16, 4) comment '单价',
    money              decimal(14, 2) comment '金额',
    tax_rate           decimal(14, 2) comment '税率',
    tax_money          decimal(14, 2) comment '税额',
    opposite_inventory bigint comment '对应产品',
    primary key (id)
);

drop table if exists uibot_invoice;
create table uibot_invoice
(
    id              bigint not null auto_increment comment '主键id',
    gmt_create      datetime    default current_timestamp comment '创建时间',
    gmt_modified    datetime    default current_timestamp comment '修改时间',
    version         int         default 0 comment '版本号',
    start_time      datetime comment '任务开始时间',
    account_book_id bigint comment '账套id',
    account_id      bigint comment '操作人id',
    area_id         int comment '区域编号',
    order_level     int(4) not null default 0,
    period varchar (8) comment '期间',
    state           int comment '取票状态',
    result_message  varchar(255) comment '结果信息',
    result_url      varchar(255) comment '结果地址',
    is_all          int         default 0 comment '是否全部勾选0:已勾选；1：全部',
    is_month        int         default 1 comment '是否月度取票（勾选月度取票或者季度取票）0:季度；1：月度',
    invoice_type    varchar(32) default '0,1,2,3,4,5,6,7' comment '发票类型',
    tax_payer_phone varchar(11) comment '手机号',
    national_name   varchar(255) comment '税局登录名',
    ip              varchar(32) comment 'ip地址',
    import_time datetime comment'导入时间',
    import_status int default 0 comment'导入状态',
    import_result_message varchar(255) comment'结果信息',
    primary key (id)
);
create unique index UK_uibot_invoice on uibot_invoice (account_book_id, period);

drop table if exists accounter_printer_set;
create table accounter_printer_set
(
    id                bigint NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create        datetime default current_timestamp comment '创建时间',
    gmt_modified      datetime default current_timestamp comment '修改时间',
    voucher_print     varchar(255) comment '凭证边距',
    voucher_width     varchar(16) comment '凭证纸的宽度',
    voucher_height    varchar(16) comment '凭证纸的高度',
    print_voucher_dir varchar(16) comment '凭证打印方向',
    voucher_rows      varchar(16) comment '凭证行数',
    a_four_voucher    varchar(16) comment 'A4纸打印凭证',
    lh                varchar(16) comment '行高',
    sheet             varchar(255) comment '报表打印边距',
    sheet_width       varchar(16) comment '报表的宽度',
    sheet_height      varchar(16) comment '报表的高度',
    print_sheet_dir   varchar(16) comment '报表打印方向',
    accounter_id      bigint comment '账号id',
    print_way         varchar(16) comment '打印方式',
    voucher_font      varchar(16) comment '凭证字体',
    sheet_font        varchar(16) comment '报表字体大小',
    tax_preparer      varchar(16) comment '报税人',
    bind_people       varchar(16) comment '装订人',
    book_keeper       varchar(16) comment '记账人',
    teller            varchar(16) comment '出纳',
    document_maker    varchar(16) comment '制单人',
    primary key (id)
);
create unique index UK_accounter_printer_set on accounter_printer_set (accounter_id);

drop table if exists send_sms;
create table send_sms
(
    id              BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '帐套id',
    phone           varchar(11) comment '手机号',
    primary key (id)
);

drop table if exists bank_account;
create table bank_account
(
    id              bigint not null auto_increment comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '账套id',
    type            int      default 0 comment '银行类型',
    account         varchar(32) comment '账号',
    phone           varchar(11) comment '手机号',
    user_no         varchar(32) comment '操作员号',
    user_name       varchar(32) comment '用户名',
    password        varchar(32) comment '密码',
    collect_type    int comment '采集方式',
    primary key (id)
);
create unique index UK_bank_account on bank_account (account_book_id, type, collect_type);

drop table if exists invoice_image_log;
create table invoice_image_log
(
    id                      bigint not null auto_increment comment '主键id',
    gmt_create              datetime default current_timestamp comment '创建时间',
    gmt_modified            datetime default current_timestamp comment '修改时间',
    account_book_id         bigint comment '账套id',
    account_id              bigint comment '操作人id',
    is_provider_or_customer int comment '0：客户（销售发票）,1：供应商(采购发票)',
    period varchar (8) comment '期间',
    file_name               varchar(128) comment '文件名',
    url                     varchar(255) comment '图片地址',
    result_content          VARCHAR comment '结果数据',
    is_success              int comment '是否成功导入',
    message                 varchar(255) comment '结果信息',
    primary key (id)
);
create index IDX_invoice_image_log on invoice_image_log (account_book_id);

drop table if exists journal_image_log;
create table journal_image_log
(
    id              bigint not null auto_increment comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '账套id',
    account_id      bigint comment '操作人id',
    period varchar (8) comment '期间',
    file_name       varchar(128) comment '文件名',
    url             varchar(255) comment '图片地址',
    result_content  VARCHAR comment '结果数据',
    message         varchar(255) comment '结果信息',
    primary key (id)
);
create index IDX_journal_image_log on journal_image_log (account_book_id);

drop table if exists tax_sms;
create table tax_sms
(
    id           BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create   datetime default current_timestamp comment '创建时间',
    gmt_modified datetime default current_timestamp comment '修改时间',
    phone        varchar(11) comment '手机号',
    state        int comment '状态',
    primary key (id)
);

drop table if exists bank_account_detail_icbc;
create table bank_account_detail_icbc
(
    id              BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '账套id',
    period varchar (8) comment '期间',
    orderid         varchar(64) comment '回单提取订单编号',
    receipt_url     varchar(255) comment '回单地址',
    busi_date       varchar(10) comment '入账日期',
    drcrf           varchar(1) comment '借贷标志 0 借，付款 1 贷，收款',
    summary         varchar(20) comment '摘要',
    amount          varchar(17) comment '金额',
    reci_pnam       varchar(60) comment '对方户名',
    serialno        varchar(11) comment '流水号',
    busitime        varchar(8) comment ' 入账时间',
    trxcode         varchar(5) comment ' 交易代码',
    detailf         varchar(1) comment ' 明细性质',
    vouhtype        varchar(9) comment ' 凭证种类',
    vouhno          varchar(17) comment '凭证号',
    currtype        varchar(3) comment '币种',
    balance         varchar(17) comment '当前余额',
    work_date       varchar(10) comment '工作日期',
    value_day       varchar(10) comment '调整起息日期',
    stat_code       varchar(7) comment ' 外汇统计代码',
    sett_mode       varchar(3) comment ' 外汇结算方式',
    act_code        varchar(10) comment '帐户核算机构号',
    tellerno        varchar(5) comment ' 柜员号',
    authtlno        varchar(5) comment ' 授权柜员号',
    authno          varchar(6) comment ' 授权代号',
    termid          varchar(15) comment '终端号',
    reci_pacc       varchar(34) comment '对方账号',
    crvouh_type     varchar(9) comment ' 对方凭证种类',
    crvouhno        varchar(17) comment '对方凭证号',
    oref            varchar(20) comment '相关业务编号',
    drbus_code      varchar(54) comment '借方业务代码',
    crbus_code      varchar(54) comment '贷方业务代码',
    en_summry       varchar(40) comment '英文备注',
    tran_tel        varchar(35) comment '经办柜员号',
    impor_tel       varchar(5) comment ' 录入柜员号',
    check_tel       varchar(5) comment ' 复核柜员号',
    recipcno        varchar(15) comment '对方客户编号',
    recip_bkn       varchar(12) comment '对方行号',
    recip_bna       varchar(60) comment '对方行名',
    oper_type       varchar(3) comment ' 网银业务种类',
    notes           varchar(140) comment '言',
    purpose         varchar(20) comment '用途',
    servface        varchar(3) comment ' 服务界面',
    event_seq       varchar(9) comment ' 大交易序号',
    ptrx_seq        varchar(7) comment ' 小交易序号',
    updtranf        varchar(1) comment ' 冲正标志',
    revtranf        varchar(1) comment ' 正反交易标志',
    primary key (id)
);
create index IDX_bank_account_detail_icbc on bank_account_detail_icbc (account_book_id, period);

drop table if exists tax_info;
create table tax_info
(
    id              bigint not null auto_increment comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '账套id',
    tax_info_type   varchar(64) comment '税种',
    tax_info_detail varchar(128) comment '税目',
    tax_info_method int comment '纳税期限 0 :季 、1 :月、2：次、3：半年、4：年',
    tax_rate        varchar(16) comment '税率',
    start_date      varchar(10) comment '认定有效期起',
    end_date        varchar(10) comment '认定有效期止',
    source          int comment '来源 1：税局采集 2：录入',
    is_tax_info     int comment '是否申报 0：否，1：是',
    primary key (id)
);

drop table if exists uibot_tax_info;
create table uibot_tax_info
(
    id              bigint not null auto_increment comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    version         int      default 0 comment '版本号',
    start_time      datetime comment '任务开始时间',
    account_book_id bigint comment '账套id',
    area_id         int comment '区域编号',
    order_level     int(4) not null default 0,
    state           int comment '取税状态',
    result_message  varchar(255) comment '结果信息',
    result_json     VARCHAR comment '结果数据json',
    tax_payer_phone varchar(11) comment '手机号',
    national_name   varchar(255) comment '税局登录名',
    ip              varchar(32) comment 'ip地址',
    primary key (id)
);
create index IDX_uibot_tax_info on uibot_tax_info (account_book_id);

drop table if exists vat_cut_declaration_extra;
CREATE TABLE vat_cut_declaration_extra
(
    id              bigint not null auto_increment comment '主键id',
    gmt_create      datetime       default current_timestamp comment '创建时间',
    gmt_modified    datetime       default current_timestamp comment '修改时间',
    account_book_id bigint comment '帐套id ',
    start_period    varchar(8) comment 'start期间',
    end_period      varchar(8) comment 'end期间',
    no              int comment '序号',
    type            int comment '减免类型',
    total_balance   decimal(14, 2) default 0 comment '免征增值税项目销售额',
    actual_balance  decimal(14, 2) default 0 comment '实际扣除金额',
    total_tax       decimal(14, 2) default 0 comment '对应的进项税额',
    actual_tax      decimal(14, 2) default 0 comment '免税额',
    primary key (id)
);
create unique index UK_vatcutdeclarationextra_book on vat_cut_declaration_extra (account_book_id, start_period, end_period, type);

drop table if exists bank_account_detail_pabc;
create table bank_account_detail_pabc
(
    id                  BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create          datetime default current_timestamp comment '创建时间',
    gmt_modified        datetime default current_timestamp comment '修改时间',
    account_book_id     bigint comment '账套id',
    period varchar (8) comment '期间',
    receipt_url         varchar(255) comment '回单地址',
    acct_date           varchar(8) comment '主机记账日期',
    tx_time             varchar(6) comment '交易时间',
    host_trace          varchar(32) comment '主机流水号',
    detail_serial_no    varchar(19) comment '明细序号',
    buss_seq_no         varchar(32) comment '业务流水号',
    out_node            varchar(9) comment '付款方网点号',
    out_bank_no         varchar(16) comment '付款方联行号',
    out_bank_name       varchar(120) comment '付款行名称',
    out_acct_no         varchar(32) comment '付款方账号',
    out_acct_name       varchar(120) comment '付款方户名',
    ccy_code            varchar(3) comment '结算币种',
    tran_amount         varchar(15) comment '交易金额',
    in_node             varchar(9) comment '收款方网点号',
    in_bank_no          varchar(16) comment '收款方联行号',
    in_bank_name        varchar(120) comment '收款方行名',
    in_acct_no          varchar(32) comment '收款方账号',
    in_acct_name        varchar(120) comment '收款方户名',
    dc_flag             varchar(1) comment '借贷标志',
    abstract_str        varchar(120) comment '摘要，未翻译的摘要，如TRS',
    voucher_no          varchar(20) comment '凭证号',
    tran_fee            varchar(15) comment '手续费',
    post_fee            varchar(15) comment '邮电费',
    acct_balance        varchar(15) comment '账户余额',
    purpose             varchar(300) comment '用途，附言',
    abstract_str_desc   varchar(100) comment '中文摘要，AbstractStr的中文翻译',
    c_voucher_no        varchar(20) comment '',
    proxy_pay_acc       varchar(100) comment '代理人账号',
    proxy_pay_name      varchar(100) comment '代理人户名',
    proxy_pay_bank_name varchar(100) comment '代理人银行名称',
    tran_channel        varchar(32) comment '',
    tran_code           varchar(32) comment '',
    host_date           varchar(8) comment '主机日期',
    tran_seq_no         varchar(32) comment '',
    remark1             varchar(300) comment '备注1',
    remark2             varchar(300) comment '备注2',
    be_reverse_flag     varchar(32) comment '',
    seq_time            varchar(32) comment '',
    fee_code            varchar(32) comment '',
    primary key (id)
);
create index IDX_bank_account_detail_pabc on bank_account_detail_pabc (account_book_id, period);

drop table if exists bank_account_receipt_icbc;
create table bank_account_receipt_icbc
(
    id               BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create       datetime default current_timestamp comment '创建时间',
    gmt_modified     datetime default current_timestamp comment '修改时间',
    account          varchar(32) comment '账号',
    zip_file_name    varchar(255) comment '压缩包名',
    pdf_file_name    varchar(255) comment 'pdf文件名',
    receipt_date     varchar(8) comment '回单日期',
    receipt_no       varchar(32) comment '回单编号',
    receipt_url      varchar(255) comment '回单地址',
    primary key (id)
);

drop table if exists bank_account_receipt_pabc;
create table bank_account_receipt_pabc
(
    id               BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create       datetime default current_timestamp comment '创建时间',
    gmt_modified     datetime default current_timestamp comment '修改时间',
    account          varchar(32) comment '账号',
    acct_date        varchar(8) comment '主机记账日期',
    host_trace       varchar(32) comment '主机流水号',
    dc_flag          varchar(1) comment '借贷标志',
    abstract_str     varchar(120) comment '摘要，未翻译的摘要，如TRS',
    detail_serial_no varchar(19) comment '明细序号',
    receipt_url      varchar(255) comment '回单地址',
    primary key (id)
);

drop table if exists rubbish_fee;
create table rubbish_fee
(
    id              BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '帐套id',
    start_period    varchar(8) comment '起始期间',
    end_period      varchar(8) comment '结束期间',
    content         varchar(2048) comment '其他信息的json字符串',
    agent_name      varchar(10) comment '经办人名称',
    agent_id        varchar(20) comment '经办人身份证号',
    primary key (id)
);
create unique index UK_rubbish_fee on rubbish_fee (account_book_id, start_period, end_period);

drop table if exists bank_account_detail_bcm;
create table bank_account_detail_bcm
(
    id              BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '账套id',
    period varchar (8) comment '期间',
    receipt_url     varchar(255) comment '回单地址',
    wiped_sts       varchar(1) comment '抹账标志',
    txn_dt          varchar(8) comment '交易日期',
    txn_tme         varchar(6) comment '交易时间',
    bus_tp          varchar(60) comment '业务类型',
    jrnl_no         varchar(16) comment '流水号',
    jrnl_seq_no     varchar(4) comment '流水序号',
    acct_no         varchar(40) comment '账号',
    acct_nme        varchar(60) comment '户名',
    incm_expdt_flg  varchar(1) comment '收支标志',
    ccy             varchar(3) comment '币种',
    txn_amt         varchar(17) comment '交易金额',
    acct_bal        varchar(17) comment '账户余额',
    avl_bal         varchar(17) comment '可用余额',
    cntrp_acct_no   varchar(40) comment '对方账号',
    cntrp_acct_nme  varchar(60) comment '对方账户名称',
    cntrp_addr      varchar(60) comment '对方地址',
    cntrp_bank_no   varchar(12) comment '对方开户行行号',
    cntrp_bank_nme  varchar(60) comment '对方账户开户行行名',
    bv_code         varchar(3) comment '凭证类型',
    bv_no           varchar(35) comment '凭证号码',
    bv_nme          varchar(60) comment '凭证名称',
    bv_sign_dt      varchar(8) comment '凭证签发日期',
    pscpt           varchar(200) comment '附言',
    remrk           varchar(200) comment '备注',
    core_txn_cd     varchar(6) comment '核心交易码',
    primary key (id)
);
create index IDX_bank_account_detail_bcm on bank_account_detail_bcm (account_book_id, period);

drop table if exists bank_account_receipt_bcm;
create table bank_account_receipt_bcm
(
    id           BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create   datetime default current_timestamp comment '创建时间',
    gmt_modified datetime default current_timestamp comment '修改时间',
    account      varchar(32) comment '账号',
    jrnl_no      varchar(32) comment '会计流水号',
    jrnl_seq_no  varchar(4) comment '流水序号',
    receipt_date varchar(8) comment '回单日期',
    receipt_no   varchar(32) comment '回单编号',
    receipt_url  varchar(255) comment '回单地址',
    primary key (id)
);

/*风险测算表*/
drop table if exists risk;
create table risk
(
    id              bigint NOT NULL AUTO_INCREMENT,
    account_book_id bigint comment '账套ID',
    period varchar (6) comment '期间',
    content         VARCHAR comment '风险测算结果',
    result          int comment '检测结果 0：未测算，1：通过，2：未通过',
    primary key (id)
);
create unique index UK_risk on risk (account_book_id, period);
create index IDX_risk_book on risk (account_book_id);

drop table if exists property_tax;
create table property_tax
(
    id                  BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create          datetime default current_timestamp comment '创建时间',
    gmt_modified        datetime default current_timestamp comment '修改时间',
    account_book_id     bigint comment '帐套id',
    start_period        varchar(8) comment '起始期间',
    end_period          varchar(8) comment '结束期间',
    is_reduction        int comment '本期是否适用增值税小规模纳税人减征政策',
    reduction_per_stamp decimal(5, 2) comment '减征比例（%）',
    agent_name          varchar(10) comment '经办人名称',
    agent_id            varchar(20) comment '经办人身份证号',
    main_body           int comment '减征政策适用主体',
    primary key (id)
);
create unique index UK_property_tax on property_tax (account_book_id, start_period, end_period);

/*房产税——从价计税*/
drop table if exists property_tax_price;
create table property_tax_price
(
    id                     BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create             datetime default current_timestamp comment '创建时间',
    gmt_modified           datetime default current_timestamp comment '修改时间',
    account_book_id        bigint comment '帐套id',
    property_tax_id        bigint comment '房产税id',
    start_period           varchar(8) comment '起始期间',
    end_period             varchar(8) comment '结束期间',
    no                     varchar(32) comment '房产编号',
    origin_value           decimal(14, 4) comment '房产原值',
    rent_origin_value      decimal(14, 4) comment '其中：出租房产原值',
    rent_income            decimal(14, 4) comment '本期申报租金收入',
    taxation_ratio         decimal(14, 4) comment '计税比例',
    taxation_basis         decimal(14, 4) comment '计税依据',
    tax_item               varchar(32) comment '税目',
    tax_rate               decimal(14, 5) comment '税率',
    current_tax_pay        decimal(14, 2) comment '本期应纳税额',
    code_deduction         varchar(128) comment '本期减免性质代码',
    tax_deduction          decimal(14, 2) comment '减免税额',
    current_tax_paid       decimal(14, 2) comment '本期已缴税额',
    fill_refund_tax_amount decimal(14, 2) comment '本期应补（退）税额',
    primary key (id)
);

/*房产税——从租计税*/
drop table if exists property_tax_rent;
create table property_tax_rent
(
    id                     BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create             datetime default current_timestamp comment '创建时间',
    gmt_modified           datetime default current_timestamp comment '修改时间',
    account_book_id        bigint comment '帐套id',
    property_tax_id        bigint comment '房产税id',
    start_period           varchar(8) comment '起始期间',
    end_period             varchar(8) comment '结束期间',
    no                     varchar(32) comment '房产编号',
    total_area             decimal(14, 4) comment '合同出租总面积（平方米）*',
    total_income           decimal(14, 4) comment '合同租金总收入*',
    rent_income            decimal(14, 4) comment '本期申报租金收入',
    declaration_start_date varchar(10) comment '申报租金所属租赁期起*',
    declaration_end_date   varchar(10) comment '申报租金所属租赁期止*',
    contract_start_date    varchar(10) comment '合同约定租赁期起*',
    contract_end_date      varchar(10) comment '合同约定租赁期止*',
    taxation_basis         decimal(14, 4) comment '计税依据',
    tax_item               varchar(32) comment '税目',
    tax_rate               decimal(14, 5) comment '税率',
    current_tax_pay        decimal(14, 2) comment '本期应纳税额',
    code_deduction         varchar(128) comment '本期减免性质代码',
    tax_deduction          decimal(14, 2) comment '减免税额',
    current_tax_paid       decimal(14, 2) comment '本期已缴税额',
    fill_refund_tax_amount decimal(14, 2) comment '本期应补（退）税额',
    primary key (id)
);

/*房产税税源信息*/
drop table if exists property_tax_info;
create table property_tax_info
(
    id              BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '帐套id',
    no              varchar(32) comment '房产编号',
    code            varchar(32) comment '产权证书号',
    name            varchar(128) comment '房产名称',
    type            int comment '纳税人类型 1：产权所有人、2： 经营管理人、3：承典人、3：房屋代管人、4：房屋使用人、5：融资租赁承租人',
    purpose         int comment '房产用途 1：工业、2：商业及办公、3：住房、4：其他',
    date            varchar(10) comment '房产取得时间',
    covered_area    decimal(14, 2) comment '建筑面积(m²)',
    city            varchar(128) comment '"房屋坐落地址（行政区划）"',
    street          varchar(128) comment '"房屋坐落地址（所处街道）"',
    address         varchar(128) comment '房屋坐落详细地址',
    tax_office      varchar(128) comment '（科、分局）"',
    land_code       varchar(32) comment '房屋所在土地编号',
    primary key (id)
);

drop table if exists land_use_tax;
create table land_use_tax
(
    id                  BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create          datetime default current_timestamp comment '创建时间',
    gmt_modified        datetime default current_timestamp comment '修改时间',
    account_book_id     bigint comment '帐套id',
    start_period        varchar(8) comment '起始期间',
    end_period          varchar(8) comment '结束期间',
    is_reduction        int comment '本期是否适用增值税小规模纳税人减征政策',
    reduction_per_stamp decimal(5, 2) comment '减征比例（%）',
    content             varchar(2048) comment '其他信息的json字符串',
    agent_name          varchar(10) comment '经办人名称',
    agent_id            varchar(20) comment '经办人身份证号',
    main_body           int comment '减征政策适用主体',
    primary key (id)
);
create unique index UK_land_use_tax on land_use_tax (account_book_id, start_period, end_period);

/*诚征土地使用税税源信息*/
drop table if exists land_use_tax_info;
create table land_use_tax_info
(
    id                 BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create         datetime default current_timestamp comment '创建时间',
    gmt_modified       datetime default current_timestamp comment '修改时间',
    account_book_id    bigint comment '帐套id',
    no                 varchar(32) comment '土地编号',
    land_parcel_no     varchar(32) comment '宗地号',
    name               varchar(255) comment '土地名称',
    type               int comment '纳税人类型 1：产权所有人、2： 经营管理人、3：承典人、3：房屋代管人、4：房屋使用人、5：融资租赁承租人',
    social_credit_code varchar(20) comment '土地使用权人纳税人识别号（统一社会信用代码）',
    use_name           varchar(64) comment '土地使用权人名称',
    certificate_number varchar(32) comment '不动产权证号',
    land_nature        int comment '土地性质 1:国有、2：集体',
    purpose            int comment '土地用途 1：工业、2：商业及办公、3：住房、4：其他',
    date               varchar(10) comment '土地取得时间',
    get_way            int comment '土地取得方式* 1：划拨、2：出让、3：租赁、4：其他、5：转让',
    floor_space        decimal(14, 2) comment '占用土地面积(m²)*',
    city               varchar(32) comment '地坐落地址（行政区划）*',
    street             varchar(32) comment '土地坐落地址（所处街乡）*',
    address            varchar(64) comment '土地坐落详细地址*',
    tax_office         varchar(64) comment '主管税务所（科，分局）',
    primary key (id)
);

/*账套信息表2*/
drop table if exists taiji_info;
create table taiji_info
(
    id             bigint NOT NULL AUTO_INCREMENT,
    gmt_create     datetime default current_timestamp comment '创建时间',
    gmt_modified   datetime default current_timestamp comment '修改时间',
    econ_kind      varchar(255) comment '企业类型',
    regist_capi    varchar(255) comment '注册资本（金额数字+1个空格+万+货币单位）',
    econ_id        varchar(255) comment '企业id',
    belong_org     varchar(255) comment '所属工商局',
    status         varchar(10) comment '经营状态（旧字段，不建议使用）',
    term_start     varchar(10) comment '营业开始日期',
    format_name    varchar(255) comment '标准企业名称（清洗名称）',
    revoke_date    varchar(10) comment '吊销日期',
    end_date       varchar(10) comment '注销日期',
    reg_no         varchar(255) comment '企业注册号',
    econ_kind_code varchar(255) comment '企业类型代码',
    domain         varchar(255) comment '四级行业',
    category_new   varchar(10) comment '企业二级分类 0115601:企业;0115602:个体;0115603:农民专业合作社;0115699:其他类型0215601:社会团体;0215602:基金会;0215603:民办非企业单位;0215604:村民/居民委员会;0215605:宗教，工会等其他社会组织;0315601:事业单位;0315602:机关;0315603:其他机构编制;0434401:香港企业;0444602:澳门企业;0415803:台湾企业;0499904:国外企业;0515601:律所;0500099:其他组织机构;',
    address        varchar(255) comment '地址',
    org_no         varchar(255) comment '组织机构号',
    district_code  varchar(255) comment '地区代码',
    start_date     varchar(10) comment '成立日期',
    scope          VARCHAR comment '经营范围',
    name           varchar(255) comment '企业名称（国家公示）',
    credit_no      varchar(32) comment '统一社会信用代码',
    new_status     varchar(10) comment '经营状态（清洗后状态） 1：存续；2：注销；3：吊销；4：撤销，5：迁出，6：设立中，7：清算中，8：停业，9：其他',
    oper_name      varchar(32) comment '企业法定代表人',
    title          varchar(32) comment '公司代表人职务',
    check_date     varchar(10) comment '核准日期',
    actual_capi    varchar(255) comment '实缴资本',
    term_end       varchar(10) comment '营业结束日期',
    currency_unit  varchar(10) comment '货币单位',
    revoke_reason  varchar(255) comment '吊销原因',
    type_new       varchar(32) comment '企业大类 01-大陆企业 02-社会组织 03-机关及事业单位 04-港澳台及国外企业 05-律所及其他组织机构',
    primary key (id)
);
create unique index IDX_taiji_info_credit_no on taiji_info (credit_no);

/*太极塔股东信息表*/
drop table if exists taiji_partner;
create table taiji_partner
(
    id                bigint NOT NULL AUTO_INCREMENT,
    gmt_create        datetime default current_timestamp comment '创建时间',
    gmt_modified      datetime default current_timestamp comment '修改时间',
    credit_no         varchar(32) comment '统一社会信用代码',
    total_real_capi   varchar(255) comment '总实缴',
    name              varchar(255) comment '股东姓名',
    identify_no       varchar(255) comment '企业证照号',
    total_should_capi varchar(255) comment '总认缴',
    stock_percent     varchar(255) comment '股比',
    identify_type     varchar(255) comment '类型',
    stock_type        varchar(255) comment '股东类型',
    primary key (id)
);
create index IDX_taiji_partner_credit_no on taiji_partner (credit_no);

/*太极塔股东身份变更表*/
drop table if exists taiji_partner_change;
create table taiji_partner_change
(
    id             bigint NOT NULL AUTO_INCREMENT,
    gmt_create     datetime default current_timestamp comment '创建时间',
    gmt_modified   datetime default current_timestamp comment '修改时间',
    credit_no      varchar(32) comment '统一社会信用代码',
    change_item    varchar(255) comment '变更项目',
    type           varchar(255) comment '变更类型（默认全部），企业名称变更，企业类型变更 ，证照号变更 ，注册资金变更 ，地址变更 ，联系方式变更 ，经营范围变更 ，负责人变更 ，股东股权变更 ，人员变更 ，分支机构变更 ，隶属关系变更 ，期限变更 ，其他变更',
    change_date    varchar(255) comment '变更日期',
    before_content VARCHAR comment '变更前内容',
    after_content  VARCHAR comment '变更后内容',
    tag            varchar(255) comment '历史信息标签（历史信息/非历史信息）',
    primary key (id)
);
create index IDX_taiji_partner_change_credit_no on taiji_partner_change (credit_no);

drop table if exists text_message;
create table text_message
(
    id                 bigint not null auto_increment comment '主键id',
    gmt_create         datetime default current_timestamp comment '创建时间',
    gmt_modified       datetime default current_timestamp comment '修改时间',
    content            varchar(255) comment '短信内容',
    phone              varchar(11) comment '短信手机号码',
    verification_code  varchar(6) comment '短信验证码',
    social_credit_code varchar(64) comment '纳税人识别号（统一社会信用代码）',
    area_id            int comment '区域编号',
    primary key (id)
);

/*工商年报记录表*/
drop table if exists gsnb_record;
create table gsnb_record
(
    id               bigint not null auto_increment comment '主键id',
    gmt_create       datetime default current_timestamp comment '创建时间',
    gmt_modified     datetime default current_timestamp comment '修改时间',
    start_time       datetime comment '任务开始时间',
    account_book_id  bigint comment '账套id',
    account_id       bigint comment '操作人id',
    area_id          int comment '区域编号',
    action_id        int comment '操作编号',
    order_level      int(4) not null default 0,
    year             varchar(8) comment '期间',
    state            int comment '申报状态',
    result_message   varchar(255) comment '结果信息',
    result_url       varchar(255) comment '结果地址',
    result_image_url varchar(255) comment '结果图片地址',
    result_json      VARCHAR comment '结果数据json',
    tax_payer_phone  varchar(11) comment '手机号',
    ip               varchar(32) comment 'ip地址',
    primary key (id)
);
create unique index UK_gsnb_record on gsnb_record (account_book_id, year, action_id);

drop table if exists hsqj_record;
create table hsqj_record
(
    id               bigint not null auto_increment comment '主键id',
    gmt_create       datetime default current_timestamp comment '创建时间',
    gmt_modified     datetime default current_timestamp comment '修改时间',
    start_time       datetime comment '任务开始时间',
    account_book_id  bigint comment '账套id',
    account_id       bigint comment '操作人id',
    area_id          int comment '区域编号',
    action_id        int comment '操作编号',
    order_level      int(4) not null default 0,
    year             varchar(8) comment '期间',
    state            int comment '申报状态',
    pay_balance      decimal(14, 2) comment '申报税额',
    result_message   varchar(255) comment '结果信息',
    result_url       varchar(255) comment '结果地址',
    result_image_url varchar(255) comment '结果图片地址',
    result_json      varchar(255) comment '结果数据json',
    tax_payer_phone  varchar(11) comment '手机号',
    national_name    varchar(255) comment '税局登录名',
    ip               varchar(32) comment 'ip地址',
    primary key (id)
) comment = '汇算清缴记录表';
create unique index UK_hsqj_record on hsqj_record (account_book_id, year, action_id);

drop table if exists gsnb_record_count;
create table gsnb_record_count
(
    id              bigint not null auto_increment comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '账套id',
    year            varchar(8) comment '期间',
    info_state      int comment '采集状态',
    count_state     int comment '取数状态',
    report_state    int comment '申报状态',
    primary key (id)
);
create unique index UK_gsnb_record_count on gsnb_record_count (account_book_id, year);

drop table if exists hsqj_record_count;
create table hsqj_record_count
(
    id              bigint not null auto_increment comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '账套id',
    year            varchar(8) comment '期间',
    info_state      int comment '采集状态',
    balance_state   int comment '余额表上传状态',
    count_state     int comment '取数状态',
    report_state    int comment '申报状态',
    pay_state    int comment '扣款状态',
    check_state    int comment '检查状态',
    pay_balance     decimal(14, 2) comment '申报税额',
    primary key (id)
) comment = '汇算清缴统计表';
create unique index UK_hsqj_record_count on hsqj_record_count (account_book_id, year);

drop table if exists gsnb_qyjbxx;
create table gsnb_qyjbxx
(
    id                    bigint not null auto_increment comment '主键id',
    gmt_create            datetime default current_timestamp comment '创建时间',
    gmt_modified          datetime default current_timestamp comment '修改时间',
    account_book_id       bigint comment '账套id',
    year                  varchar(8) comment '年度',
    company_name          varchar(255) comment '企业名称',
    company_code          varchar(64) comment '统一社会信用代码/注册号',
    company_address       varchar(255) comment '企业通信地址',
    postal_code           varchar(16) comment '邮政编码',
    company_phone         varchar(16) comment '企业联系电话',
    company_email         varchar(32) comment '企业电子邮箱',
    business_scope        varchar(2048) comment '企业主营业务活动',
    person_num            int comment '从业人数',
    is_person_num         int comment '从业人数 是否公示',
    woman_num             int comment '女性从业人数',
    is_woman_num          int comment '女性从业人数 是否公示',
    business_status       varchar(16) comment '经营状态',
    company_holding       varchar(16) comment '企业控股情况',
    is_company_holding    int comment '企业控股情况 是否公示',
    is_sure               int comment '是否有对外担保',
    is_web                int comment '是否有网站或网店',
    is_holding            int comment '是否发生股东股权转让',
    is_buy                int comment '是否有投资信息或购买其他公司股权',
    is_loan               int comment '是否有贷款需求（不公示）',
    is_card               int comment '是否有预付卡',
    student_manager       int comment '其中高校毕业生人数 经营者',
    student_worker        int comment '其中高校毕业生人数 雇员',
    soldier_manager       int comment '其中退役士兵人数 经营者',
    soldier_worker        int comment '其中退役士兵人数 雇员',
    disabler_manager      int comment '其中残疾人人数 经营者',
    disabler_worker       int comment '其中残疾人人数 雇员',
    loser_manager         int comment '其中失业人员再就业人数 经营者',
    loser_worker          int comment '其中失业人员再就业人数 雇员',
    is_science            int comment '是否是科技企业',
    is_customs_enterprise int comment '是否是海关企业',
    primary key (id)
);
create unique index UK_gsnb_qyjbxx on gsnb_qyjbxx (account_book_id, year);

drop table if exists gsnb_tzsbxx;
create table gsnb_tzsbxx
(
    id                              bigint not null auto_increment comment '主键id',
    gmt_create                      datetime default current_timestamp comment '创建时间',
    gmt_modified                    datetime default current_timestamp comment '修改时间',
    account_book_id                 bigint comment '账套id',
    year                            varchar(8) comment '期间',
    all_num                         int comment '办理使用登记特种设备总台数',
    safe_num                        int comment '检验有效期内特种设备总台数',
    boiler_register_num             int comment '锅炉 办理使用登记特种设备台(套)数',
    pressure_vessel_register_num    int comment '压力容器(不含气瓶) 办理使用登记特种设备台(套)数',
    elevator_register_num           int comment '电梯 办理使用登记特种设备台(套)数',
    lift_machinery_register_num     int comment '起重机械 办理使用登记特种设备台(套)数',
    passenger_rope_way_register_num int comment '客运索道 办理使用登记特种设备台(套)数',
    play_facility_register_num      int comment '大型游乐设施 办理使用登记特种设备台(套)数',
    motor_vehicle_register_num      int comment '场(厂)内专用机动车辆 办理使用登记特种设备台(套)数',
    boiler_safe_num                 int comment '锅炉 检验有效期内特种设备总台(套)数',
    pressure_vessel_safe_num        int comment '压力容器(不含气瓶) 检验有效期内特种设备总台(套)数',
    elevator_safe_num               int comment '电梯 检验有效期内特种设备总台(套)数',
    lift_machinery_safe_num         int comment '起重机械 检验有效期内特种设备总台(套)数',
    passenger_rope_way_safe_num     int comment '客运索道 检验有效期内特种设备总台(套)数',
    play_facility_safe_num          int comment '大型游乐设施 检验有效期内特种设备总台(套)数',
    motor_vehicle_safe_num          int comment '场(厂)内专用机动车辆 检验有效期内特种设备总台(套)数',
    primary key (id)
);
create unique index UK_gsnb_tzsbxx on gsnb_tzsbxx (account_book_id, year);

drop table if exists gsnb_gdjczxx;
create table gsnb_gdjczxx
(
    id              bigint not null auto_increment comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '账套id',
    year            varchar(8) comment '期间',
    name            varchar(64) comment '股东姓名',
    type            varchar(32) comment '股东/发起人类型',
    need_pay_amount varchar(64) comment '认缴出资额(万元)',
    need_pay_date   varchar(10) comment '认缴出资时间',
    need_pay_way    varchar(255) comment '认缴出资方式',
    paid_amount     varchar(64) comment '实缴出资额(万元)',
    paid_date       varchar(10) comment '实缴出资时间',
    paid_way        varchar(255) comment '实缴出资方式',
    primary key (id)
);

drop table if exists gsnb_wzhwdxx;
create table gsnb_wzhwdxx
(
    id                                       bigint not null auto_increment comment '主键id',
    gmt_create                               datetime default current_timestamp comment '创建时间',
    gmt_modified                             datetime default current_timestamp comment '修改时间',
    account_book_id                          bigint comment '账套id',
    year                                     varchar(8) comment '期间',
    type                                     varchar(8) comment '网站或网店类型',
    name                                     varchar(255) comment '网站或网店名称',
    address                                  varchar(255) comment '网站或网店网址',
    net_trading_platform_name_or_icp_message varchar(255) comment '网店所在网络交易平台名称或网站ICP备案信息',
    primary key (id)
);

drop table if exists gsnb_gqbgxx;
create table gsnb_gqbgxx
(
    id              bigint not null auto_increment comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '账套id',
    year            varchar(8) comment '期间',
    name            varchar(255) comment '股东',
    before_percent  varchar(64) comment '变更前股权比例',
    after_percent   varchar(64) comment '变更后股',
    change_date     varchar(10) comment '股权变更',
    primary key (id)
);

drop table if exists gsnb_dwtzxx;
create table gsnb_dwtzxx
(
    id                 bigint not null auto_increment comment '主键id',
    gmt_create         datetime default current_timestamp comment '创建时间',
    gmt_modified       datetime default current_timestamp comment '修改时间',
    account_book_id    bigint comment '账套id',
    year               varchar(8) comment '期间',
    name               varchar(255) comment '投资设立企业或购买股权企业名称',
    social_credit_code varchar(255) comment '统一社会信用代码/注册号',
    primary key (id)
);

drop table if exists gsnb_zczkxx;
create table gsnb_zczkxx
(
    id                   bigint not null auto_increment comment '主键id',
    gmt_create           datetime default current_timestamp comment '创建时间',
    gmt_modified         datetime default current_timestamp comment '修改时间',
    account_book_id      bigint comment '账套id',
    year                 varchar(8) comment '期间',
    total_assets         varchar(64) comment '资产总额',
    is_total_assets      int comment '是否公示 资产总额 0:否；1：是',
    total_owners         varchar(64) comment '所有者权益合计',
    is_total_owners      int comment '是否公示 所有者权益合计 0:否；1：是',
    total_liability      varchar(64) comment '负债总额',
    is_total_liability   int comment '是否公示 负债总额 0:否；1：是',
    total_income         varchar(64) comment '营业总收入',
    is_total_income      int comment '是否公示 营业总收入 0:否；1：是',
    total_main_income    varchar(64) comment '其中：主营业务收入',
    is_total_main_income int comment '是否公示 其中：主营业务收入 0:否；1：是',
    total_profit         varchar(64) comment '利润总额',
    is_total_profit      int comment '是否公示 利润总额 0:否；1：是',
    net_profit           varchar(64) comment '净利润',
    is_net_profit        int comment '是否公示 净利润 0:否；1：是',
    total_tax_paid       varchar(64) comment '净利润',
    is_total_tax_paid    int comment '是否公示 净利润 0:否；1：是',
    primary key (id)
);
create unique index UK_gsnb_zczkxx on gsnb_zczkxx (account_book_id, year);

drop table if exists gsnb_dwdbxx;
create table gsnb_dwdbxx
(
    id               bigint not null auto_increment comment '主键id',
    gmt_create       datetime default current_timestamp comment '创建时间',
    gmt_modified     datetime default current_timestamp comment '修改时间',
    account_book_id  bigint comment '账套id',
    year             varchar(8) comment '期间',
    creditor         varchar(64) comment '债权人',
    debtor           varchar(64) comment '债务人',
    type             varchar(8) comment '主债权种类',
    amount           varchar(64) comment '主债权数额(万元)',
    start_date       varchar(10) comment '履行债务的期限起',
    end_date         varchar(10) comment '履行债务的期限起',
    guarantee_period varchar(10) comment '保证的期间',
    guarantee_mode   varchar(10) comment '保证的方式',
    is_public        int comment '是否公示 0:否，1：是',
    guarantee_scope  varchar(255) comment '保证担保的范围',
    primary key (id)
);

drop table if exists gsnb_kjqyxxcj;
create table gsnb_kjqyxxcj
(
    id              bigint not null auto_increment comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '账套id',
    year            varchar(8) comment '期间',
    technology_num  varchar(64) comment '门从事科技研发人员（单位：人）',
    technology_fund varchar(255) comment '年度专门用于科技研发投入资金 (单位:万元)',
    primary key (id)
);
create unique index UK_gsnb_kjqyxxcj on gsnb_kjqyxxcj (account_book_id, year);

drop table if exists gsnb_djxx;
create table gsnb_djxx
(
    id                         bigint not null auto_increment comment '主键id',
    gmt_create                 datetime default current_timestamp comment '创建时间',
    gmt_modified               datetime default current_timestamp comment '修改时间',
    account_book_id            bigint comment '账套id',
    year                       varchar(8) comment '期间',
    num                        int comment '中共党员(包括预备党员)人数',
    party_organ_system         varchar(16) comment '党组织建制',
    is_build_party             int comment '是否建立党组织',
    is_party_member            int comment '法定代表人是否为党员',
    is_secretary               int comment '法定代表人是否为党组织书记',
    party_name                 varchar(64) comment '党组织名称',
    party_code                 varchar(64) comment '党组织代码',
    party_social_code          varchar(64) comment '党组织主管/隶属单位统一社会信息代码',
    party_builder_name         varchar(64) comment '党组织建制负责人姓名',
    email                      varchar(128) comment '电子邮箱',
    office_telephone           varchar(64) comment '办公电话',
    mobile_phone               varchar(64) comment '手机',
    party_affiliate_unit       int comment '党组织主管/隶属单位',
    party_build_way            int comment '党组织组建方式',
    party_annual_new_member    varchar(255) comment '本年度发展新党员',
    party_form_time            varchar(10) comment '党组织组建时间',
    team_num                   int comment '团员人数',
    team_build                 int comment '团组织建制',
    is_operator_team           int comment '经营者是否为团员',
    is_operator_team_secretary int comment '经营者是否为团组织书记',
    primary key (id)
);
create unique index UK_gsnb_djxx on gsnb_djxx (account_book_id, year);

drop table if exists gsnb_djxx_ln;
create table gsnb_djxx_ln
(
    id                          bigint not null auto_increment comment '主键id',
    gmt_create                  datetime default current_timestamp comment '创建时间',
    gmt_modified                datetime default current_timestamp comment '修改时间',
    account_book_id             bigint comment '账套id',
    year                        varchar(8) comment '期间',
    is_party_member             int comment '法定代表人是否为党员',
    mobile_phone                varchar(64) comment '手机',
    other_work                  int comment '其他职务',
    party_total_num             int comment '党员总数',
    register_party_num          int comment '在册党员数',
    not_transfer_party_num      int comment '未转入党组织关系人数',
    annual_develop_members_num  int comment '本年度发展党员数',
    activist_now_Num            int comment '现有入党积极分子数量',
    is_build_party              int comment '企业是否建立党组织',
    not_build_reason            varchar(128) comment '未建立党组织主要原因',
    build_party_time            varchar(10) comment '拟建立党组织时间',
    party_name                  varchar(64) comment '党组织名称',
    is_new_build                int comment '是否为本年度新建立党组织',
    approval_organ              varchar(64) comment '批准党组织成立的机构',
    party_organ_system          int comment '党组织建制',
    party_build_way             int comment '组建方式 ',
    party_committees_num        int comment '党委数（不包含其分公司及分支机构）',
    party_branch_total_num      int comment '党总支数（不包含其分公司及分支机构）',
    party_branch_num            int comment '党支部数（不包含其分公司及分支机构）',
    party_work_area             varchar(64) comment '党组织活动场所面积（单位：平方米）',
    is_secretary                int comment '法定代表人（经营者）是否党组织书记',
    party_secretary_name        varchar(64) comment '党组织书记姓名',
    telephone                   varchar(64) comment '固定电话',
    email                       varchar(128) comment '电子邮箱',
    is_have_party_activity_fund int comment '有无党组织活动经费',
    is_implement_standard       int comment '税前列支5%标准是否落实',
    party_honor_over_province   varchar(255) comment '党组织获得省级以上荣誉',
    primary key (id)
);
create unique index UK_gsnb_djxx_ln on gsnb_djxx_ln (account_book_id, year);

drop table if exists gsnb_tjxx;
create table gsnb_tjxx
(
    id                         bigint not null auto_increment comment '主键id',
    gmt_create                 datetime default current_timestamp comment '创建时间',
    gmt_modified               datetime default current_timestamp comment '修改时间',
    account_book_id            bigint comment '账套id',
    year                       varchar(8) comment '期间',
    is_build_team_organization int comment '是否建立团组织',
    team_charge_name           varchar(64) comment '团组织负责人姓名',
    gender                     int comment '性别',
    office_phone               varchar(64) comment '办公电话',
    mobile_phone               varchar(64) comment '手机',
    team_affiliate_unit        int comment '团组织主管/隶属单位',
    team_build_situation       int comment '团组织建制情况',
    team_build_way             int comment '团组织组建方式',
    team_number                int comment '团员人数（含预备团员）',
    youth_under_number         int comment '35岁以下青年人数',
    team_build_time            varchar(20) comment '团组织组建时间',
    primary key (id)
);
create unique index UK_gsnb_tjxx on gsnb_tjxx (account_book_id, year);

drop table if exists gsnb_czdwxkz;
create table gsnb_czdwxkz
(
    id              bigint      not null auto_increment comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '账套id',
    year            varchar(8) comment '期间',
    name            varchar(64) not null comment '许可名称',
    no              varchar(64) not null comment '许可编号',
    from_date       varchar(20) comment '有效期自',
    to_date         varchar(20) comment '有效期至',
    primary key (id)
);
drop table if exists gsnb_ymscxkz;
create table gsnb_ymscxkz
(
    id              bigint      not null auto_increment comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '账套id',
    year            varchar(8) comment '期间',
    name            varchar(64) not null comment '许可名称',
    no              varchar(64) not null comment '许可编号',
    from_date       varchar(20) comment '有效期自',
    to_date         varchar(20) comment '有效期至',
    primary key (id)
);
drop table if exists gsnb_tzsbscxkz;
create table gsnb_tzsbscxkz
(
    id              bigint      not null auto_increment comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '账套id',
    year            varchar(8) comment '期间',
    name            varchar(64) not null comment '许可名称',
    no              varchar(64) not null comment '许可编号',
    from_date       varchar(20) comment '有效期自',
    to_date         varchar(20) comment '有效期至',
    primary key (id)
);
drop table if exists gsnb_sbxx;
create table gsnb_sbxx
(
    id                             bigint not null auto_increment comment '主键id',
    gmt_create                     datetime default current_timestamp comment '创建时间',
    gmt_modified                   datetime default current_timestamp comment '修改时间',
    account_book_id                bigint comment '账套id',
    year                           varchar(8) comment '期间',
    pension_num                    int comment '城镇职工基本养老保险(单位：人)',
    medical_num                    int comment '职工基本医疗保险(单位：人)',
    maternity_insurance_num        int comment '生育保险(单位：人)',
    unemployment_num               int comment '失业保险(单位：人)',
    injury_insurance_num           int comment '工伤保险(单位：人)',
    pension_base                   varchar(64) comment '单位缴费基数 单位参加城镇职工基本养老保险缴费基数',
    unemployment_base              varchar(64) comment '单位缴费基数 单位参加失业保险缴费基数',
    medical_base                   varchar(64) comment '单位缴费基数 单位参加职工基本医疗保险缴费基数',
    injury_insurance_base          varchar(64) comment '单位缴费基数 单位参加工伤保险缴费基数',
    maternity_insurance_base       varchar(64) comment '单位缴费基数 单位参加生育保险缴费基数',
    is_base                        int comment '单位缴费基数 是否公示 0：否；1：是',
    pension_paid                   varchar(64) comment '本期实际缴费金额 参加城镇职工基本养老保险本期实际缴费金额',
    unemployment_paid              varchar(64) comment '本期实际缴费金额 参加失业保险本期实际缴费金额',
    medical_paid                   varchar(64) comment '本期实际缴费金额 参加职工基本医疗保险本期实际缴费金额',
    injury_insurance_paid          varchar(64) comment '本期实际缴费金额 参加工伤保险本期实际缴费金额',
    maternity_insurance_paid       varchar(64) comment '本期实际缴费金额 单位参加生育保险缴费基数',
    is_paid                        int comment '本期实际缴费金额 是否公示 0：否；1：是',
    pension_arrears                varchar(64) comment '单位累计欠缴金额 单位参加城镇职工基本养老保险累计欠缴金额',
    unemployment_arrears           varchar(64) comment '单位累计欠缴金额 单位参加失业保险累计欠缴金额',
    medical_arrears                varchar(64) comment '单位累计欠缴金额 单位参加职工基本医疗保险累计欠缴金额',
    injury_insurance_arrears       varchar(64) comment '单位累计欠缴金额 单位参加工伤保险累计欠缴金额',
    maternity_insurance_arrears    varchar(64) comment '单位累计欠缴金额 单位参加生育保险累计欠缴金额',
    is_arrears                     int comment '单位累计欠缴金额 是否公示 0：否；1：是',
    pension_pay_num                varchar(64) comment '全年累计缴费人次（各月人数之和） 单位参加城镇职工基本养老保险全年累计缴费人次',
    unemployment_pay_num           varchar(64) comment '全年累计缴费人次（各月人数之和） 单位参加失业保险全年累计缴费人次',
    medical_pay_num                varchar(64) comment '全年累计缴费人次（各月人数之和） 单位参加职工基本医疗保险全年累计缴费人次',
    injury_insurance_pay_num       varchar(64) comment '全年累计缴费人次（各月人数之和） 单位参加工伤保险全年累计缴费人次',
    maternity_insurance_pay_num    varchar(64) comment '全年累计缴费人次（各月人数之和） 单位参加生育保险全年累计缴费人次',
    pension_person_num             varchar(64) comment '期末参保人数（12月份人数） 12月份参加城镇职工基本养老保险人数',
    unemployment_person_num        varchar(64) comment '期末参保人数（12月份人数） 12月份参加失业保险人数',
    medical_person_num             varchar(64) comment '期末参保人数（12月份人数） 12月份参加职工基本医疗保险人数',
    injury_insurance_person_num    varchar(64) comment '期末参保人数（12月份人数） 12月份参加工伤保险人数',
    maternity_insurance_person_num varchar(64) comment '期末参保人数（12月份人数） 12月份参加生育保险人数',
    primary key (id)
);
create unique index UK_gsnb_sbxx on gsnb_sbxx (account_book_id, year);

drop table if exists gsnb_tjsx;
create table gsnb_tjsx
(
    id              bigint      not null auto_increment comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp  comment '修改时间',
    account_book_id bigint comment '账套id',
    year            varchar(8) comment '期间',
    opening_time    varchar(20) comment '开业时间',
    institutional_situation int  comment '执行企业会计准则制度情况',
    belong_situation int comment '国有控股企业隶属情况',
    primary key (id)
);
create unique index UK_gsnb_tjsx on gsnb_tjsx (account_book_id, year);

drop table if exists account_party_index;
create table account_party_index
(
    id              bigint not null auto_increment comment '主键id',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    party_index     int comment '服务器编号',
    party_index_url varchar(255) comment '服务器地址',
    primary key (id)
);

drop table if exists account_token;
create table account_token
(
    id           bigint not null auto_increment comment '主键id',
    gmt_create   datetime default current_timestamp comment '创建时间',
    gmt_modified datetime default current_timestamp comment '修改时间',
    account_id   bigint comment '用户id',
    token        varchar(32) comment 'token',
    primary key (id)
);
create index IDX_account_token on account_token (gmt_create);

drop table if exists tax_national_name;
create table tax_national_name
(
    id            BIGINT NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create    datetime default current_timestamp comment '创建时间',
    gmt_modified  datetime default current_timestamp comment '修改时间',
    end_time      datetime default current_timestamp comment '锁定结束时间',
    national_name varchar(255) comment '税局登录名',
    state         int comment '状态',
    primary key (id)
);
create index IDX_tax_national_name on tax_national_name (end_time, state);

drop table if exists tax_record_log;
create table tax_record_log
(
    id               bigint not null auto_increment comment '主键id',
    gmt_create       datetime default current_timestamp comment '创建时间',
    gmt_modified     datetime default current_timestamp comment '修改时间',
    version          int      default 0 comment '版本号',
    start_time       datetime comment '任务开始时间',
    account_book_id  bigint comment '账套id',
    account_id       bigint comment '操作人id',
    tax_type         int comment '税种',
    area_id          int comment '区域编号',
    action_id        int comment '操作编号',
    source_id        int comment '申报源（1：电子税务局 2：自然人系统）',
    worker_index     int(4) not null default 0,
    order_level      int(4) not null default 0,
    period varchar (8) comment '期间',
    tax_state        int comment '申报状态',
    pay_balance      decimal(14, 2) comment '申报税额',
    result_message   varchar(255) comment '结果信息',
    result_url       varchar(255) comment '结果地址',
    result_image_url varchar(255) comment '结果图片地址',
    is_last_month    int      default 0 comment '个税是否按上月申报',
    tax_payer_phone  varchar(11) comment '手机号',
    national_name    varchar(255) comment '税局登录名',
    ip               varchar(32) comment 'ip地址',
    uri              varchar(255) comment 'uri地址',
    user_agent       varchar(255) comment 'User-Agent',
    primary key (id)
);

drop table if exists rpa_cookie_task;
create table rpa_cookie_task
(
    id                bigint not null auto_increment comment '主键id',
    gmt_create        datetime default current_timestamp comment '创建时间',
    gmt_modified      datetime default current_timestamp comment '修改时间',
    version           int default 0 comment '版本号',
    env               varchar(8) comment '环境',
    ip                varchar(32) comment 'ip地址',
    sms_code          varchar(6) comment '短信验证码',
    task_id           varchar(255) comment '异步任务id',
    account_book_id   bigint comment '账套id',
    account_book_name varchar(64) comment '账套名称',
    tax_code          varchar(64) comment '税号',
    national_name     varchar(255) comment '电子税务局登录名',
    national_pwd      varchar(255) comment '电子税局密码',
    tax_payer_phone   varchar(255) comment '办税人手机号',
    tax_authorities   varchar(255) comment '主管税务机关',
    province          varchar(32) comment '所属省份',
    city              varchar(32) comment '所属城市',
    type_auth         int default 0 comment '验证：方式 0：网页 1：APP',
    area_id           int comment '区域编号',
    action_id         int comment '操作编号',
    state             int comment '状态',
    result_message    varchar(255) comment '结果信息',
    login_way         int comment '电子税务局登录方式 1:旧版登录；2：新版登录；3：代理登录',
    social_credit_code_agent varchar(255) comment '代理：税号',
    source            int default 0 comment '来源 0：财务软件 1：小程序 2：app',
    primary key (id)
);

drop table if exists rpa_cookie_state;
create table rpa_cookie_state
(
    id                 bigint not null auto_increment comment '主键id',
    gmt_create         datetime default current_timestamp comment '创建时间',
    gmt_modified       datetime default current_timestamp comment '修改时间',
    social_credit_code varchar(64) comment '纳税人识别号（统一社会信用代码）',
    login_way          int default 2 comment '电子税务局登录方式 1:旧版登录；2：新版登录；3：代理登录',
    open_state         int default 0 comment '开通状态 0：未开通 1：已开通',
    state              int      default 1 comment '状态0：无效 1：有效',
    result_message     varchar(255) comment '结果信息',
    auth_time          datetime comment '验证时间',
    primary key (id)
);
create index IDX_rpa_cookie_state_code_way ON rpa_cookie_state(social_credit_code, login_way);

drop table if exists old_import_record;
create table old_import_record
(
    id                bigint not null auto_increment comment '主键id',
    gmt_create        datetime default current_timestamp comment '创建时间',
    gmt_modified      datetime default current_timestamp comment '修改时间',
    account_id        bigint comment '用户id',
    import_status     bigint comment '导账状态',
    old_import_type    int comment '导账类型',
    middle_data_url   varchar(255) comment '中间数据url',
    final_data_url    varchar(255) comment '最终数据url',
    result_message    varchar(255) comment '结果信息',
    account_book_name varchar(255) comment '账套名称',
    primary key (id)
);

drop table if exists old_import;
create table old_import
(
    id                 bigint not null auto_increment comment '主键id',
    account_id         bigint comment '用户id',
    gmt_create         datetime default current_timestamp comment '导账创建时间',
    gmt_modified       datetime default current_timestamp comment '导账修改时间',
    old_import_type    int comment '导账类型',
    balance_url        varchar(255) comment '余额数据url',
    voucher_url        varchar(255) comment '凭证数据url',
    final_data_url     varchar(255) comment '最终数据url',
    is_del             int default 0 comment '是否删除',
    book_name          varchar(255) comment '账套名称',
    start_period       varchar(255) comment '启用期间',
    accounting_system  varchar(255) comment '会计制度',
    addtax             varchar(255) comment '纳税人资格',
    uuid               varchar(255) comment '前后端交互用的key',
    voucher_min_period varchar(255) comment '凭证最小区间',
    result_message     varchar(255) comment '结果信息',
    upload_status      int default 1 comment '上传状态 1:未开始 2:等待中 3:运行中 4:已完成 5:异常',
    match_status       int default 1 comment '匹配状态 1:未开始 2:等待中 3:运行中 4:已完成 5:异常',
    import_status      int default 1 comment '导入状态 1:未开始 2:等待中 3:运行中 4:已完成 5:异常',
    check_status       int default 1 comment '检查状态 1:未开始 2:等待中 3:运行中 4:已完成 5:异常',
    account_book_id    bigint comment '账套id',
    primary key (id)
);
create index IDX_old_import on old_import (uuid);

drop table if exists old_import_init_balance;
create table old_import_init_balance
(
    id               bigint not null auto_increment comment '主键id',
    old_import_id    bigint comment 'old_import导账记录表的id',
    gmt_create       datetime default current_timestamp comment '导账创建时间',
    gmt_modified     datetime default current_timestamp comment '导账修改时间',
    no               varchar(255) comment '科目编码',
    name             varchar(255) comment '科目名称',
    measuring_unit   varchar(255) comment '计量单位',
    model           varchar(256) comment '规格型号',
    curr_symbol      varchar(255) comment '外币符号',
    ass_name         varchar(255) comment '辅助核算名称',
    item_name        varchar(255) comment '项目名称',
    customer_name    varchar(255) comment '客户名称',
    provider_name    varchar(255) comment '供应商名称',
    department_name  varchar(255) comment '部门',
    employee_name    varchar(255) comment '员工名称',
    inventory_name   varchar(255) comment '存货',
    jfljbalance      decimal(14, 2) comment '本年累计借方发生额',
    jfljbalance_org  decimal(14, 2) comment '本年累计借方发生额原币',
    jfljnumber       decimal(16, 4) comment '本年累计借方数量',
    dfljbalance      decimal(14, 2) comment '本年累计贷方发生额',
    dfljbalance_org  decimal(16, 4) comment '本年累计贷方发生额原币',
    dfljnumber       decimal(16, 4) comment '本年累计贷方数量',
    endjfbalance     decimal(16, 4) comment '期末借方余额',
    endjfbalance_org decimal(16, 4) comment '期末借方余额原币',
    endjfnumber      decimal(16, 4) comment '期末借方数量',
    enddfbalance     decimal(16, 4) comment '期末贷方余额',
    enddfbalance_org decimal(16, 4) comment '期末贷方余额原币',
    enddfnumber      decimal(16, 4) comment '期末贷方数量',
    primary key (id)
);
create index IDX_old_import_init_balance on old_import_init_balance (old_import_id);

drop table if exists old_import_end_balance;
create table old_import_end_balance
(
    id               bigint not null auto_increment comment '主键id',
    old_import_id    bigint comment 'old_import导账记录表的id',
    gmt_create       datetime default current_timestamp comment '导账创建时间',
    gmt_modified     datetime default current_timestamp comment '导账修改时间',
    no               varchar(255) comment '科目编码',
    name             varchar(255) comment '科目名称',
    measuring_unit   varchar(255) comment '计量单位',
    model           varchar(256) comment '规格型号',
    curr_symbol      varchar(255) comment '外币符号',
    ass_name         varchar(255) comment '辅助核算名称',
    item_name        varchar(255) comment '项目名称',
    customer_name    varchar(255) comment '客户名称',
    provider_name    varchar(255) comment '供应商名称',
    department_name  varchar(255) comment '部门',
    employee_name    varchar(255) comment '员工名称',
    inventory_name   varchar(255) comment '存货',
    jfljbalance      decimal(14, 2) comment '本年累计借方发生额',
    jfljbalance_org  decimal(14, 2) comment '本年累计借方发生额原币',
    jfljnumber       decimal(16, 4) comment '本年累计借方数量',
    dfljbalance      decimal(14, 2) comment '本年累计贷方发生额',
    dfljbalance_org  decimal(16, 4) comment '本年累计贷方发生额原币',
    dfljnumber       decimal(16, 4) comment '本年累计贷方数量',
    endjfbalance     decimal(16, 4) comment '期末借方余额',
    endjfbalance_org decimal(16, 4) comment '期末借方余额原币',
    endjfnumber      decimal(16, 4) comment '期末借方数量',
    enddfbalance     decimal(16, 4) comment '期末贷方余额',
    enddfbalance_org decimal(16, 4) comment '期末贷方余额原币',
    enddfnumber      decimal(16, 4) comment '期末贷方数量',
    primary key (id)
)comment = '导账期末余额数据表';
create index IDX_old_import_end_balance on old_import_end_balance (old_import_id);

drop table if exists old_import_voucher;
create table old_import_voucher
(
    id               bigint not null auto_increment comment '主键id',
    old_import_id    bigint comment 'old_import导账记录表的id',
    gmt_create       datetime default current_timestamp comment '导账创建时间',
    gmt_modified     datetime default current_timestamp comment '导账修改时间',
    account_book_id  bigint comment '账套id',
    voucher_date_str varchar(255) comment '凭证日期字符串',
    voucher_no       varchar(255) comment '凭证编号',
    summary          varchar(1024) comment '摘要',
    subject_no       varchar(255) comment '科目编码',
    long_text        varchar(255) comment '科目长名称',
    ass_name         varchar(255) comment '辅助核算名称',
    item             varchar(255) comment '项目',
    customer         varchar(255) comment '客户',
    provider         varchar(255) comment '供应商',
    department       varchar(255) comment '部门',
    employee         varchar(255) comment '员工',
    inventory        varchar(255) comment '存货',
    jf_balance       decimal(16, 4) comment '借方金额',
    df_balance       decimal(16, 4) comment '贷方金额',
    number           decimal(16, 4) comment '数量',
    measuring_unit   varchar(255) comment '计量单位（可空）',
    model           varchar(256) comment '规格型号',
    unit_price       decimal(16, 4) comment '单价',
    money_org        decimal(16, 4) comment '外币金额',
    curr_symbol      varchar(255) comment '外币符号（可空）',
    exchange_rate    decimal(16, 4) comment '汇率',
    written_person   varchar(255) comment '制单人',
    audit_person     varchar(255) comment '审核人',
    accessory_no     varchar(255) comment '附件数',
    primary key (id)
);
create index IDX_old_import_voucher on old_import_voucher (old_import_id);

drop table if exists old_import_subject;
create table old_import_subject
(
    id                  bigint       not null auto_increment comment '主键id',
    old_import_id       bigint comment 'old_import导账记录表的id',
    gmt_create          datetime default current_timestamp comment '导账创建时间',
    gmt_modified        datetime default current_timestamp comment '导账修改时间',
    no                  varchar(255) not null comment '科目编码',
    pno                 varchar(255) comment '父科目编码',
    text                varchar(255) comment '科目名称',
    long_text           varchar(255) comment '长名称',
    level               int comment '科目级次',
    is_init             int comment '是否置顶',
    leaf                int comment '是否叶子节点，0：否，1：是',
    measuring_unit      varchar(255) comment '计量单位（可空）',
    curr_symbol         varchar(255) comment '外币符号(可空)',
    dim_account_type    varchar(255) comment '辅助核算（可空）',
    is_measuring_unit   int comment '是否数量科目',
    is_curr_symbol      int comment '是否外币科目',
    is_dim_account_type int comment '是否辅助核算科目',
    match_subject_id    bigint comment '已匹配的科目Id',
    is_old              int comment '区分新旧科目',
    primary key (id)
);
create index IDX_old_import_subject on old_import_subject (old_import_id);

drop table if exists inventory_use;
create table inventory_use
(
    id                 bigint not null auto_increment comment '主键id',
    gmt_create         datetime default current_timestamp comment '创建时间',
    gmt_modified       datetime default current_timestamp comment '修改时间',
    account_book_id    bigint comment '账套id',
    dim_inventory_id   bigint comment '存货辅助核算id',
    dim_inventory_type int comment '存货类型',
    period varchar (6) comment '期间',
    balance            decimal(14, 2) comment '金额',
    number             decimal(16, 4) comment '数量',
    unit_price         decimal(16, 4) comment '单价',
    primary key (id)
);
create index IDX_inventory_use on inventory_use (account_book_id, period, dim_inventory_id);

drop table if exists inventory_estimate;
create table inventory_estimate
(
    id                 bigint not null auto_increment comment '主键id',
    gmt_create         datetime default current_timestamp comment '创建时间',
    gmt_modified       datetime default current_timestamp comment '修改时间',
    account_book_id    bigint comment '账套id',
    dim_inventory_id   bigint comment '存货辅助核算id',
    dim_inventory_type int comment '存货类型',
    period varchar (6) comment '期间',
    balance            decimal(14, 2) comment '金额',
    number             decimal(16, 4) comment '数量',
    unit_price         decimal(16, 4) comment '单价',
    voucher_id         bigint comment '暂估凭证id',
    primary key (id)
);
create index IDX_inventory_estimate on inventory_estimate (account_book_id, period, dim_inventory_type, dim_inventory_id);

drop table if exists inventory_back;
create table inventory_back
(
    id bigint not null auto_increment comment '主键id',
    gmt_create datetime default current_timestamp comment '创建时间',
    gmt_modified datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '账套id',
    dim_inventory_id bigint comment '存货辅助核算id',
    dim_inventory_type int comment '存货类型',
    period varchar(6) comment '期间',
    balance decimal(14, 2) comment '金额',
    number decimal(16, 4) comment '数量',
    unit_price decimal(16, 4) comment '单价',
    voucher_id bigint comment '回冲凭证id',
    primary key (id)
);
create index IDX_inventory_back on inventory_back (account_book_id, period, dim_inventory_type, dim_inventory_id);

drop table if exists inventory_cost;
create table inventory_cost
(
    id               bigint not null auto_increment comment '主键id',
    gmt_create       datetime default current_timestamp comment '创建时间',
    gmt_modified     datetime default current_timestamp comment '修改时间',
    account_book_id  bigint comment '账套id',
    dim_inventory_id bigint comment '存货辅助核算id',
    period varchar (6) comment '期间',
    number           decimal(16, 4) comment '完工数量',
    value            decimal(14, 2) comment '完工产值',
    value_per        decimal(16, 4) comment '完工产值百分比',
    primary key (id)
);
create index IDX_inventory_cost on inventory_cost (account_book_id, period, dim_inventory_id);


drop table if exists inventory_subject_setting;
create table inventory_subject_setting
(
    id                       bigint not null auto_increment comment '主键id',
    gmt_create               datetime default current_timestamp comment '创建时间',
    gmt_modified             datetime default current_timestamp comment '修改时间',
    account_book_id          bigint comment '账套id',
    production_cost_zjcl_id  bigint comment '生产成本直接材料',
    production_cost_zjrg_id  bigint comment '生产成本直接人工',
    production_cost_zzfy_id  bigint comment '生产成本制造费用',
    zyywsr_subject_id        bigint comment '主营业务收入',
    zg_subject_id            varchar(64) comment '暂估科目',
    cb_subject_id            bigint comment '成本科目',
    primary key (id)
);
create index IDX_inventory_subject_setting on inventory_subject_setting (account_book_id);


/*新操作记录表*/
drop table if exists new_operation_log;
create table new_operation_log
(
    id              bigint NOT NULL AUTO_INCREMENT comment '主键',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    login_id        varchar(64) comment '操作账号',
    operate_type    int comment '操作类型',
    accounter_id    bigint comment '操作人',
    accounter_no    varchar(64) comment '员工级次编码',
    accounter_name  varchar(64) comment '员工姓名',
    remark          varchar(255) comment '操作记录（备注）',
    ip              varchar(64) comment 'ip地址',
    primary key (id)
);
create index IDX_newoperationlog_accounterno on new_operation_log (accounter_no);
create index IDX_newoperationlog_operatetype on new_operation_log (operate_type);
create index IDX_newoperationlog_gmt_modified on new_operation_log (gmt_modified);

drop table if exists bank_task;
create table bank_task
(
    id              bigint not null auto_increment comment '主键id',
    gmt_create      datetime    default current_timestamp comment '创建时间',
    gmt_modified    datetime    default current_timestamp  comment '修改时间',
    version         int         default 0 comment '版本号',
    start_time      datetime comment '任务开始时间',
    period varchar (8) comment '期间',
    account_book_id bigint comment '账套id',
    bank_type int comment '银行类型',
    user_name varchar(32) comment '用户名',
    password varchar(32) comment '密码',
    user_no varchar(32) comment '操作员号',
    account varchar(32) comment '银行账号',
    account_name varchar(255) comment '户名',
    state int comment '获取状态',
    result_message varchar(255) comment '结果信息',
    receipt_result_url varchar(255) comment '回单结果地址',
    journal_result_url varchar(255) comment '流水结果地址',
    ip varchar(32) comment 'ip地址',
    env varchar(32) comment '环境',
    image_result_url varchar(255) comment '图片结果地址',
    primary key (id)
);
create unique index UK_bank_task on bank_task (account_book_id, period,bank_type);

drop table if exists voucher_book;
create table voucher_book
(
    id              bigint NOT NULL AUTO_INCREMENT comment '主键',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    account_book_id bigint comment '账套ID',
    period          varchar(6) comment '期间',
    state           int comment '电子账簿生成 状态1：未生成；2：已生成；3：生成失败',
    result_message  varchar(255) comment '结果信息',
    url             varchar(255) comment '电子账簿pdf下载地址',
    primary key (id)
);
create unique index UK_voucher_book on voucher_book (account_book_id, period);

drop table if exists party_work_total;
create table party_work_total
(
    id              bigint NOT NULL AUTO_INCREMENT comment '主键',
    gmt_create      datetime default current_timestamp comment '创建时间',
    gmt_modified    datetime default current_timestamp comment '修改时间',
    party_id        bigint comment '加盟商ID',
    work_date       datetime comment '操作时间',
    modify_count    int comment '加盟商做账次数',
    longin_count    int comment '加盟商登录次数',
    book_list_count int comment '加盟商查看账套列表次数',
    download_voucher_count int comment '加盟商下载凭证次数',
    restore_book_count int comment '加盟商恢复账套次数',
    primary key (id)
);
create unique index UK_party_work_total on party_work_total (party_id, work_date);

drop table if exists qxy_log;
create table qxy_log
(
    id bigint not null auto_increment comment '主键id',
    gmt_create datetime default current_timestamp comment '创建时间',
    gmt_modified datetime default current_timestamp comment '修改时间',
    social_credit_code varchar(64) comment '纳税人识别号（统一社会信用代码）',
    uri varchar(32) comment 'uri',
    ip varchar(32) comment 'ip地址',
    phone varchar(255) comment '手机号',
    req_id varchar(255) comment '请求ID',
    message varchar(255) comment '结果信息',
    login_way int comment '电子税务局登录方式 1:旧版登录；2：新版登录；3：代理登录',
    national_name varchar(255) comment '登录名',
    national_pwd varchar(255) comment '密码',
    area_id int comment '区域',
    social_credit_code_agent varchar(255) comment '代理：税号',
    city varchar(255) comment '所属城市',
    primary key(id)
);

drop table if exists qxy_order_log;
create table qxy_order_log
(
    id bigint not null auto_increment comment '主键id',
    gmt_create datetime default current_timestamp comment '创建时间',
    gmt_modified datetime default current_timestamp comment '修改时间',
    book_name varchar(64) comment '账套名称',
    social_credit_code varchar(64) comment '纳税人识别号（统一社会信用代码）',
    end_date datetime comment '升级版报税到期时间',
    login_id varchar(64) comment '操作账号',
    accounter_id bigint comment '操作人',
    accounter_no varchar(64) comment '员工级次编码',
    accounter_name varchar(64) comment '员工姓名',
    ip varchar(32) comment 'ip地址',
    primary key(id)
);
create index IDX_qxyorderlog_accounterno on qxy_order_log (accounter_no);
create index IDX_qxyorderlog_socialcreditcode on qxy_order_log (social_credit_code);
create index IDX_qxyorderlog_accounterid on qxy_order_log (accounter_id);

drop table if exists invoice_pdf;
create table invoice_pdf
(
    id bigint not null auto_increment comment '主键id',
    gmt_create datetime default current_timestamp comment '创建时间',
    gmt_modified datetime default current_timestamp comment '修改时间',
    invoice_no  VARCHAR(32) not null comment '发票号',
    url varchar(255) comment'pdf url',
    primary key(id)
);
create unique index UK_invoice_pdf on invoice_pdf (invoice_no);

drop table if exists ai_helper_setting;
create table ai_helper_setting
(
    id bigint not null auto_increment comment '主键id',
    gmt_create datetime default current_timestamp comment '创建时间',
    gmt_modified datetime default current_timestamp comment '修改时间',
    account_book_id bigint, /*账套id*/
    tick_way  int default 0 comment '勾选确认方式',/*0：人工确认（默认）,1:自动确认*/
    invoice_collect_way int default 0 comment '发票采集方式',/*0：人工提交 （默认）,1:自动提交*/
    bank_collect_way int default 0 comment '银行采集方式',/*0：人工提交 （默认）,1:自动提交*/
    invoice_generate_voucher_way int default 1 comment '增值税发票生成凭证方式',/*0：自动生成，1：人工生成*/
    carry_forward_way int default 0 comment '结转方式',/*0:人工，1：自动*/
    invoice_import_way int default 0 comment '发票导入方式',/*0：人工导入:1：自动导入*/
    copy_salary_way int default 0 comment '复制工资方式',/*0：人工复制，1：自动复制*/
    salary_generate_standard_way int default 1 comment '工资生成标准凭证',/*0：自动生成:1：人工生成*/
    pay_way int default 0 comment '结账',/*0：人工，1：默认*/
    individual_tax_apply_way int default 0 comment '个税申报方式',/*0：人工提交 （默认）,1:自动提交*/
    production_operation_way int default 0 comment '生产经营申报方式',/*0：人工提交 （默认）,1:自动提交*/
    tax_bureau_way int default 0 comment '税局申报方式',/*0：人工提交 （默认）,1:自动提交*/
    tax_bureau_inspect_way int default 0 comment '税局检查方式',/*0：人工提交 （默认）,1:自动提交*/
    individual_tax_pay_way int default 0 comment '个税扣款方式',/*0：人工提交 （默认）,1:自动提交*/
    production_operation_pay_way int default 0 comment '生产经营扣款方式',/*0：人工提交 （默认）,1:自动提交*/
    tax_bureau_pay_way int default 0 comment '税局扣款方式',/*0：人工提交 （默认）,1:自动提交*/
    apply_way int default 0 comment '申报方式',/*0：人工提交 （默认）,1:自动提交*/
    deduction_way int default 0 comment '扣款方式',/*0：人工提交 （默认）,1:自动提交*/
    jzbsqd_date int default 2 comment '记账报税启动日期',/*默认2*/
    sbqd_date int default 2 comment '社保启动日期',/*默认2*/
    primary key(id)
);
create unique index UK_ai_helper_setting on ai_helper_setting (account_book_id);

drop table if exists ai_helper_total_setting;
create table ai_helper_total_setting
(
    id bigint not null auto_increment comment '主键id',
    gmt_create datetime default current_timestamp comment '创建时间',
    gmt_modified datetime default current_timestamp comment '修改时间',
    accountant_id bigint, /*会计id*/
    tick_way  int default 0 comment '勾选确认方式',/*0：人工确认（默认）,1:自动确认*/
    invoice_collect_way int default 0 comment '发票采集方式',/*0：人工提交 （默认）,1:自动提交*/
    bank_collect_way int default 0 comment '银行采集方式',/*0：人工提交 （默认）,1:自动提交*/
    invoice_generate_voucher_way int default 1 comment '增值税发票生成凭证方式',/*0：自动生成，1：人工生成*/
    carry_forward_way int default 0 comment '结转方式',/*0:人工，1：自动*/
    invoice_import_way int default 0 comment '发票导入方式',/*0：人工导入:1：自动导入*/
    copy_salary_way int default 0 comment '复制工资方式',/*0：人工复制，1：自动复制*/
    salary_generate_standard_way int default 1 comment '工资生成标准凭证',/*0：自动生成:1：人工生成*/
    pay_way int default 0 comment '结账',/*0：人工，1：默认*/
    individual_tax_apply_way int default 0 comment '个税申报方式',/*0：人工提交 （默认）,1:自动提交*/
    production_operation_way int default 0 comment '生产经营申报方式',/*0：人工提交 （默认）,1:自动提交*/
    tax_bureau_way int default 0 comment '税局申报方式',/*0：人工提交 （默认）,1:自动提交*/
    tax_bureau_inspect_way int default 0 comment '税局检查方式',/*0：人工提交 （默认）,1:自动提交*/
    individual_tax_pay_way int default 0 comment '个税扣款方式',/*0：人工提交 （默认）,1:自动提交*/
    production_operation_pay_way int default 0 comment '生产经营扣款方式',/*0：人工提交 （默认）,1:自动提交*/
    tax_bureau_pay_way int default 0 comment '税局扣款方式',/*0：人工提交 （默认）,1:自动提交*/
    apply_way int default 0 comment '申报方式',/*0：人工提交 （默认）,1:自动提交*/
    deduction_way int default 0 comment '扣款方式',/*0：人工提交 （默认）,1:自动提交*/
    jzbsqd_date int default 2 comment '记账报税启动日期',/*默认2*/
    sbqd_date int default 2 comment '社保启动日期',/*默认2*/
    primary key(id)
);

drop table if exists ai_chat;
create table ai_chat
(
    id bigint not null auto_increment comment '主键id',
    gmt_create datetime default current_timestamp comment '创建时间',
    gmt_modified datetime default current_timestamp comment '修改时间',
    phone varchar(255) comment '手机号',
    ai_type int default 0 comment 'AI身份类型 1~6',
    session_id varchar(255) comment '手机-类型组合',
    record_type varchar(255) comment '记录来源类型user/ai',
    content varchar(255) comment '对话文本',
    file_id varchar(255) comment '文件id',
    party_id varchar(255) comment '合作商id',
    book_name varchar(255) comment '公司名称',
    is_like int default 0 comment '是否收藏',
    primary key(id)
);
create index IDX_ai_chat on ai_chat (phone, ai_type);

drop table if exists ai_chat_file;
create table ai_chat_file
(
    id bigint not null auto_increment comment '主键id',
    gmt_create datetime default current_timestamp comment '创建时间',
    gmt_modified datetime default current_timestamp comment '修改时间',
    file_name varchar(255) comment '文件名',
    file_url varchar(255) comment '文件地址',
    file_id varchar(255) comment '文件id',
    primary key(id)
);
create unique index UK_ai_chat_file on ai_chat_file (file_id);

drop table if exists ai_weixin_user;
create table ai_weixin_user
(
    id bigint not null auto_increment comment '主键id',
    gmt_create datetime default current_timestamp comment '创建时间',
    gmt_modified datetime default current_timestamp comment '修改时间',
    user_id varchar(255) comment '用户id',
    nickname varchar(255) comment '微信昵称',
    avatar varchar(255) comment '微信头像',
    gender int comment '性别',
    primary key(id)
) COMMENT = '微信客服用户';
create unique index UK_ai_weixin_user on ai_weixin_user (user_id);

drop table if exists hsqj1;
create table hsqj1 (
    id bigint not null auto_increment comment '主键id',
    gmt_create datetime default current_timestamp comment '创建时间',
    gmt_modified datetime default current_timestamp comment '修改时间',
    account_book_id bigint not null comment '账套id',
    year int not null comment '申报年度',

    -- 基本经营信息
    row101 varchar(64) comment '101纳税申报企业类型(填写代码)',
    row102 decimal(5,2) comment '102分支机构购地的纳税比例(%)',
    row103 decimal(14,2) comment '103资产总额(填写年均值，单位：万元)',
    row104 int comment '104从业人数(填写年均值，单位：人)',
    row105 varchar(255) comment '105所属国民经济行业',
    row106 int comment '106从事国家限制或禁止行业（0-否 1-是）',
    row107 varchar(64) comment '采用会计准则或会计制度',
    row108 int comment '108采用一般企业所得税报表格式(2019年版)',
    row109 int comment '109小型微利企业（0-否 1-是）',
    row110 int comment '110上市公司(口境内口境外)（0-否 1-境内 2-境外）',

    -- 税收事项
    row201 int comment '201从事股权投资业务（0-否 1-是）',
    row202 int comment '202存在境外关联交易（0-否 1-是）',
    row2031 int comment '203-1选择采用的境外所得抵免方式 1:分国（地区）不分项  2:不分国（地区）不分项',
    row2032 int comment '203-2新增境外所得纳税调整情况 0:否 1:旅游业 2:现代服务业 3:高新技术产业',
    row204 int comment '204有限责任制创业投资企业法人代表人 0:否 1:是',
    row205 int comment '205出口退税办理（0-否 1-是）',
    row206 varchar(64) comment '206技术先进型服务企业类型',
    row207 int comment '207非营利组织（0-否 1-是）',
    row208 varchar(64) comment '208软件、集成电路企业类型（填写代码）',
    row209 int comment '209集成电路生产项目类型 1：130纳米 2：65纳米 3：28纳米',
    row2101 varchar(64) comment '210-1申报所属期(年度)入库编号1',
    row2102 varchar(64) comment '210-2申报所属期(年度)入库编号2',
    row2103 varchar(64) comment '210-3所属期(上一年度)入库编号2',
    row2104 varchar(64) comment '210-4入库时间1',
    row2111 varchar(64) comment '211-1证书编号1',
    row2112 varchar(64) comment '211-2证书编号2',
    row2113 varchar(64) comment '211-3证书编号3',
    row2114 varchar(64) comment '211-4失效时间1',
    row212 int comment '212重组事项特殊性税务处理方式 1:一般性 2:特殊性',
    row213 varchar(64) comment '213重组事项类型(填写代码)',
    row214 int comment '214生产经营性停止且停止生产经营六个月得度（0-否 1-是）',

    -- 生产经营情况
    row215 varchar(32) comment '215政策性搬迁开始时间',
    row216 int comment '216发生重大市值损失对外投资损失纳税调整情况（0-否 1-是）',
    row217 int comment '217发生市值损失产生对外投资损失计算所得额纳税调整（0-否 1-是）',
    row218 int comment '218发生特殊债务投资入账调整纳税事项（0-否 1-是）',
    row219 int comment '219非货币性资产对外投资转让所得递延纳税年度 (0-否 1-是)',
    row220 int comment '220发生技术成果投资入股递延纳税事项（0-否 1-是）',
    row221 int comment '221技术成果投资入股递延纳税年度（0-否 1-是）',
    row222 int comment '222发生生产（股权）划转特殊性税务处理事项（0-否 1-是）',
    row223 varchar(32) comment '223储备专项前清算纳税年度',
    row224 int comment '224版本选择 0: 否 1: 2015版 2: 2021版 3: 自行设计',

    -- 股东分红信息
    shareholder_info varchar comment '股东分红信息(包含股东名称、证件类型、证件号码、投资比例、分红金额等)',
    other_shareholder_ratio decimal(5,2) comment '其余股东合计 投资比例',
    other_dividend decimal(14,2) comment '其余股东合计 当年（决议日）分配的股息、红利等权益性投资收益金额',
    primary key (id)
) comment = 'A000000企业所得税年度纳税申报基础信息表';
create unique index UK_hsqj1 on hsqj1 (account_book_id, year);

drop table if exists hsqj2;
create table hsqj2
(
    id bigint not null auto_increment comment '主键id',
    gmt_create datetime default current_timestamp comment '创建时间',
    gmt_modified datetime default current_timestamp comment '修改时间',
    account_book_id BIGINT NOT NULL COMMENT '账套id',
    year INT NOT NULL COMMENT '申报年度',
    table_no varchar(32) comment'表单编号',
    table_name varchar(64) comment'表单名称',
    is_apply int comment'是否申报',
    primary key(id)
)COMMENT = '表单选择';
create unique index UK_hsqj2 on hsqj2 (account_book_id, year,table_name);

DROP TABLE IF EXISTS hsqj3;
CREATE TABLE hsqj3 (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键id',
    gmt_create DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    gmt_modified DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    account_book_id BIGINT NOT NULL COMMENT '账套id',
    year INT NOT NULL COMMENT '申报年度',

    -- 利润总额计算部分
    row1 DECIMAL(15, 2) COMMENT '一、营业收入(填写A101010\\101020\\103000)',
    row2 DECIMAL(15, 2) COMMENT '减：营业成本(填写A102010\\102020\\103000)',
    row3 DECIMAL(15, 2) COMMENT '减：税金及附加',
    row4 DECIMAL(15, 2) COMMENT '减：销售费用(填写A104000)',
    row5 DECIMAL(15, 2) COMMENT '减：管理费用(填写A104000)',
    row6 DECIMAL(15, 2) COMMENT '减：研发费用(填写A104000)',
    row7 DECIMAL(15, 2) COMMENT '减：财务费用(填写A104000)',
    row8 DECIMAL(15, 2) COMMENT '加：其他收益',
    row9 DECIMAL(15, 2) COMMENT '加：投资收益（损失以‘-’号填列）',
    row10 DECIMAL(15, 2) COMMENT '加：净敞口套期收益（损失以‘－’号填列）',
    row11 DECIMAL(15, 2) COMMENT '加：公允价值变动收益（损失以‘－’号填列）',
    row12 DECIMAL(15, 2) COMMENT '加：信用减值损失（损失以‘－’号填列）',
    row13 DECIMAL(15, 2) COMMENT '加：资产减值损失（损失以‘－’号填列）',
    row14 DECIMAL(15, 2) COMMENT '加：资产处置收益（损失以‘－’号填列）',
    row15 DECIMAL(15, 2) COMMENT '二、营业利润（损失以‘－’号填列）',
    row16 DECIMAL(15, 2) COMMENT '加：营业外收入(填写A101010\\101020\\103000)',
    row17 DECIMAL(15, 2) COMMENT '减：营业外支出(填写A102010\\102020\\103000)',
    row18 DECIMAL(15, 2) COMMENT '三、利润总额（15 + 16 - 17）',

    -- 应纳税所得额计算部分
    row19 DECIMAL(15, 2) COMMENT '减：境外所得（填写A108010）',
    row20 DECIMAL(15, 2) COMMENT '加：纳税调整增加额（填写A105000）',
    row21 DECIMAL(15, 2) COMMENT '减：纳税调整减少额（填写A105000）',
    row22 DECIMAL(15, 2) COMMENT '减：免税、减计收入及加计扣除（22.1 + 22.2 +...） 填写优惠事项',
    row22options VARCHAR(255) COMMENT '22行具体优惠事项（json）',
    row23 DECIMAL(15, 2) COMMENT '加：境外应税所得抵减境内亏损（填写A108000）',
    row24 DECIMAL(15, 2) COMMENT '四、纳税调整后所得（18 - 19 + 20 - 21 - 22 + 23）',
    row25 DECIMAL(15, 2) COMMENT '减：所得减免（填写 A107020 ）',
    row26 DECIMAL(15, 2) COMMENT '减：弥补以前年度亏损（填写A106000）',
    row27 DECIMAL(15, 2) COMMENT '减：抵扣应纳税所得额（填写A107030）',
    row28 DECIMAL(15, 2) COMMENT '五、应纳税所得额（24 - 25 - 26 - 27）',

    -- 应纳税额计算部分
    row29 DECIMAL(5, 2) COMMENT '税率（25%）',
    row30 DECIMAL(15, 2) COMMENT '六、应纳所得税额（28 × 29）',
    row31 DECIMAL(15, 2) COMMENT '减：减免所得税额（31.1 + 31.2 +...）填写优惠事项',
    row31options VARCHAR(255) COMMENT '31行具体优惠事项（json）',
    row32 DECIMAL(15, 2) COMMENT '减：抵免所得税额（填写A107050）',
    row33 DECIMAL(15, 2) COMMENT '七、应纳税额（30 - 31 - 32）',
    row34 DECIMAL(15, 2) COMMENT '加：境外所得应纳所得税额（填写A108000）',
    row35 DECIMAL(15, 2) COMMENT '减：境外所得抵免所得税额（填写A108000）',
    row36 DECIMAL(15, 2) COMMENT '八、实际应纳所得税额（33 + 34 - 35）',

    -- 实际应补（退）税额计算部分
    row37 DECIMAL(15, 2) COMMENT '减：本年累计预缴所得税额',
    row371 DECIMAL(15, 2) COMMENT '各期预缴应补（退）所得税额合计',
    row372 DECIMAL(15, 2) COMMENT '特定业务累计已预缴所得税额',
    row373 DECIMAL(15, 2) COMMENT '本年预缴开票企业所得税额',
    row374 DECIMAL(15, 2) COMMENT '本年预缴申报误收退税金额',
    row38 DECIMAL(15, 2) COMMENT '九、本年应补（退）所得税额（36 - 37）',
    row39 DECIMAL(15, 2) COMMENT '其中：总机构分摊本年应补（退）所得税额(填写A109000)',
    row40 DECIMAL(15, 2) COMMENT '财政集中分配本年应补（退）所得税额（填写A109000）',
    row41 DECIMAL(15, 2) COMMENT '总机构主体生产经营部门分摊本年应补（退）所得税额(填写A109000)',
    row_fz1 DECIMAL(15, 2) COMMENT '中央级收入应补（退）所得税额【一般企业38×60%；总机构（39+40+41）*60%】',
    row_fz2 DECIMAL(15, 2) COMMENT '中央待分配收入应补（退）所得税额【跨省总机构（39+40）*20%】',
    row_fz3 DECIMAL(15, 2) COMMENT '地方级收入应补（退）所得税额【一般企业38×40%；跨省总机构（39+40）*20%+41*40%；总机构（39+40+41）*40%）】',
    row42 INT DEFAULT 0 COMMENT '本年民族自治地区地方分享部分优惠方式 免征 减征 否',
    row420 DECIMAL(15, 2) COMMENT '优惠幅度（0为不减免，100%为免征）',
    row421 DECIMAL(15, 2) COMMENT '本年应减免金额（一般企业36行×40%×42.0行“优惠幅度”；总机构A109000表第23行）',
    row422 DECIMAL(15, 2) COMMENT '本年累计已减免金额（本年度4季度预缴申报表23.1行，总机构A109000表第24行）',
    row423 DECIMAL(15, 2) COMMENT '因优惠产生的地方级收入应补（退）金额(一般企业42.1-42.2；总机构A10900025行)',
    row424 DECIMAL(15, 2) COMMENT '总机构及分支机构地方级收入全年减免总额（42.1+A109010第13列合计）',
    row43 DECIMAL(15, 2) COMMENT '减：稽查查补（退）所得税额',
    row_fz4 DECIMAL(15, 2) COMMENT '其中：稽查查补中央级收入应补（退）所得税额',
    row_fz5 DECIMAL(15, 2) COMMENT '稽查查补中央待分配收入应补（退）所得税额',
    row_fz6 DECIMAL(15, 2) COMMENT '稽查查补地方级收入应补（退）所得税额',
    row44 DECIMAL(15, 2) COMMENT '减：特别纳税调整补（退）所得税额',
    row_fz7 DECIMAL(15, 2) COMMENT '其中：特别纳税调整中央级收入应补（退）所得税额',
    row_fz8 DECIMAL(15, 2) COMMENT '特别纳税调整中央待分配收入应补（退）所得税额',
    row_fz9 DECIMAL(15, 2) COMMENT '特别纳税调整地方级收入应补（退）所得税额',
    row_fz10 DECIMAL(15, 2) COMMENT '中央级收入实际应补（退）所得税额（FZ1-FZ4-FZ7）',
    row_fz11 DECIMAL(15, 2) COMMENT '中央待分配收入实际应补（退）所得税额（FZ2-FZ5-FZ8）',
    row_fz12 DECIMAL(15, 2) COMMENT '地方级收入实际应补（退）所得税额（FZ3-42.3-FZ6-FZ9）',
    row45 DECIMAL(15, 2) COMMENT '十、本年实际应补（退）所得税额（一般企业38 - 42.3 - 43 - 44行；总机构A109000第26行）',

    PRIMARY KEY (id)
) COMMENT = 'A100000企业所得税年度纳税申报主表';
create unique index UK_hsqj3 on hsqj3 (account_book_id, year);

drop table if exists hsqj4;
create table hsqj4 (
    id bigint not null auto_increment comment '主键id',
    gmt_create datetime default current_timestamp comment '创建时间',
    gmt_modified datetime default current_timestamp comment '修改时间',
    account_book_id bigint not null comment '账套id',
    year int not null comment '申报年度',
    row int comment '行次',
    amount decimal(14,2) comment '金额',
    subject_ids varchar(255) comment '选用的科目id',
    primary key (id)
) comment = 'A101010一般企业收入明细表';
create unique index UK_hsqj4 on hsqj4 (account_book_id, year, row);

drop table if exists  hsqj5;
create table  hsqj5
(
    id bigint not null auto_increment comment '主键id',
    gmt_create datetime default current_timestamp comment '创建时间',
    gmt_modified datetime default current_timestamp comment '修改时间',
    account_book_id  bigint not null comment '账套id',
    year int not null comment '申报年度',
    row            int comment'行次',
    money           decimal(10, 2) comment'金额',
    subject_ids varchar(255) comment '选用的科目id',
    primary key(id)
) comment = 'A102010一般企业成本支出明细表';
create unique index UK_hsqj5 on hsqj5 (account_book_id, year, row);

DROP TABLE IF EXISTS hsqj6;
CREATE TABLE hsqj6 (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键id',
    gmt_create DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    gmt_modified DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    account_book_id BIGINT NOT NULL COMMENT '账套id',
    year INT NOT NULL COMMENT '申报年度',
    row INT NOT NULL COMMENT '行次',
    sale DECIMAL(15, 2) COMMENT '销售费用',
    sale_formula VARCHAR(255) COMMENT '销售费用公式',
    sale_out DECIMAL(15, 2) COMMENT '销售费用 其中：境外支付',
    manage DECIMAL(15, 2) COMMENT '管理费用',
    manage_formula VARCHAR(255) COMMENT '管理费用公式',
    manage_out DECIMAL(15, 2) COMMENT '管理费用 其中：境外支付',
    finance DECIMAL(15, 2) COMMENT '财务费用',
    finance_formula VARCHAR(255) COMMENT '财务费用公式',
    finance_out DECIMAL(15, 2) COMMENT '财务费用 其中：境外支付',
    PRIMARY KEY (id)
) COMMENT = 'A104000期间费用明细表';
create unique index UK_hsqj6 on hsqj6 (account_book_id, year, row);

drop table if exists hsqj7;
create table hsqj7 (
    id bigint not null auto_increment comment '主键id',
    gmt_create datetime default current_timestamp comment '创建时间',
    gmt_modified datetime default current_timestamp comment '修改时间',
    account_book_id bigint not null comment '账套id',
    year int not null comment '申报年度',
    row varchar(32) comment '行次',
    account_amount decimal(14,2) comment '账载金额',
    income_amount decimal(14,2) comment '税收金额',
    increase_amount decimal(14,2) comment '调增金额',
    decrease_amount decimal(14,2) comment '调减金额',
    primary key (id)
) comment = 'A105000纳税调整项目明细表';
create unique index UK_hsqj7 on hsqj7 (account_book_id, year, row);

drop table if exists hsqj8;
create table hsqj8
(
    id                bigint      NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create        datetime default current_timestamp comment '创建时间',
    gmt_modified      datetime default current_timestamp comment '修改时间',
    account_book_id   bigint  comment '账套id',
    year              int     comment '申报年度',
    row int not null comment '行次',
    program_name varchar(64) comment'项目',
    zz_money   decimal(14, 2), /*账载金额*/
    sjfs_money   decimal(14, 2), /*实际发生额*/
    ssgd_kcl   decimal(14, 2), /*税收规定扣除率*/
    yqnd_ljjz_kc_money   decimal(14, 2), /*以前年度累计结转扣除额*/
    ss_money   decimal(14, 2), /*税收金额*/
    nstz_money   decimal(14, 2), /*纳税调整金额*/
    ljjz_yhnd_kc_money   decimal(14, 2), /*累计结转以后年度扣除额*/
    zz_subject_ids varchar(255) comment '账载金额选用的科目id',
    sjfs_money_subject_ids varchar(255) comment '实际发生额选用的科目id',
    ss_money_subject_ids varchar(255) comment '税收金额选用的科目id',
    primary key (id)
) comment = 'A105050职工薪酬支出及纳税调整明细表';
create unique index UK_hsqj8 on hsqj8 (account_book_id, year, row);

DROP TABLE IF EXISTS hsqj9;
CREATE TABLE hsqj9 (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键id',
    gmt_create DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    gmt_modified DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    account_book_id BIGINT NOT NULL COMMENT '账套id',
    year INT NOT NULL COMMENT '申报年度',
    row INT NOT NULL COMMENT '行次',
    column1 DECIMAL(15, 2) COMMENT '广告费和业务宣传费',
    column2 DECIMAL(15, 2) COMMENT '保险企业手续费及佣金支出',
    PRIMARY KEY (id)
) COMMENT = 'A105060广告费和业务宣传费跨年度纳税调整明细表';
create unique index UK_hsqj9 on hsqj9 (account_book_id, year, row);

drop table if exists hsqj10;
create table hsqj10
(
    id                bigint      NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create        datetime default current_timestamp comment '创建时间',
    gmt_modified      datetime default current_timestamp comment '修改时间',
    account_book_id   bigint  comment '账套id',
    year              int     comment '申报年度',
    row               varchar(32) comment '行次',
    account_amount    decimal(14, 2) comment '账载金额',
    previous_year_amount    decimal(14, 2) comment '以前年度结转可扣除的捐赠额',
    tax_limit_amount    decimal(14, 2) comment '按税收规定计算的扣除限额',
    income_amount     decimal(14, 2) comment '税收金额',
    increase_amount   decimal(14, 2) comment '纳税调增金额',
    decrease_amount   decimal(14, 2) comment '纳税调减金额',
    transfer_amount   decimal(14, 2) comment '可结转以后年度扣除的捐赠额',
    primary key (id)
) comment = 'A105070捐赠支出及纳税调整明细表';
create unique index UK_hsqj10 on hsqj10 (account_book_id, year, row);

drop table if exists hsqj11;
create table hsqj11
(
    id                bigint      NOT NULL AUTO_INCREMENT comment '主键id',
    gmt_create        datetime default current_timestamp comment '创建时间',
    gmt_modified      datetime default current_timestamp comment '修改时间',
    account_book_id   bigint  comment '账套id',
    year              int     comment '申报年度',
    row varchar(64) not null comment '行次',
    is_selected int comment '是否选中',
    program_name varchar(64) comment'项目',
    zcyz_money   decimal(14, 2), /*资产原值*/
    bnzj_tx_money   decimal(14, 2), /*本年折旧、摊销额*/
    ljzj_tx_money_zz   decimal(14, 2), /*累计折旧、摊销额*/
    zcjs_fundation   decimal(14, 2), /*资产计税基础*/
    sszj_tx_money   decimal(14, 2), /*税收折旧、摊销额*/
    zj_tx_money   decimal(14, 2), /*享受加速折旧政策的资产按税收一般规定计算的折旧、摊销额*/
    jszj_txtj_money   decimal(14, 2), /*加速折旧、摊销统计额*/
    ljzj_tx_money_ss   decimal(14, 2), /*累计折旧、摊销额*/
    nstz_money   decimal(14, 2), /*纳税调整金额*/
    primary key (id)
) comment = 'A105080资产折旧、摊销及纳税调整明细表';
create unique index UK_hsqj11 on hsqj11 (account_book_id, year, row);

DROP TABLE IF EXISTS hsqj12;
CREATE TABLE hsqj12 (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键id',
    gmt_create DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    gmt_modified DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    account_book_id BIGINT NOT NULL COMMENT '账套id',
    year INT NOT NULL COMMENT '申报年度',
    row INT NOT NULL COMMENT '行次',
    column1 INT COMMENT '年度',
    column2 DECIMAL(15, 2) COMMENT '当年境内所得额',
    column3 DECIMAL(15, 2) COMMENT '分立转出的亏损额',
    column4 DECIMAL(15, 2) COMMENT '可弥补年限5年',
    column5 DECIMAL(15, 2) COMMENT '可弥补年限8年',
    column6 DECIMAL(15, 2) COMMENT '可弥补年限10年',
    column7 VARCHAR(255) COMMENT '弥补亏损企业类型',
    column8 DECIMAL(15, 2) COMMENT '当年亏损额',
    column9 DECIMAL(15, 2) COMMENT '当年待弥补的亏损额',
    column10 DECIMAL(15, 2) COMMENT '使用境内所得弥补',
    column11 DECIMAL(15, 2) COMMENT '使用境外所得弥补',
    column12 DECIMAL(15, 2) COMMENT '当年可结转以后年度弥补的亏损额',
    PRIMARY KEY (id)
) COMMENT = 'A106000企业所得税弥补亏损明细表';
create unique index UK_hsqj12 on hsqj12 (account_book_id, year, row);

DROP TABLE IF EXISTS hsqj_balance;
CREATE TABLE hsqj_balance (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键id',
    gmt_create DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    gmt_modified DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    account_book_id BIGINT NOT NULL COMMENT '账套id',
    year INT NOT NULL COMMENT '申报年度',
    no varchar(255) comment '科目编码',
    text varchar(255) comment '科目名称',
    direction int comment '借贷方向  1:借  2:贷',
    level int comment '科目级次',
    is_init int comment '是否预置',
    pno varchar(32) comment '父科目编码',
    leaf int comment '是否叶子节点',
    long_text varchar(255) comment '全级次名称',
    initbalance decimal(14, 2) comment '期初余额',
    jfbalance decimal(14, 2) comment '借方余额',
    dfbalance decimal(14, 2) comment '贷方余额',
    endbalance decimal(14, 2) comment '期末余额',
    PRIMARY KEY (id)
) COMMENT = '汇算清缴余额表';
create unique index hsqj_balance on hsqj_balance (account_book_id, year, no);

DROP TABLE IF EXISTS tax_report;
CREATE TABLE tax_report (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键id',
    gmt_create DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    gmt_modified DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    version INT COMMENT '版本号',
    order_id VARCHAR(255) COMMENT '订单编号',
    g_id VARCHAR(255) COMMENT '报告版本',
    env VARCHAR(255) COMMENT '环境',
    ip VARCHAR(255) COMMENT 'ip地址',
    sms_code VARCHAR(255) COMMENT '验证码',
    task_id VARCHAR(255) COMMENT '异步任务id',
    account_book_id BIGINT COMMENT '账套id',
    account_book_name VARCHAR(255) COMMENT '账套名称',
    tax_code VARCHAR(255) COMMENT '税号',
    national_name VARCHAR(255) COMMENT '电子税务局登录名',
    national_pwd VARCHAR(255) COMMENT '电子税局密码',
    tax_payer_phone VARCHAR(255) COMMENT '办税人手机号',
    tax_authorities VARCHAR(255) COMMENT '主管税务机关',
    type_auth INT DEFAULT 0 COMMENT '验证：方式 0：网页 1：APP',
    province VARCHAR(255) COMMENT '所属省份',
    city VARCHAR(255) COMMENT '所属城市',
    login_way INT COMMENT '电子税务局登录方式 1:旧版登录；2：新版登录；3：代理登录；4：代理登录验证',
    social_credit_code_agent VARCHAR(255) COMMENT '代理：社会信用代码',
    start_time DATETIME COMMENT '开始运行时间',
    area_id INT COMMENT '区域编号',
    action_id INT COMMENT '操作编号',
    order_level INT DEFAULT 0 COMMENT '优先级',
    state INT COMMENT '状态',
    result_message VARCHAR(1024) COMMENT '结果信息',
    result_url VARCHAR(1024) COMMENT '结果下载地址',
    result_image_url VARCHAR(1024) COMMENT '结果图片地址',
    result_json_url VARCHAR(1024) COMMENT '结果数据json',
    result_pdf_url VARCHAR(1024) COMMENT '结果数据pdf',
    result_json_url2 VARCHAR(1024) COMMENT '结果数据json2',
    result_pdf_url2 VARCHAR(1024) COMMENT '结果数据pdf2',
    result_json_url3 VARCHAR(1024) COMMENT '结果数据json3',
    result_pdf_url3 VARCHAR(1024) COMMENT '结果数据pdf3',
    result_json_url4 VARCHAR(1024) COMMENT '结果数据json4',
    result_pdf_url4 VARCHAR(1024) COMMENT '结果数据pdf4',
    result_json_url5 VARCHAR(1024) COMMENT '结果数据json5',
    result_pdf_url5 VARCHAR(1024) COMMENT '结果数据pdf5',
    source int default 0 comment '来源 0：财务软件 1：小程序 2：app',
    PRIMARY KEY (id)
) COMMENT = '风险报告表';

DROP TABLE IF EXISTS tax_report_data;
CREATE TABLE tax_report_data (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键id',
    gmt_create DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    gmt_modified DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    tax_code VARCHAR(255) COMMENT '税号',
    type VARCHAR(255) COMMENT '类型',
    data_url VARCHAR(1024) COMMENT '数据地址',
    PRIMARY KEY (id)
) COMMENT = '风险报告数据表';
create unique index UK_tax_report_data on tax_report_data (tax_code, type);

DROP TABLE IF EXISTS app_user;
CREATE TABLE app_user (
   id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键id',
   gmt_create DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   gmt_modified DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
   phone varchar(11) NOT NULL COMMENT '手机号',
   reference varchar(255) COMMENT '推荐人',
   reference_company varchar(255) COMMENT '推荐人服务公司',
   register_time varchar(255) COMMENT '注册时间',
   company_id BIGINT  COMMENT '公司id',
   ai_end_time DATETIME  COMMENT 'ai到期时间',
   manual_consult_end_time DATETIME  COMMENT '人工税务咨询到期时间',

   PRIMARY KEY (id)
) COMMENT = '用户登录信息表';

DROP TABLE IF EXISTS app_code;
CREATE TABLE app_code (
  id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键id',
  gmt_create DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  gmt_modified DATETIME DEFAULT CURRENT_TIMESTAMP  COMMENT '修改时间',
  phone varchar(11) NOT NULL COMMENT '手机号',
  code varchar(6) NOT NULL COMMENT '验证码',
  end_time varchar(255) COMMENT '到期时间',
  PRIMARY KEY (id)
) COMMENT = '验证码表';
create unique index UK_app_code on app_code (phone, code);

/*章鱼问账企业表*/
drop table if exists app_company;
create table app_company
(
    id                bigint      NOT NULL AUTO_INCREMENT,
    gmt_create DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    gmt_modified DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    name          varchar(64) comment '公司名称',
    user_id            bigint comment '用户id',
    addtax             varchar(255) comment '增值税',
    accounting_system  varchar(255) comment '会计制度',
    status             int not null default 0 comment '0：未删除；1：删除',
    tax_code VARCHAR(255) COMMENT '税号',
    national_name VARCHAR(255) COMMENT '电子税务局登录名',
    national_pwd VARCHAR(255) COMMENT '电子税局密码',
    tax_payer_phone VARCHAR(255) COMMENT '办税人手机号',
    tax_authorities VARCHAR(255) COMMENT '主管税务机关',
    type_auth INT DEFAULT 1 COMMENT '验证：方式 0：网页 1：APP',
    province VARCHAR(255) COMMENT '所属省份',
    city VARCHAR(255) COMMENT '所属城市',
    login_way INT COMMENT '电子税务局登录方式 1:旧版登录；2：新版登录；3：代理登录；4：代理登录验证',
    social_credit_code_agent VARCHAR(255) COMMENT '代理：社会信用代码',
    primary key (id)
) comment = '章鱼问账企业表';

drop table if exists app_orders;
CREATE TABLE app_orders (
    id  bigint NOT NULL AUTO_INCREMENT,
    gmt_create DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    gmt_modified DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    order_no varchar(64) NOT NULL COMMENT '订单编号',
    user_id bigint  NOT NULL COMMENT '用户ID',
    company_id bigint  NOT NULL COMMENT '公司ID',
    service_id bigint  NOT NULL COMMENT '服务ID',
    service_name VARCHAR(255) NOT NULL COMMENT '服务名称',
    amount decimal(10,2) NOT NULL COMMENT '订单金额',
    order_status int NOT NULL DEFAULT 1 COMMENT '订单状态：1-待支付，2-已支付，3-已取消',
    transaction_id varchar(64) DEFAULT NULL COMMENT '交易流水号',
    payment_time datetime DEFAULT NULL COMMENT '支付时间',
    cancel_time datetime DEFAULT NULL COMMENT '取消时间',
    trade_type varchar(255) COMMENT '交易类型：JSAPI：公众号支付、小程序支付 NATIVE：Native支付 APP：APP支付 MICROPAY：付款码支付 MWEB：H5支付 FACEPAY：刷脸支付',
    report_status int  COMMENT '报告状态',
    report_url varchar(255) COMMENT '报告地址',
    result_msg varchar COMMENT '报告信息',
    PRIMARY KEY (id)
) COMMENT = '订单信息表';
CREATE INDEX idx_orders_user_company ON app_orders(user_id, company_id);
CREATE INDEX idx_orders_order_no ON app_orders(order_no);

drop table if exists app_orders_log;
CREATE TABLE app_orders_log (
    id  bigint NOT NULL AUTO_INCREMENT,
    gmt_create DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    gmt_modified DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    user_id bigint NOT NULL COMMENT '用户ID',
    order_id bigint COMMENT '订单表id',
    company_id bigint COMMENT '公司id',
    result_message VARCHAR COMMENT '结果信息',
    PRIMARY KEY (id)
) COMMENT = '订单信息日志表';

drop table if exists app_payer_info;
create table app_payer_info
(
    id bigint not null auto_increment comment '主键id',
    gmt_create datetime default current_timestamp comment '创建时间',
    gmt_modified datetime default current_timestamp comment '修改时间',
    user_id bigint not null comment '用户id',
    open_id varchar(255) comment '微信open_id',
    union_id varchar(255) comment '微信union_id',
    session_key varchar(255) comment '微信session_key',
    err_code int comment '错误码',
    err_msg varchar(255) comment '错误信息',
    primary key (id)
) comment = '支付者信息表';

