#database setting
spring.jpa.database=mysql
spring.datasource.url=**********************************************************************************************************************************
spring.datasource.username=hongfund_os
spring.datasource.password=hongfund_os_5013
#disable automatic initialize for mysql
spring.jpa.hibernate.ddl-auto=none
spring.jpa.showSql=false
spring.datasource.initialization-mode=never
spring.datasource.sqlScriptEncoding=UTF-8
spring.flyway.enabled=false

#custom settings
app.loginTimeoutDays=10
app.isUniqueBookCode=false
email.url=https://fc.hongfund.com:8602
server.http.port=8090
redisson.host=************
redisson.password=pupu!8dsn&plQJ&K

# logging settings
logging.file.name=/var/log/hongfund/jzs/HongFundOSCloud.log
logging.level.root=ERROR
logging.level.com.hongfund.efi.service.ScheduleService=INFO
logging.level.com.hongfund.efi.service.SystemService=INFO
logging.level.com.hongfund.efi.AuthFilter=WARN

# /info endpoint
info.app.name=hongfund jzs system
info.app.version=1.0

#https port
server.port = 8443
server.ssl.key-store = classpath:hongfund.com.pfx
server.ssl.key-store-password = zch7cu5j

# admin settings
spring.boot.admin.client.username=admin
spring.boot.admin.client.password=superadmin
spring.boot.admin.client.instance.name=jzs
spring.boot.admin.client.url=http://fctest.hongfund.com:8899
spring.boot.admin.client.instance.service-url=https://fc.hongfund.com:8602
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always