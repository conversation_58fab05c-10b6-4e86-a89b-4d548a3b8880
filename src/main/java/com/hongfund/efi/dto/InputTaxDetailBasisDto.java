package com.hongfund.efi.dto;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class InputTaxDetailBasisDto {
    /**
     * 其他票据取数
     */
    public int otherInvoiceNum = 0;
    public BigDecimal otherInvoiceMoneyAmounts = BigDecimal.ZERO;
    public BigDecimal otherInvoiceTaxAmounts = BigDecimal.ZERO;

    /**
     * 进项专票取数
     */
    public int specialInvoiceNum = 0;
    public BigDecimal specialInvoiceMoneyAmounts = BigDecimal.ZERO;
    public BigDecimal specialInvoiceTaxAmounts = BigDecimal.ZERO;

    /**
     * 进项普票
     */
    public int generalInvoiceNum = 0;
    public BigDecimal generalInvoiceMoneyAmounts = BigDecimal.ZERO;
    public BigDecimal generalInvoiceTaxAmounts = BigDecimal.ZERO;

    public InputTaxDetailBasisDto() {

    }
}
