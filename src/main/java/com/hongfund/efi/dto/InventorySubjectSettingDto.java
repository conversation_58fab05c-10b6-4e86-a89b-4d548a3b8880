package com.hongfund.efi.dto;

import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;

public class InventorySubjectSettingDto {
    public Long id;
    public Date gmtCreate;
    public Date gmtModified;
    /**
     * 账套id
     */
    public Long accountBookId;
    /**
     * 生产成本直接材料
     */
    public Long productionCostZjclId;
    /**
     * 生产成本直接人工
     */
    public Long productionCostZjrgId;

    /**
     * 生产成本制造费用
     */
    public Long productionCostZzfyId;
    /**
     * 主营业务收入
     */
    public Long zyywsrSubjectId;
    /**
     * 暂估科目 科目id.科目辅助关系id
     */
    public String zgSubjectId;
    /**
     * 成本科目
     */
    public Long cbSubjectId;
    /**
     * 暂估科目
     */
    public FixedAssetsSubjectDto zgSubject;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
