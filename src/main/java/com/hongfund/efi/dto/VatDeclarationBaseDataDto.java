package com.hongfund.efi.dto;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;

/**
 * <AUTHOR>
 */
public class VatDeclarationBaseDataDto {
	/**
	 * 3% 总销售额 (第1行)
	 */
	public String moneyAmounts_3;
	/**
	 * 3% 专票销售额(第2行)
	 */
	public String specialMoneyAmounts_3;
	/**
	 * 3% 普票销售额(第3行)
	 */
	public String generalMoneyAmounts_3;
	/**
	 * 5% 总销售额(第4行)
	 */
	public String moneyAmounts_5;
	/**
	 * 5% 专票销售额(第5行)
	 */
	public String specialMoneyAmounts_5;
	/**
	 * 5% 普票销售额(第6行)
	 */
	public String generalMoneyAmounts_5;
	/**
	 * 3% 总税额
	 */
	public String taxAmounts_3;
	/**
	 * 3% 专票税额
	 */
	public String specialTaxAmounts_3;
	/**
	 * 3% 税务代开 专票 税额(第23行)
	 */
	public String specialInsteadOpenTaxAmounts_3;
	/**
	 * 3% 普票税额
	 */
	public String generalTaxAmounts_3;
	/**
	 * 5% 总税额
	 */
	public String taxAmounts_5;
	/**
	 * 5% 专票税额
	 */
	public String specialTaxAmounts_5;
	/**
	 * 5% 税务代开 专票 税额(第23行)
	 */
	public String specialInsteadOpenTaxAmounts_5;
	/**
	 * 5% 普票税额
	 */
	public String generalTaxAmounts_5;

	/**
	 * 0% 其他免税销售额(第12行)
	 */
	public String otherFreeAmounts_0;
	/**
	 * 0% 总出口销售额(第13行)
	 */
	public String salesAmounts_0;
	/**
	 * 0% 税控器 出口销售额(第14行)
	 */
	public String taxControlSalesAmounts_0;
	/**
	 * 本期应纳税额减征额(第18行)
	 */
	public String vatCutDeclaration;
	/**
	 * 1% 总销售额 (第1行)
	 */
	public String moneyAmounts_1;
	/**
	 * 1% 专票销售额(第2行)
	 */
	public String specialMoneyAmounts_1;
	/**
	 * 1% 税务代开 专票 销售额
	 */
	public String specialInsteadOpenMoneyAmounts_1;
	/**
	 * 1% 普票销售额(第3行)
	 */
	public String generalMoneyAmounts_1;
	/**
	 * 1% 总税额
	 */
	public String taxAmounts_1;
	/**
	 * 1% 专票税额
	 */
	public String specialTaxAmounts_1;
	/**
	 * 1% 税务代开 专票 税额(第23行)
	 */
	public String specialInsteadOpenTaxAmounts_1;
	/**
	 * 1% 普票税额
	 */
	public String generalTaxAmounts_1;

	/**
	 * 0% 销售额
	 */
	public String  moneyAmounts_0;


	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

	public void setZero() {
		if (StringUtils.isBlank(moneyAmounts_3)) {
			moneyAmounts_3 = "0.00";
		}
		if (StringUtils.isBlank(specialMoneyAmounts_3)) {
			specialMoneyAmounts_3 = "0.00";
		}
		if (StringUtils.isBlank(moneyAmounts_3)) {
			moneyAmounts_3 = "0.00";
		}
		if (StringUtils.isBlank(specialMoneyAmounts_3)) {
			specialMoneyAmounts_3 = "0.00";
		}
		if (StringUtils.isBlank(moneyAmounts_3)) {
			moneyAmounts_3 = "0.00";
		}
		if (StringUtils.isBlank(specialMoneyAmounts_3)) {
			specialMoneyAmounts_3 = "0.00";
		}
		if (StringUtils.isBlank(generalMoneyAmounts_3)) {
			generalMoneyAmounts_3 = "0.00";
		}
		if (StringUtils.isBlank(moneyAmounts_5)) {
			moneyAmounts_5 = "0.00";
		}
		if (StringUtils.isBlank(specialMoneyAmounts_5)) {
			specialMoneyAmounts_5 = "0.00";
		}
		if (StringUtils.isBlank(generalMoneyAmounts_5)) {
			generalMoneyAmounts_5 = "0.00";
		}
		if (StringUtils.isBlank(taxAmounts_3)) {
			taxAmounts_3 = "0.00";
		}
		if (StringUtils.isBlank(specialTaxAmounts_3)) {
			specialTaxAmounts_3 = "0.00";
		}
		if (StringUtils.isBlank(specialInsteadOpenTaxAmounts_3)) {
			specialInsteadOpenTaxAmounts_3 = "0.00";
		}
		if (StringUtils.isBlank(generalTaxAmounts_3)) {
			generalTaxAmounts_3 = "0.00";
		}
		if (StringUtils.isBlank(taxAmounts_5)) {
			taxAmounts_5 = "0.00";
		}
		if (StringUtils.isBlank(specialTaxAmounts_5)) {
			specialTaxAmounts_5 = "0.00";
		}
		if (StringUtils.isBlank(specialInsteadOpenTaxAmounts_5)) {
			specialInsteadOpenTaxAmounts_5 = "0.00";
		}
		if (StringUtils.isBlank(generalTaxAmounts_5)) {
			generalTaxAmounts_5 = "0.00";
		}
		if (StringUtils.isBlank(otherFreeAmounts_0)) {
			otherFreeAmounts_0 = "0.00";
		}
		if (StringUtils.isBlank(salesAmounts_0)) {
			salesAmounts_0 = "0.00";
		}
		if (StringUtils.isBlank(taxControlSalesAmounts_0)) {
			taxControlSalesAmounts_0 = "0.00";
		}
		if (StringUtils.isBlank(vatCutDeclaration)) {
			vatCutDeclaration = "0.00";
		}
		if (StringUtils.isBlank(moneyAmounts_1)) {
			moneyAmounts_1 = "0.00";
		}
		if (StringUtils.isBlank(specialMoneyAmounts_1)) {
			specialMoneyAmounts_1 = "0.00";
		}
		if (StringUtils.isBlank(generalMoneyAmounts_1)) {
			generalMoneyAmounts_1 = "0.00";
		}
		if (StringUtils.isBlank(specialInsteadOpenMoneyAmounts_1)) {
			specialInsteadOpenMoneyAmounts_1 = "0.00";
		}
		if (StringUtils.isBlank(taxAmounts_1)) {
			taxAmounts_1 = "0.00";
		}
		if (StringUtils.isBlank(specialTaxAmounts_1)) {
			specialTaxAmounts_1 = "0.00";
		}
		if (StringUtils.isBlank(generalTaxAmounts_1)) {
			generalTaxAmounts_1 = "0.00";
		}
		if (StringUtils.isBlank(specialInsteadOpenTaxAmounts_1)) {
			specialInsteadOpenTaxAmounts_1 = "0.00";
		}
		if (StringUtils.isBlank(moneyAmounts_0)) {
			moneyAmounts_0 = "0.00";
		}
	}
}
