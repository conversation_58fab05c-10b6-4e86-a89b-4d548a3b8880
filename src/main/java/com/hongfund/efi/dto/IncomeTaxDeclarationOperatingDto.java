package com.hongfund.efi.dto;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.hongfund.efi.utils.BigDecimalUtils.getDecimalValue;
import static com.hongfund.efi.utils.BigDecimalUtils.getStringValue;

public class IncomeTaxDeclarationOperatingDto {
    public List<Long> ids;
    public Long id;
    public Long accountBookId;              //帐套
    public String startPeriod;              /*起始期间*/
    public String endPeriod;                /*结束期间*/
    public Integer isMonthOperating = 1;    /*是否月报*/
    public Integer collectionType = 0;      /*征收方式 0:核定征收  1:查账征收*/
    public Date writtenDate;                /*填报日期*/
    public Date declareDate;                /*申报日期*/
    public String income;                   /*收入总额*/
    public String cost;                     /*成本费用*/
    public String profit;                   /*利润总额*/
    public String offsetPreviousLoss;       /*弥补以前年度亏损*/
    public String compareResult;            /*弥补以前年度亏损和利润总额对比后的的结果*/
    public List<DimEmployeeDto> employeeList = new ArrayList<>(); /*人员信息*/
    public String employeeListContent;                  /*人员信息*/
    public String remark;                               /*备注*/

    public void setNull() {
        if (StringUtils.isBlank(income)) {
            income = "0.00";
        }
        if (StringUtils.isBlank(cost)) {
            cost = "0.00";
        }
        if (StringUtils.isBlank(profit)) {
            profit = "0.00";
        }
        if (StringUtils.isBlank(offsetPreviousLoss)) {
            offsetPreviousLoss = "0.00";
        }
        if (StringUtils.isBlank(compareResult)) {
            compareResult = "0.00";
        }
    }

    public void setPreviousLoss() {
        compareResult = getStringValue(getDecimalValue(profit).min(getDecimalValue(offsetPreviousLoss)).max(BigDecimal.ZERO));
    }
}
