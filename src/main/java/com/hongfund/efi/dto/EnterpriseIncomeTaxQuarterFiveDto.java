package com.hongfund.efi.dto;

import org.apache.commons.lang3.StringUtils;

public class EnterpriseIncomeTaxQuarterFiveDto {

    public Long id;
    public Long accountBookId;                          //帐套
    public String startPeriod;                          /*起始期间*/
    public String endPeriod;                            /*结束期间*/
    public String name;                                 /*技术成果名称*/
    public String type;                                 /*技术成果类型*/
    public String code;                                 /*技术成果编号*/
    public String fairValue;                            /*公允价值*/
    public String taxBase;                              /*计税基础*/
    public String acquisitionDate;                      /*取得股权时间（年月日）*/
    public String deferredIncome;                       /*递延所得*/
    public String companyName;                          /*企业名称*/
    public String taxPayerCode;                         /*纳税人识别号*/
    public String taxAuthorities;                       /*主管税务机关*/
    public String investor;                             /*与投资方是否为*/
    public String remark;                               /*备注*/
    public String writePeriod;                          /*填表日期（年月）*/

    public void setNull() {
        if (StringUtils.isBlank(fairValue)) {
            fairValue = "0.00";
        }
        if (StringUtils.isBlank(taxBase)) {
            taxBase = "0.00";
        }
        if (StringUtils.isBlank(deferredIncome)) {
            deferredIncome = "0.00";
        }
    }
}
