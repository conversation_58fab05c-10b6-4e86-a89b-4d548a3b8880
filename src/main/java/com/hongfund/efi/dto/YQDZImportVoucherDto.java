package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class YQDZImportVoucherDto {

	/**
	 * "",  辅助类型
	 */
	public String assistantType;

	/**
	 * "", 辅助编码
	 */
	public String assistantCode;

	/**
	 * "kehui",  制单人姓名
	 */
	public String makerName;

	/**
	 * "202106", 会计期间
	 */
	public String period;

	/**
	 * 0,  附件数
	 */
	public Long receiptNum;

	/**
	 * "\u5f00\u5177\u4e13\u7968",  摘要
	 */
	public String summary;

	/**
	 * "1122002", 科目编码  "1002002USD"外币的科目编码
	 */
	public String titleCode;

	/**
	 * "1", 凭证号
	 */
	public String number;

	/**
	 * "2021-06-30" 凭证日期
	 */
	public String journalDate;

	/**
	 * "0",    贷方金额
	 */
	public BigDecimal creditAmount = BigDecimal.ZERO;

	/**
	 * "162984",   借方金额
	 */
	public BigDecimal debitAmount = BigDecimal.ZERO;

	/**
	 * "0", 借方数量
	 */
	public BigDecimal debitAmountQ = BigDecimal.ZERO;

	/**
	 * "0", 借方外币
	 */
	public BigDecimal debitAmountF = BigDecimal.ZERO;

	/**
	 * 0,
	 */
	public BigDecimal unitPrice = BigDecimal.ZERO;

	/**
	 * 0,  汇率
	 */
	public BigDecimal exchangeRate = BigDecimal.ZERO;

	/**
	 * "0", 贷方数量
	 */
	public BigDecimal creditAmountQ = BigDecimal.ZERO;

	/**
	 * "0", 贷方外币
	 */
	public BigDecimal creditAmountF = BigDecimal.ZERO;

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
