package com.hongfund.efi.dto;

/**
 * 发票信息查询接口参数
 */
public class EntInvoiceRequestDto {
    /**
     * 纳税人识别号
     */
    public String taxNo;
    /**
     * 企业名称
     */
    public String entName;
    /**
     * 科目,1:进项;2:销项
     */
    public String subject;
    /**
     * 发票类型
     * 01 增值税专用发票 02 货运运输业增值税专用发票 03 机动车销售统一发 票 04 增值税普通发票： 10 增值税普通发票 （电子）
     * 11 增值税普通发票
     * （卷式）
     * 14 通行费电子票 15 二手车销售统一发 票
     */
    public String invoiceType;
    /**
     * 开始日期:格式YYYY-MM-DD，不能为空
     */
    public String startDate;
    /**
     * 结束日期: 格式YYYY-MM-DD,不能为空
     */
    public String endDate;
    /**
     * 获取开始日期:格式YYYY-MM-DD，不能为空
     */
    public String acquiStartDate;
    /**
     * 获取结束日期: 格式YYYY-MM-DD,不能为空
     */
    public String acquiEndDate;
}
