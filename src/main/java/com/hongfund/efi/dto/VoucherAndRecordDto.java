package com.hongfund.efi.dto;

import java.math.BigDecimal;

import org.apache.commons.lang3.builder.ToStringBuilder;

public class VoucherAndRecordDto {
	public Long id;				//凭证ID
	public String foresubText;	//凭证分录科目全级次名称
	public String period;		//凭证期间
	public BigDecimal dfmoney;	//贷方余额
	
	
	public VoucherAndRecordDto(Long id, String foresubText, String period, BigDecimal dfmoney) {
		super();
		this.id = id;
		this.foresubText = foresubText;
		this.period = period;
		this.dfmoney = dfmoney;
	}

	public VoucherAndRecordDto() {
		
	}
	
	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}


}
