package com.hongfund.efi.dto;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
public class InventoryAndDimDto {
    /**
     * 单据日期
     */
    public Date orderDate;
    /**
     * 单据类型 1：采购入库，2：产品入库
     */
    public Integer receiptType;
    /**
     * 规格
     */
    public String specification;
    /**
     * 计量单位
     */
    public String measuringUnit;
    /**
     * 数量
     */
    public BigDecimal number;
    /**
     * 单价
     */
    public BigDecimal price;
    /**
     * 金额
     */
    public BigDecimal money;
    /**
     * 税率
     */
    public BigDecimal taxRate;
    /**
     * 税额
     */
    public BigDecimal taxMoney;
    /**
     * 对方科目
     */
    public String oppositeSubject;
    /**
     * 供应商
     */
    public String supplier;
    /**
     * 税金科目
     */
    public String taxSubject;
    /**
     * 项目编码
     */
    public String no;
    /**
     * 商品类型
     */
    public Integer type;
    /**
     * 项目名称
     */
    public String name;
}
