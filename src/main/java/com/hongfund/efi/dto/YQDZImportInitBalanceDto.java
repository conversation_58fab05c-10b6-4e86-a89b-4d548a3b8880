package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;
import java.util.Map;

public class YQDZImportInitBalanceDto {

    /**
     * "1122", 科目编码
     */
    public String titleCode;
    /**
     * "", 外币编码（USD...）
     */
    public String fcurCode;
    /**
     * "2020", 年份
     */
    public String year;
    /**
     * "", 计量单位
     */
    public String unit;
    /**
     * "",  辅助类型
     */
    public String assistantType;

    /**
     * "", 辅助编码
     */
    public String assistantCode;
    /**
     * 原币借方期初
     */
    public Map<String, BigDecimal> fcurDebitOpeningBalance;
    /**
     * 借方期初
     */
    public Map<String, BigDecimal> debitOpeningBalance;
    /**
     * 贷方本期
     */
    public Map<String, BigDecimal> creditAmount;
    /**
     * 借方本期
     */
    public Map<String, BigDecimal> debitAmount;
    /**
     * 原币借方本期
     */
    public Map<String, BigDecimal> fcurDebitAmount;
    /**
     * 数量贷方期末
     */
    public Map<String, BigDecimal> quantityCreditEndBalance;
    /**
     * 借方期末
     */
    public Map<String, BigDecimal> debitEndBalance;
    /**
     * 数量贷方本期
     */
    public Map<String, BigDecimal> quantityCreditAmount;
    /**
     * 贷方期末
     */
    public Map<String, BigDecimal> creditEndBalance;
    /**
     * 原币贷方期初
     */
    public Map<String, BigDecimal> fcurCreditOpeningBalance;
    /**
     * 借方本年累计
     */
    public Map<String, BigDecimal> debitYearTotalAmount;
    /**
     * 原币借方期末
     */
    public Map<String, BigDecimal> fcurDebitEndBalance;
    /**
     * 数量借方期末
     */
    public Map<String, BigDecimal> quantityDebitEndBalance;
    /**
     * 数量借方本期
     */
    public Map<String, BigDecimal> quantityDebitAmount;
    /**
     * 原币贷方期末
     */
    public Map<String, BigDecimal> fcurCreditEndBalance;
    /**
     * 原币贷方本期
     */
    public Map<String, BigDecimal> fcurCreditAmount;
    /**
     * 数量借方期初
     */
    public Map<String, BigDecimal> quantityDebitOpeningBalance;
    /**
     * 数量借方本年累计
     */
    public Map<String, BigDecimal> quantityDebitYearTotalAmount;
    /**
     * 贷方期初
     */
    public Map<String, BigDecimal> creditOpeningBalance;
    /**
     * 贷方本年累计
     */
    public Map<String, BigDecimal> creditYearTotalAmount;
    /**
     * 数量贷方本年累计
     */
    public Map<String, BigDecimal> quantityCreditYearTotalAmount;
    /**
     * 原币借方本年累计
     */
    public Map<String, BigDecimal> fcurDebitYearTotalAmount;
    /**
     * 原币贷方本年累计
     */
    public Map<String, BigDecimal> fcurCreditYearTotalAmount;
    /**
     * 数量贷方期初
     */
    public Map<String, BigDecimal> quantityCreditOpeningBalance;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
