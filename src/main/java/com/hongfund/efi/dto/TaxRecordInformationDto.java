package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

/**
 * 报税情况
 */
public class TaxRecordInformationDto {
	/**
	 * 加盟商编号
	 */
	public String partyId;
	/**
	 * 加盟商名称
	 */
	public String partyName;
	/**
	 * 税种
	 */
	public String taxType;
	/**
	 * 申报状态
	 */
	public String taxState;
	/**
	 * 数量
	 */
	public Long count = 0L;



	public TaxRecordInformationDto() {
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
