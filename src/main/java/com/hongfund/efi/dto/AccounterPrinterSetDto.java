package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

public class AccounterPrinterSetDto {

	public Long id;
	public String voucherPrint;            // 凭证边距
	public String voucherWidth;            // 凭证纸的宽度
	public String voucherHeight;           // 凭证纸的高度
	public String printVoucherDir;     	   // 凭证打印方向
	public String voucherRows;             // 凭证行数
	public String aFourVoucher;            // A4纸打印凭证
	public String lh;			   		   // 行高
	public String sheet;				   // 报表打印边距
	public String sheetWidth;              // 报表的宽度
	public String sheetHeight;             // 报表的高度
	public String printSheetDir;           // 报表打印方向
	public Long accounterId;               // 账号id
	public String printWay;           	   // 打印方式
	public String voucherFont;			   // 凭证字体
	public String sheetFont; 			   // 报表字体大小
	/**
	 * 报税人
	 */
	public String taxPreparer;

	/**
	 * 装订人
	 */
	public String bindPeople;

	/**
	 * 记账人
	 */
	public String bookKeeper;

	/**
	 * 出纳
	 */
	public String teller;

	/**
	 * 制单人
	 */
	public String documentMaker;

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

}
