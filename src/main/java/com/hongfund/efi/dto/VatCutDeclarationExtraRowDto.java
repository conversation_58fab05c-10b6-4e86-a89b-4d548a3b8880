package com.hongfund.efi.dto;

import com.hongfund.efi.utils.BigDecimalUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.Date;

/**
 * 免税项目表
 */
public class VatCutDeclarationExtraRowDto {
	public Long id;
	public Date gmtCreate;
	public Date gmtModified;
	public Long accountBookId;

	/**
	 * 开始期间
	 */
	public String startPeriod;
	/**
	 * 结束期间
	 */
	public String endPeriod;
	/**
	 * 序号
	 */
	public Integer no;
	/**
	 * 免税性质代码
	 */
	public Integer type;
	/**
	 * 免征增值税项目销售额
	 */
	public String totalBalance;
	/**
	 * 实际扣除金额
	 */
	public String actualBalance;
	/**
	 * 扣除后免税销售额
	 */
	public String extraBalance;
	/**
	 * 对应的进项税额
	 */
	public String totalTax;
	/**
	 * 免税额
	 */
	public String actualTax;

	public VatCutDeclarationExtraRowDto() {
	}

	public void countExtraBalance() {
		extraBalance = BigDecimalUtils.subtract(totalBalance, actualBalance);
	}

	public void addData(VatCutDeclarationExtraRowDto data) {
		totalBalance = BigDecimalUtils.sum(totalBalance, data.totalBalance);
		actualBalance = BigDecimalUtils.sum(actualBalance, data.actualBalance);
		extraBalance = BigDecimalUtils.sum(extraBalance, data.extraBalance);
		totalTax = BigDecimalUtils.sum(totalTax, data.totalTax);
		actualTax = BigDecimalUtils.sum(actualTax, data.actualTax);
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
