package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class GeneralVatDeclarationDataDto {
    /**
     * 一般项目本期数
     */
    public List<String> generalMonthBalance = new ArrayList<>();
    /**
     * 一般项目本年累计
     */
    public List<String> generalYearBalance = new ArrayList<>();
    public String endPeriod;
    public String startPeriod;
    public Integer isSave = 0;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
