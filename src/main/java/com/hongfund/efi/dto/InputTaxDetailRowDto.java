package com.hongfund.efi.dto;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
public class InputTaxDetailRowDto {
    public String id;
    /**
     * 帐套id
     */
    public String accountBookId;
    /**
     * 期间
     */
    public String period;
    /**
     * 栏次
     */
    public String row;
    /**
     * 份数
     */
    public String quantity;
    /**
     *金额
     */
    public String amount;
    /**
     * 税额
     */
    public String tax;

    public InputTaxDetailRowDto() {

    }

    public InputTaxDetailRowDto(String id, String accountBookId, String period, String row, String quantity, String amount, String tax) {
        this.id = id;
        this.accountBookId = accountBookId;
        this.period = period;
        this.row = row;
        this.quantity = quantity;
        this.amount = amount;
        this.tax = tax;
    }

    public InputTaxDetailRowDto(String accountBookId, String period, String row) {
        this.accountBookId = accountBookId;
        this.period = period;
        this.row = row;
    }

    /**
     * 根据关键字，获取附表二对应数据（主表用）
     */
    public String getColumn(String column){
        if ("G".equals(column)) {
            return tax;
        }
        return null;
    }

    public void setNull() {
        if (StringUtils.isBlank(quantity)) {
            quantity = "0";
        }
        if (StringUtils.isBlank(amount)) {
            amount = "0.00";
        }
        if (StringUtils.isBlank(tax)) {
            tax = "0.00";
        }
    }
}
