package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.Date;

/**
 * 风险报告数据表
 */
public class TaxReportDataDto {
	public Long id;
	public Date gmtCreate;
	public Date gmtModified;
	/**
	 * 税号
	 */
	public String taxCode;
	/**
	 * 类型
	 */
	public String type;
	/**
	 * 数据地址
	 */
	public String dataUrl;

	public TaxReportDataDto() {
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
