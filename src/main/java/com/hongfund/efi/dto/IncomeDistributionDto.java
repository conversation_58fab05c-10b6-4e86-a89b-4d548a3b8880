package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

public class IncomeDistributionDto {

	/**账套id*/
	public Long accountBookId;
	/**坐标*/
	public Integer rowIndex;
	/**期间*/
	public String period;
	/**本年收益*/
	public String income;
	/**行次（左）*/
	public String incomeRow;
	/**金额（左）*/
	public String incomeBalance;
	/**收分配益*/
	public String distribution;
	/**行次（右）*/
	public String distributionRow;
	/**金额（右）*/
	public String distributionBalance;

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

}
