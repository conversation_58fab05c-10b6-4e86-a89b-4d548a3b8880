package com.hongfund.efi.dto;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;

public class DimBalanceValueDto {
	
	public Long id;                 /*主键*/
	public Long accountBookId;                             	//账簿
	public String dfbalance;         						//贷方余额，本位币
	public String dfbalanceOrg;      						//贷方余额，原币
	public String dfnumber;          						//贷方累计数量
	public String endbalance;        						//期末余额，本位币
	public String endbalanceOrg;     						//期末余额，原币
	public String endnumber;         						//期末数量
	public String initbalance;       						//期初余额，本位币
	public String initbalanceOrg;    						//期初余额，原币
	public String initnumber;        						//期初数量
	public String jfbalance;        						//借方余额，本位币
	public String jfbalanceOrg;     						//借方余额，原币
	public String jfnumber;         						//借方累计数量
	public String period;                                  	//期间
	public Long subjectId;                               	//科目
	public String subjectNo;                               	//科目编码
	public Long subjectDimRelationId;                	//科目辅助核算组合关联表
	
	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
	
	public void setNull() {
		if(StringUtils.isBlank(initbalance)){
			initbalance = "0";
		}
		if(StringUtils.isBlank(initbalanceOrg)){
			initbalanceOrg = "0";
		}
		if(StringUtils.isBlank(initnumber)){
			initnumber = "0";
		}
		if(StringUtils.isBlank(jfbalance)){
			jfbalance = "0";
		}
		if(StringUtils.isBlank(jfbalanceOrg)){
			jfbalanceOrg = "0";
		}
		if(StringUtils.isBlank(jfnumber)){
			jfnumber = "0";
		}
		if(StringUtils.isBlank(dfbalance)){
			dfbalance = "0";
		}
		if(StringUtils.isBlank(dfbalanceOrg)){
			dfbalanceOrg = "0";
		}
		if(StringUtils.isBlank(dfnumber)){
			dfnumber = "0";
		}
		if(StringUtils.isBlank(endbalance)){
			endbalance = "0";
		}
		if(StringUtils.isBlank(endbalanceOrg)){
			endbalanceOrg = "0";
		}
		if(StringUtils.isBlank(endnumber)){
			endnumber = "0";
		}
	}
	
}
