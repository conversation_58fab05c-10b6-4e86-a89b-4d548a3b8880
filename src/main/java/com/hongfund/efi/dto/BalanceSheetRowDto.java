package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

public class BalanceSheetRowDto {
	
	public String asset;					//资产
	public String assetRow;					//行次（左）
	public String assetEndBalance;			//期末余额（左）
	public String assetInitBalance;			//年初余额（左）
	public String equity;					//负债和所有者权益
	public String equityRow;				//行次（右）
	public String equityEndBalance;			//期末余额（右）
	public String equityInitBalance;		//年初余额（右）
	
	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

}
