package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.List;

/**
 * <AUTHOR>
 */
public class VatCutDeclarationExtraDto {

	public List<VatCutDeclarationExtraRowDto> data;
	public VatCutDeclarationExtraRowDto total;
	/**
	 * 起始期间
	 */
	public String startPeriod;
	/**
	 * 结束期间
	 */
	public String endPeriod;

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

}
