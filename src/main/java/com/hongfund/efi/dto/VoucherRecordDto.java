package com.hongfund.efi.dto;

import com.hongfund.efi.domain.SubjectDimRelation;
import com.hongfund.efi.utils.BeanMapper;
import com.hongfund.efi.utils.DBCache;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 */
public class VoucherRecordDto {

	public Long id;

	/**
	 *  帐套id
	 */
	public Long accountBookId;
	/**
	 *  辅助核算关系
	 */
	public SubjectDimRelationDto relation;
	/**
	 *  科目与辅助核算关联关系
	 */
	public Long subjectDimRelationId;
	/**
	 *  外币汇率
	 */
	public BigDecimal currencyexchange = BigDecimal.ZERO;
	/**
	 *  贷方外币金额
	 */
	public BigDecimal dfcurrency = BigDecimal.ZERO;
	/**
	 *  贷方金额
	 */
	public BigDecimal dfmoney = BigDecimal.ZERO;
	/**
	 *  贷方原币金额
	 */
	public BigDecimal dfmoneyOrg = BigDecimal.ZERO;
	/**
	 *  贷方数量
	 */
	public BigDecimal dfnumber = BigDecimal.ZERO;
	/**
	 *  汇率
	 */
	public BigDecimal exchangeRate = BigDecimal.ZERO;
	/**
	 *  科目全级次名称
	 */
	public String foresubText;
	/**
	 *  借方外币金额
	 */
	public BigDecimal jfcurrency = BigDecimal.ZERO;
	/**
	 *  借方金额
	 */
	public BigDecimal jfmoney = BigDecimal.ZERO;
	/**
	 *  借方原币金额
	 */
	public BigDecimal jfmoneyOrg = BigDecimal.ZERO;
	/**
	 *  借方数量
	 */
	public BigDecimal jfnumber = BigDecimal.ZERO;
	/**
	 *  subject
	 */
	public Long subjectId;
	/**
	 *  凭证摘要
	 */
	public String summary;
	/**
	 *  单价
	 */
	public BigDecimal unitPrice;
	/**
	 *  voucher
	 */
	public Long voucherId;
	/**
	 * 计量单位（可空）
	 */
	public String measuringUnit;
	/**
	 * 规格型号
	 */
	public String model;
	/**
	 * 外币符号（可空）
	 */
	public String currSymbol;
	/**
	 * 余额数据
	 */
	public BalanceValueDto balance = new BalanceValueDto();

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

	public VoucherRecordDto() {

	}

	public VoucherRecordDto(SubjectDimRelationDto relation, BigDecimal dfmoney, BigDecimal dfnumber, BigDecimal dfmoneyOrg, String foresubText, BigDecimal jfmoney, BigDecimal jfnumber, BigDecimal jfmoneyOrg, Long subjectId, String summary) {
		this.relation = relation;
		this.dfmoney = dfmoney;
		this.dfnumber = dfnumber;
		this.dfmoneyOrg = dfmoneyOrg;
		this.foresubText = foresubText;
		this.jfmoney = jfmoney;
		this.jfnumber = jfnumber;
		this.jfmoneyOrg = jfmoneyOrg;
		this.subjectId = subjectId;
		this.summary = summary;
		this.calculatePriceAndExchangeRate();
	}

	public VoucherRecordDto(SubjectDimRelationDto relation, BigDecimal dfmoney, String foresubText, BigDecimal jfmoney, Long subjectId, String summary) {
		this.relation = relation;
		this.dfmoney = dfmoney;
		this.foresubText = foresubText;
		this.jfmoney = jfmoney;
		this.subjectId = subjectId;
		this.summary = summary;
	}

	public VoucherRecordDto(BigDecimal dfmoney, BigDecimal dfnumber, BigDecimal dfmoneyOrg, String foresubText, BigDecimal jfmoney, BigDecimal jfnumber, BigDecimal jfmoneyOrg, String summary, Long subjectId) {
		this(null, dfmoney, dfnumber, dfmoneyOrg, foresubText, jfmoney, jfnumber, jfmoneyOrg, subjectId, summary);
	}

	public VoucherRecordDto(BigDecimal dfmoney, String foresubText, BigDecimal jfmoney, String summary, Long subjectId) {
		this(null, dfmoney, foresubText, jfmoney, subjectId, summary);
	}

	public void setNull() {
		if(currencyexchange == null) {
			currencyexchange = BigDecimal.ZERO;
		}
		if(dfcurrency == null) {
			dfcurrency = BigDecimal.ZERO;
		}
		if(dfmoney == null) {
			dfmoney = BigDecimal.ZERO;
		}
		if(dfmoneyOrg == null) {
			dfmoneyOrg = BigDecimal.ZERO;
		}
		if(dfnumber == null) {
			dfnumber = BigDecimal.ZERO;
		}
		if(exchangeRate == null) {
			exchangeRate = BigDecimal.ZERO;
		}
		if(jfcurrency == null) {
			jfcurrency = BigDecimal.ZERO;
		}
		if(jfmoney == null) {
			jfmoney = BigDecimal.ZERO;
		}
		if(jfmoneyOrg == null) {
			jfmoneyOrg = BigDecimal.ZERO;
		}
		if(jfnumber == null) {
			jfnumber = BigDecimal.ZERO;
		}
	}

	/**
	 * 判断分录是否匹配指定的科目和辅助核算
	 */
	public boolean isMatch(DBCache cache, SubjectDto subjectDto) {
		if (subjectDto.relation == null) {
			return StringUtils.startsWith(foresubText, subjectDto.no);
		}
		else {
			if (subjectDimRelationId != null) {
				SubjectDimRelation relation = cache.getRelationById(subjectDimRelationId);
				return relation.isMatch(BeanMapper.map(subjectDto.relation, SubjectDimRelationDto.class));
			}
		}
		return false;
	}

	/**
	 * 计算单价和汇率
	 */
	public void calculatePriceAndExchangeRate() {
		if (BigDecimal.ZERO.compareTo(jfnumber) != 0) {
			if (BigDecimal.ZERO.compareTo(jfmoneyOrg) != 0) {
				unitPrice = jfmoneyOrg.divide(jfnumber, 4, RoundingMode.HALF_UP);
			}
			else {
				unitPrice = jfmoney.divide(jfnumber, 4, RoundingMode.HALF_UP);
			}
		}
		if (BigDecimal.ZERO.compareTo(dfnumber) != 0) {
			if (BigDecimal.ZERO.compareTo(dfmoneyOrg) != 0) {
				unitPrice = dfmoneyOrg.divide(dfnumber, 4, RoundingMode.HALF_UP);
			}
			else {
				unitPrice = dfmoney.divide(dfnumber, 4, RoundingMode.HALF_UP);
			}
		}
		if (BigDecimal.ZERO.compareTo(jfmoneyOrg) != 0) {
			exchangeRate = jfmoney.divide(jfmoneyOrg, 4, RoundingMode.HALF_UP);
		}
		if (BigDecimal.ZERO.compareTo(dfmoneyOrg) != 0) {
			exchangeRate = dfmoney.divide(dfmoneyOrg, 4, RoundingMode.HALF_UP);
		}
	}

}
