package com.hongfund.efi.dto;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 生产领料列表行
 */
public class InventoryUseRowDto {
	public Long id;
	public Long accountBookId;
	public String period;
	/**
	 * 名称
	 */
	public String name;
	/**
	 * 编码
	 */
	public String no;
	/**
	 * 计量单位
	 */
	public String measureUnit;
	/**
	 * 规格型号
	 */
	public String model;
	/**
	 * 本月库存余额：金额
	 */
	public BigDecimal balance = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
	/**
	 * 本月库存余额：数量
	 */
	public BigDecimal number = BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);
	/**
	 * 本月库存余额：单价
	 */
	public BigDecimal unitPrice = BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);
	/**
	 * 本期领用：金额
	 */
	public BigDecimal balanceUse = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
	/**
	 * 本期领用：数量
	 */
	public BigDecimal numberUse = BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);
	/**
	 * 本期领用：单价
	 */
	public BigDecimal unitPriceUse = BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);
	/**
	 * 暂估：金额
	 */
	public BigDecimal balanceEstimate = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
	/**
	 * 暂估：数量
	 */
	public BigDecimal numberEstimate = BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);
	/**
	 * 暂估：单价
	 */
	public BigDecimal unitPriceEstimate = BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);
	/**
	 * 暂估：凭证id
	 */
	public Long voucherIdEstimate;
	/**
	 * 本月结存：金额
	 */
	public BigDecimal balanceAdjusting = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
	/**
	 * 本月结存：数量
	 */
	public BigDecimal numberAdjusting = BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);
	/**
	 * 本月结存：单价
	 */
	public BigDecimal unitPriceAdjusting = BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);

	public void add(InventoryUseRowDto row) {
		balance = balance.add(row.balance);
		number = number.add(row.number);
		balanceUse = balanceUse.add(row.balanceUse);
		numberUse = numberUse.add(row.numberUse);
		balanceEstimate = balanceEstimate.add(row.balanceEstimate);
		numberEstimate = numberEstimate.add(row.numberEstimate);
		balanceAdjusting = balanceAdjusting.add(row.balanceAdjusting);
		numberAdjusting = numberAdjusting.add(row.numberAdjusting);
	}

	/**
	 * 计算结存
	 */
	public void countAdjusting() {
		balanceAdjusting = balance.subtract(balanceUse);
		numberAdjusting = number.subtract(numberUse);
		if (voucherIdEstimate != null) {
			balanceAdjusting = balanceAdjusting.add(balanceEstimate);
			numberAdjusting = numberAdjusting.add(numberEstimate);
		}
		if (numberAdjusting.compareTo(BigDecimal.ZERO) != 0) {
			unitPriceAdjusting = balanceAdjusting.divide(numberAdjusting, 4, RoundingMode.HALF_UP);
		}
	}
}
