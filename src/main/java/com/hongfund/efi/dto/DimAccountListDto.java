package com.hongfund.efi.dto;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;

public class DimAccountListDto {
	
	public List<DimItemDto> dimItemList = new ArrayList<>();					// 项目
	public List<DimCustomerDto> dimCustomerList = new ArrayList<>();			// 客户
	public List<DimProviderDto> dimProviderList = new ArrayList<>();			// 供应商
	public List<DimDepartmentDto> dimDepartmentList = new ArrayList<>();		// 部门
	public List<DimEmployeeDto> dimEmployeeList = new ArrayList<>();			// 员工
	public List<DimInventoryDto> dimInventoryList = new ArrayList<>();			// 存货

	
	public DimAccountListDto() {
	}


	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
	
}
