package com.hongfund.efi.dto;

import java.util.ArrayList;
import java.util.List;

public class JspBalanceOutside {
    public JspBalanceSecond interests= new JspBalanceSecond();
    public JspBalanceSecond cost= new JspBalanceSecond();
    public JspBalanceSecond profitAndLoss= new JspBalanceSecond();
    public List<JspBalanceSecond> list= new ArrayList<>();
    public int total;
    public JspBalanceSecond assest= new JspBalanceSecond();
    public JspBalanceSecond liabilities= new JspBalanceSecond();
}
