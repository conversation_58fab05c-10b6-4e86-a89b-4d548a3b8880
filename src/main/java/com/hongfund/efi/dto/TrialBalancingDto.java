package com.hongfund.efi.dto;


import org.apache.commons.lang3.builder.ToStringBuilder;

public class TrialBalancingDto {
	
	public String qcjfbalance ;		//期初借方余额
	public String qcdfbalance ;		//期初贷方余额
	public String jfljbalance ;		//累计借方余额
	public String dfljbalance ;		//累计贷方余额
	public String message ;				//资产负债表是否平衡提示
	public Integer qcstate ;			//期初天平状态0：/ ;1：——; 2：\
	public Integer ljstate ;			//累计天平状态0：/ ;1：——; 2：\
	public String qcresult;			//期初金额之差
	public String ljresult; 		//累计金额之差
	
	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

}
