package com.hongfund.efi.dto;

import com.hongfund.efi.domain.SubjectDimRelation;
import org.apache.commons.lang3.builder.ToStringBuilder;

/**
 * <AUTHOR>
 */
public class FixedAssetsSubjectDto {
    public Long id;
    /**
     * 科目编码
     */
    public String no;
    /**
     * 科目名称
     */
    public String text;
    /**
     * 辅助核算
     */
    public String dimAccountType;
    /**
     * 账套id
     */
    public long accountBookId;
    /**
     * 长名称
     */
    public String longText;
    /**
     * 辅助项目名称
     */
    public String dimAccountName;
    /**
     * 辅助项目编码
     */
    public String dimAccountNo;
    /**
     * 辅助核算关系
     */
    public SubjectDimRelation relation;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
