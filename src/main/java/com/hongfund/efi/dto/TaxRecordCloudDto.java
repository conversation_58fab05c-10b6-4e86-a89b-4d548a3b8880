package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.Date;

/**
 * 个税数据云存储
 */
public class TaxRecordCloudDto {
	public Long id;
	public Date gmtCreate;
	public Date gmtModified;
	public Long accountBookId;
	/**
	 * 税号
	 */
	public String taxCode;
	/**
     * 数据地址
	 */
	public String cloudUrl;
	/**
	 * 数据状态 0：空闲 1：繁忙
	 */
	public Integer cloudState;

	public TaxRecordCloudDto() {
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
