package com.hongfund.efi.dto;

import java.util.Date;

public class AppCompanyDto {
    public Long id;
    public Date gmtCreate;
    public Date gmtModified;

    /**
     * 公司名称
     */
    public String name;
    /**
     * 用户id
     */
    public Long userId;
    /**
     * 增值税
     */
    public String addtax;
    /**
     * 会计制度
     */
    public String accountingSystem;
    /**
     * 公司状态
     */
    public Integer status = 0;
    /**
     * 税号
     */
    public String taxCode;
    /**
     * 电子税务局登录名
     */
    public String nationalName;
    /**
     * 电子税务局密码
     */
    public String nationalPwd;
    /**
     * 办税人手机号
     */
    public String taxPayerPhone;
    /**
     * 主管税务机关
     */
    public String taxAuthorities;
    /**
     * 验证方式
     */
    public Integer typeAuth;
    /**
     * 所属省份
     */
    public String province;
    /**
     * 所属城市
     */
    public String city;
    /**
     * 登录方式
     */
    public Integer loginWay;
    /**
     * 代理社会信用代码
     */
    public String socialCreditCodeAgent;
    /**
     * 是否当前企业
     */
    public Integer isDefault = 0;
}
