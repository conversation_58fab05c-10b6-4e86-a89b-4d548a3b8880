package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

/**
 * <AUTHOR>
 */
public class VatCutDeclarationRowDto {

	/**
	 * 主键
	 */
	public Long id;
	/**
	 * 账套id
	 */
	public Long accountBookId;
	/**
	 * start期间
	 */
	public String startPeriod;
	/**
	 * end期间
	 */
	public String endPeriod;
	/**
	 * 序号
	 */
	public Integer no;
	/**
	 * 减免类型
	 */
	public Integer type;
	/**
	 * 发生额
	 */
	public String balance;
	/**
	 * 调减额
	 */
	public String deductBalance;
	/**
	 * 实际抵减税额
	 */
	public String actualBalance;
	/**
	 * 应抵减税额
	 */
	public String countBalance;
	/**
	 * 期初余额
	 */
	public String initBalance;
	/**
	 * 期末余额
	 */
	public String endBalance;

	/**
	 * 本期服务、不动产和无形资产价税合计额（免税销售额）
	 */
	public String salesBalance;

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

	/**
	 * 根据关键字，获取附表四对应数据（主表用）
	 */
	public String getColumn(String column){
		if ("F".equals(column)) {
			return actualBalance;
		}
		return null;
	}

}
