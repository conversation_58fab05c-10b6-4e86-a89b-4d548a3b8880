package com.hongfund.efi.dto;

import com.hongfund.efi.domain.Subject;
import com.hongfund.efi.utils.DBCache;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * 模板分录
 */
public class VoucherRecordTemplateDto {

	/**
	 * 贷方金额
	 */
	public BigDecimal dfmoney = BigDecimal.ZERO;
	/**
	 * 借方金额
	 */
	public BigDecimal jfmoney = BigDecimal.ZERO;
	/**
	 * 科目长名称
	 */
	public String subjectLongText;
	/**
	 * 科目编码
	 */
	public String subjectNo;
	/**
	 * 科目id
	 */
	public Long subjectId;
	/**
	 * 计量单位（可空）
	 */
	public String measuringUnit;
	/**
	 * 外币符号（可空）
	 */
	public String currSymbol;
	/**
	 * 借贷方向  1：借  -1：贷
	 */
	public Integer dir;
	/**
	 * 凭证摘要
	 */
	public String summary;
	/**
	 *  辅助核算关系
	 */
	public SubjectDimRelationDto relation;
	/**
	 *  科目与辅助核算关联关系
	 */
	public Long subjectDimRelationId;
	/**
	 * 辅助项目编码
	 */
	public String showNo;
	/**
	 * 辅助项目名称
	 */
	public String showLongText;
	/**
	 * 匹配到的下级科目编码
	 */
	public String leafNo;
	/**
	 * 匹配到的下级科目长名称
	 */
	public String leafLongText;

	/**
	 * 如果有长名称，根据长名称设置科目编码
	 */
	public void fixSubjectNo(DBCache cache) {
		if (StringUtils.isNotBlank(subjectLongText)) {
			Subject subject = cache.getSubjectByLongText(subjectLongText);
			if (subject != null) {
				subjectNo = subject.no;
			}
		}
	}
}
