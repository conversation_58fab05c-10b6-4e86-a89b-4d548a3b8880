package com.hongfund.efi.dto;

import com.hongfund.efi.domain.BaseBalance;
import com.hongfund.efi.domain.Subject;
import com.hongfund.efi.domain.Voucher;
import com.hongfund.efi.domain.VoucherRecord;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static com.hongfund.efi.utils.FormatUtils.DATE_FORMAT_YMD_;

public class SubsidiaryLedgerRowDto {
	public String no;
	public String longText;
	public String foresubText;
	public String date;
	public String voucherNo;
	public String summary;
	public String direction;
	public BigDecimal jfbalance = BigDecimal.ZERO;
	public BigDecimal jfbalanceOrg = BigDecimal.ZERO;
	public BigDecimal jfnumber = BigDecimal.ZERO;
	public BigDecimal jfprice = BigDecimal.ZERO;
	public BigDecimal dfbalance = BigDecimal.ZERO;
	public BigDecimal dfbalanceOrg = BigDecimal.ZERO;
	public BigDecimal dfnumber = BigDecimal.ZERO;
	public BigDecimal dfprice = BigDecimal.ZERO;
	public BigDecimal currentbalance = BigDecimal.ZERO;
	public BigDecimal currentbalanceOrg = BigDecimal.ZERO;
	public BigDecimal currentnumber = BigDecimal.ZERO;
	public BigDecimal currentprice = BigDecimal.ZERO;
	public BigDecimal exchangeRate;
	public Long voucherId = -1L;
	public Long voucherRecordId = -1L;

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

	/**
	 * 设置单价，借贷方向
	 */
	public void setData(Subject subject) {
		// 单价等于总价除以数量，取4位小数（如果科目有外币核算，单价应该是外币单价）
		if(jfnumber.compareTo(BigDecimal.ZERO) != 0) {
			if(subject.currSymbol == null) {
				jfprice = jfbalance.divide(jfnumber, 4, RoundingMode.HALF_UP);
			}
			else {
				jfprice = jfbalanceOrg.divide(jfnumber, 4, RoundingMode.HALF_UP);
			}
		}
		if(dfnumber.compareTo(BigDecimal.ZERO) != 0) {
			if(subject.currSymbol == null) {
				dfprice = dfbalance.divide(dfnumber, 4, RoundingMode.HALF_UP);
			}
			else {
				dfprice = dfbalanceOrg.divide(dfnumber, 4, RoundingMode.HALF_UP);
			}
		}
		if(currentnumber.compareTo(BigDecimal.ZERO) != 0) {
			if(subject.currSymbol == null) {
				currentprice = currentbalance.divide(currentnumber, 4, RoundingMode.HALF_UP);
			}
			else {
				currentprice = currentbalanceOrg.divide(currentnumber, 4, RoundingMode.HALF_UP);
			}
		}

		//余额为0为平
		if(currentbalance.compareTo(BigDecimal.ZERO) == 0) {
			direction = "平";
		}
		else {
			if(currentbalance.compareTo(BigDecimal.ZERO) > 0) {
				direction = subject.direction == 1 ? "借" : "贷";
			}
			else {
				direction = subject.direction == 1 ? "贷" : "借";
				currentbalance = currentbalance.negate();
				currentbalanceOrg = currentbalanceOrg.negate();
				currentnumber = currentnumber.negate();
			}
		}
	}

	public void setDataByVoucher(Voucher voucher, VoucherRecord voucherRecord) {
		date = DATE_FORMAT_YMD_.get().format(voucher.voucherDate);
		voucherNo = voucher.voucherNo;
		summary = voucherRecord.summary;
		exchangeRate = voucherRecord.exchangeRate;
		voucherId = voucher.id;
		voucherRecordId = voucherRecord.id;
		jfbalance = voucherRecord.jfmoney;
		jfbalanceOrg = voucherRecord.jfmoneyOrg;
		jfnumber = voucherRecord.jfnumber;
		dfbalance = voucherRecord.dfmoney;
		dfbalanceOrg = voucherRecord.dfmoneyOrg;
		dfnumber = voucherRecord.dfnumber;
	}

	public void addFs(BaseBalance balance) {
		jfbalance = jfbalance.add(balance.jfbalance);
		jfbalanceOrg = jfbalanceOrg.add(balance.jfbalanceOrg);
		jfnumber = jfnumber.add(balance.jfnumber);
		dfbalance = dfbalance.add(balance.dfbalance);
		dfbalanceOrg = dfbalanceOrg.add(balance.dfbalanceOrg);
		dfnumber = dfnumber.add(balance.dfnumber);
	}

	public void setCurrentByInit(BaseBalance balance) {
		currentbalance = balance.initbalance;
		currentbalanceOrg = balance.initbalanceOrg;
		currentnumber = balance.initnumber;
	}

	public void setCurrentByEnd(BaseBalance balance) {
		currentbalance = balance.endbalance;
		currentbalanceOrg = balance.endbalanceOrg;
		currentnumber = balance.endnumber;
	}

	/**
	 * 是否有值
	 */
	public boolean isNotZero() {
		return jfbalance.compareTo(BigDecimal.ZERO) != 0 ||
		jfbalanceOrg.compareTo(BigDecimal.ZERO) != 0 ||
		jfnumber.compareTo(BigDecimal.ZERO) != 0 ||
		jfprice.compareTo(BigDecimal.ZERO) != 0 ||
		dfbalance.compareTo(BigDecimal.ZERO) != 0 ||
		dfbalanceOrg.compareTo(BigDecimal.ZERO) != 0 ||
		dfnumber.compareTo(BigDecimal.ZERO) != 0 ||
		dfprice.compareTo(BigDecimal.ZERO) != 0 ||
		currentbalance.compareTo(BigDecimal.ZERO) != 0 ||
		currentbalanceOrg.compareTo(BigDecimal.ZERO) != 0 ||
		currentnumber.compareTo(BigDecimal.ZERO) != 0 ||
		currentprice.compareTo(BigDecimal.ZERO) != 0;
	}
}
