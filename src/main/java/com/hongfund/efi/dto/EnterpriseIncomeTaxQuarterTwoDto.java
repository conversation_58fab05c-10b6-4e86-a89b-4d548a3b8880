package com.hongfund.efi.dto;

import org.apache.commons.lang3.StringUtils;

public class EnterpriseIncomeTaxQuarterTwoDto {
    public Long id;
    public Long accountBookId;                                          //帐套
    public String startPeriod;                                          /*起始期间*/
    public String endPeriod;                                            /*结束期间*/
    public Integer row;                                                 /*行次*/
    public String originalValue;                                        /*资产原值*/
    public String accountDepreciationAmount;                            /*账载折旧金额(会计做账的折旧金额)*/
    public String taxDepreciationAmount;                                /*按照税收一般规定计算的折旧金额（国家税收规定的折旧金额）*/
    public String quickenDepreciationAmount;                            /*享受加速折旧优惠计算的折旧金额(折旧额)*/
    public String taxReductionAmount;                                   /*纳税调减金额*/
    public String discountAmount;                                       /*享受加速折旧优惠金额(可抵减利润的金额，满足一定条件才有)*/

    public void setNull() {
        if (StringUtils.isBlank(originalValue)) {
            originalValue = "0.00";
        }
        if (StringUtils.isBlank(accountDepreciationAmount)) {
            accountDepreciationAmount = "0.00";
        }
        if (StringUtils.isBlank(taxDepreciationAmount)) {
            taxDepreciationAmount = "0.00";
        }
        if (StringUtils.isBlank(quickenDepreciationAmount)) {
            quickenDepreciationAmount = "0.00";
        }
        if (StringUtils.isBlank(taxReductionAmount)) {
            taxReductionAmount = "0.00";
        }
        if (StringUtils.isBlank(discountAmount)) {
            discountAmount = "0.00";
        }
    }
}
