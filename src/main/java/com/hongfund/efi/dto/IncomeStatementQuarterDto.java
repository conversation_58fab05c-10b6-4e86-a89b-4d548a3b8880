package com.hongfund.efi.dto;

import com.hongfund.efi.utils.PeriodUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;

public class IncomeStatementQuarterDto {

    public IncomeStatementDto yearCash = new IncomeStatementDto();                //本年累计
    public IncomeStatementDto oneQuarterCash = new IncomeStatementDto();          //第一季度
    public IncomeStatementDto twoQuarterCash = new IncomeStatementDto();           //第二季度
    public IncomeStatementDto threeQuarterCash = new IncomeStatementDto();         //第三季度
    public IncomeStatementDto fourQuarterCash = new IncomeStatementDto();          //第四季度

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

	public IncomeStatementDto getMonthCash(String period) {
	    switch (PeriodUtils.getQuarterIntValue(period)) {
            case 1 : return oneQuarterCash;
            case 2 : return twoQuarterCash;
            case 3 : return threeQuarterCash;
            default : return fourQuarterCash;
        }
    }
}
