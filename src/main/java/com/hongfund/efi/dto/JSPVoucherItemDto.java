package com.hongfund.efi.dto;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class JSPVoucherItemDto {
    public Long id;
    public String createdAt;
    public String updatedAt;
    public boolean addFlag;
    public Long voucherId;
    public Integer type;
    public String summary;
    public Long subjectId;
    public BigDecimal inAmount;
    public BigDecimal outAmount;
    public String affectInventory;
    public String price;
    public String count;
    public String remark;
    public Integer seq;
    public Integer status;
    public String wb;
    public String rate;
    public String inAmountWb;
    public String outAmountWb;
    public String inventoryStrIds;
    public String fzhsId;
    public List<JSPInventoryIdsDto> inventoryIds = new ArrayList<>();
    public String fzhsCode;
    public String fzhsName;
    public String subjectCode;
    public String subjectName;
    public String unit;
}
