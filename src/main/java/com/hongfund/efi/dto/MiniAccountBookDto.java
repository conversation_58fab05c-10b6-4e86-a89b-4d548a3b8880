package com.hongfund.efi.dto;

/**
 * 报税用的账套信息
 * @author: chenbin
 */
public class MiniAccountBookDto {
    /**
     * 客户名称
     */
    public String bookName;
    /**
     * 会计制度
     */
    public String accountingSystem;
    /**
     * 纳税人识别号
     */
    public String socialCreditCode;
    /**
     * 国税登录名
     */
    public String nationalName;
    /**
     * 国税密码
     */
    public String nationalPwd;
    /**
     * 办税人登录名
     */
    public String taxPayerName;
    /**
     * 办税人密码
     */
    public String taxPayerPwd;
    /**
     * 是否为月度增值税申报
     */
    public Integer isMonthTax;
    /**
     * 是否为月度财务报表申报
     */
    public Integer isMonthFinance;
    /**
     * 纳税人资格
     */
    public String addtax;

    public MiniAccountBookDto() {
    }

    public MiniAccountBookDto(String bookName, String accountingSystem, String socialCreditCode, String nationalName, String nationalPwd, String taxPayerName, String taxPayerPwd, Integer isMonthTax, Integer isMonthFinance, String addtax) {
        this.bookName = bookName;
        this.accountingSystem = accountingSystem;
        this.socialCreditCode = socialCreditCode;
        this.nationalName = nationalName;
        this.nationalPwd = nationalPwd;
        this.taxPayerName = taxPayerName;
        this.taxPayerPwd = taxPayerPwd;
        this.isMonthTax = isMonthTax;
        this.isMonthFinance = isMonthFinance;
        this.addtax = addtax;
    }
}
