package com.hongfund.efi.dto;

import com.hongfund.efi.domain.OldImportVoucher;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class OldImportVoucherDto {
    /**
     * id
     */
    public Long id;
    /**
     * oldImportId
     */
    public Long oldImportId;
    /**
     * 帐套id
     */
    public Long accountBookId;
    /**
     * 凭证日期字符串
     */
    public String voucherDateStr;
    /**
     * 凭证编号
     */
    public String voucherNo;
    /**
     * 摘要
     */
    public String summary;
    /**
     * 科目编码
     */
    public String subjectNo;
    /**
     * 科目长名称
     */
    public String longText;
    /**
     * 辅助核算名称
     */
    public String assName;
    public Date gmtCreate;//创建
    public Date gmtModified; //修改时间
    /**
     * 项目
     */
    public String item;
    /**
     * 客户
     */
    public String customer;
    /**
     * 供应商
     */
    public String provider;
    /**
     * 部门
     */
    public String department;
    /**
     * 员工
     */
    public String employee;
    /**
     * 存货
     */
    public String inventory;
    /**
     * 借方金额
     */
    public BigDecimal jfBalance = BigDecimal.ZERO;
    /**
     * 贷方金额
     */
    public BigDecimal dfBalance = BigDecimal.ZERO;
    /**
     * 数量
     */
    public BigDecimal number = BigDecimal.ZERO;
    /**
     * 计量单位（可空）
     */
    public String measuringUnit;
    /**
     * 单价
     */
    public BigDecimal unitPrice = BigDecimal.ZERO;
    /**
     * 外币金额
     */
    public BigDecimal moneyOrg = BigDecimal.ZERO;
    /**
     * 外币符号（可空）
     */
    public String currSymbol;
    /**
     * 汇率
     */
    public BigDecimal exchangeRate = BigDecimal.ZERO;
    /**
     * 制单人
     */
    public String writtenPerson;
    /**
     * 审核人
     */
    public String auditPerson;
    /**
     * 附件数
     */
    public String accessoryNo;
    /**
     * 规格
     */
    public String model;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public OldImportVoucherDto() {
    }

    /**
     * 是否有辅助
     */
    public boolean countDimAccountTypeModified() {
        return StringUtils.isNotBlank(assName) || StringUtils.isNotBlank(item) || StringUtils.isNotBlank(customer) || StringUtils.isNotBlank(provider) || StringUtils.isNotBlank(department) || StringUtils.isNotBlank(employee) || StringUtils.isNotBlank(inventory);
    }

    /**
     * 是否有数量
     */
    public boolean countMeasuringUnit() {
        return number.compareTo(BigDecimal.ZERO) != 0;
    }

    /**
     * 是否有外币
     */
    public boolean countCurrSymbol() {
        return moneyOrg.compareTo(BigDecimal.ZERO) != 0;
    }

    /**
     * 根据科目辅助核算类型名称
     */
    public String countDimAccountType() {
        String result = "";
        if (StringUtils.isNotBlank(item)) {
            result += ",项目";
        }
        if (StringUtils.isNotBlank(customer)) {
            result += ",客户";
        }
        if (StringUtils.isNotBlank(provider)) {
            result += ",供应商";
        }
        if (StringUtils.isNotBlank(department)) {
            result += ",部门";
        }
        if (StringUtils.isNotBlank(employee)) {
            result += ",员工";
        }
        if (StringUtils.isNotBlank(inventory)) {
            result += ",存货";
        }
        return StringUtils.removeStart(result, ",");
    }

    public void modify() {
        if (jfBalance == null) {
            jfBalance = BigDecimal.ZERO;
        }
        if (dfBalance == null) {
            dfBalance = BigDecimal.ZERO;
        }
        if (number == null) {
            number = BigDecimal.ZERO;
        }
        if (unitPrice == null) {
            unitPrice = BigDecimal.ZERO;
        }
        if (moneyOrg == null) {
            moneyOrg = BigDecimal.ZERO;
        }
        if (exchangeRate == null) {
            exchangeRate = BigDecimal.ZERO;
        }
    }

    public void map(OldImportVoucher voucher) {
        id = voucher.id;
        oldImportId = voucher.oldImportId;
        accountBookId = voucher.accountBookId;
        voucherDateStr = voucher.voucherDateStr;
        voucherNo = voucher.voucherNo;
        summary = voucher.summary;
        subjectNo = voucher.subjectNo;
        longText = voucher.longText;
        assName = voucher.assName;
        item = voucher.item;
        customer = voucher.customer;
        provider = voucher.provider;
        department = voucher.department;
        employee = voucher.employee;
        inventory = voucher.inventory;
        jfBalance = voucher.jfBalance;
        dfBalance = voucher.dfBalance;
        number = voucher.number;
        measuringUnit = voucher.measuringUnit;
        model = voucher.model;
        unitPrice = voucher.unitPrice;
        moneyOrg = voucher.moneyOrg;
        currSymbol = voucher.currSymbol;
        exchangeRate = voucher.exchangeRate;
        writtenPerson = voucher.writtenPerson;
        auditPerson = voucher.auditPerson;
        accessoryNo = voucher.accessoryNo;
    }

    public static List<OldImportVoucherDto> mapList(List<OldImportVoucher> vouchers) {
        List<OldImportVoucherDto> result = new ArrayList<>();
        for (OldImportVoucher voucher : vouchers) {
            OldImportVoucherDto voucherDto = new OldImportVoucherDto();
            voucherDto.map(voucher);
            result.add(voucherDto);
        }
        return result;
    }
}
