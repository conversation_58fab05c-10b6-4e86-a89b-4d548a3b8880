package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;
import java.util.Date;

public class InventoryOrderDetailDto {

    public Long id;
    public Date gmtCreate;
    public Date gmtModified;
    /**
     * 帐套id
     */
    public Long accountBookId;
    /**
     * 单据id
     */
    public Long inventoryOrderId;
    /**
     * 期间
     */
    public String period;
    /**
     * 存货辅助核算id
     */
    public Long dimInventoryId;
    /**
     * 存货名称
     */
    public String dimInventoryName;
    /**
     * 存货编码
     */
    public String dimInventoryNo;
    /**
     * 规格
     */
    public String specification;
    /**
     * 计量单位
     */
    public String measuringUnit;
    /**
     * 数量
     */
    public BigDecimal number;
    /**
     * 单价
     */
    public BigDecimal price;
    /**
     * 金额
     */
    public BigDecimal money;
    /**
     * 税率
     */
    public BigDecimal taxRate;
    /**
     * 税额
     */
    public BigDecimal taxMoney;
    /**
     * 对应产品
     */
    public Long oppositeInventory;
    /**
     * 对应产品名称
     */
    public String oppositeInventoryName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
