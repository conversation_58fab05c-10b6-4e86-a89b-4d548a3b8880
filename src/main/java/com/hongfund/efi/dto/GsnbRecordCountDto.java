package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.Date;

/**
 * 工商年报统计表
 */
public class GsnbRecordCountDto {
	public Long id;
	public Date gmtCreate;
	public Date gmtModified;
	public Long accountBookId;
	public String year;
	/**
	 * 采集状态
	 */
	public Integer infoState;
	/**
	 * 取数状态
	 */
	public Integer countState;
	/**
	 * 申报状态
	 */
	public Integer reportState;

	public String bookName;

	public GsnbRecordCountDto() {
	}

	public GsnbRecordCountDto(Long accountBookId, Integer infoState, Integer countState, Integer reportState, String bookName) {
		this.accountBookId = accountBookId;
		this.infoState = infoState;
		this.countState = countState;
		this.reportState = reportState;
		this.bookName = bookName;
	}

	public void setDefault() {
		if (infoState == null) {
			infoState = 1;
		}
		if (countState == null) {
			countState = 1;
		}
		if (reportState == null) {
			reportState = 1;
		}
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
