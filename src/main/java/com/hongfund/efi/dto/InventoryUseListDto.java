package com.hongfund.efi.dto;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 生产领料列表
 */
public class InventoryUseListDto {
	public Long accountBookId;
	public String period;
	/**
	 * 数据行
	 */
	public List<InventoryUseRowDto> list = new ArrayList<>();
	/**
	 * 合计
	 */
	public InventoryUseRowDto total = new InventoryUseRowDto();
	/**
	 * 领料方式（0：主营业务收入百分比领用 1：材料库存百分比领用 2：全额领用）
	 */
	public Integer useType;
	/**
	 * 领用百分比（主营业务收入）
	 */
	public Integer per1;
	/**
	 * 领用百分比（材料库存）
	 */
	public Integer per2;
	/**
	 * 原材料余额
	 */
	public BigDecimal balanceInventory;
	/**
	 * 预计领用
	 */
	public BigDecimal balanceExpect;
	/**
	 * 主营业务收入
	 */
	public BigDecimal balanceMain;
	/**
	 * 单品领料百分比
	 */
	public BigDecimal usePer = BigDecimal.ZERO;
	/**
	 * 按钮：估价
	 */
	public boolean isGj;
	/**
	 * 按钮：重新估价
	 */
	public boolean isCxgj;
	/**
	 * 按钮：暂估入库
	 */
	public boolean isZgrk;

	public BigDecimal getFinalPer() {
		if (useType == 0) {
			return usePer;
		}
		else if (useType == 1) {
			return new BigDecimal(per2);
		}
		else {
			return new BigDecimal(100);
		}
	}

	public void countButtons() {
		isGj = true;
		isCxgj = false;
		isZgrk = true;
		for (InventoryUseRowDto row : list) {
			if (row.numberEstimate.compareTo(BigDecimal.ZERO) != 0 || row.balanceEstimate.compareTo(BigDecimal.ZERO) != 0) {
				isGj = false;
				isCxgj = true;
				if (row.voucherIdEstimate != null) {
					isZgrk = false;
				}
			}
		}
	}
}
