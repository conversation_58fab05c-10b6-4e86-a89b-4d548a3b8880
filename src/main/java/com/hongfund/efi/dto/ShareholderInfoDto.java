package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;

/**
 * 股东分红信息
 */
public class ShareholderInfoDto {
    /**
     * 股东名称
     */
    public String shareholderName;

    /**
     * 证件类型
     */
    public String idType;

    /**
     * 证件号码
     */
    public String idNumber;

    /**
     * 投资比例（百分比）
     */
    public BigDecimal investmentRatio = BigDecimal.ZERO;

    /**
     * 分红金额
     */
    public BigDecimal dividendAmount = BigDecimal.ZERO;

    /**
     * 国籍（注册地址）
     */
    public String nationality;

    public ShareholderInfoDto() {
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
} 