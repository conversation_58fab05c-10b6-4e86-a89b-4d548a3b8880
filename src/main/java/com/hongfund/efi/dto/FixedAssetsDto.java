package com.hongfund.efi.dto;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;

import java.util.ArrayList;
import java.util.List;

public class FixedAssetsDto {
   public List<FixedAssetsRowDto> fads = new ArrayList<>();
   public Page<FixedAssetsRowDto> pagFads;
   public FixedAssetsRowDto total ;				//合计
   public int canAdd; 							//是否可以添加：0：可以，1：不可以
   public String text; 							//提示信息
   public VoucherDto voucher;					//生成的凭证


   public FixedAssetsRowDto getFixedAssetsRowDto(List<FixedAssetsRowDto> fixedAssetsRowDtos, String fixedAssetsType) {
      FixedAssetsRowDto total = new FixedAssetsRowDto();
      for (FixedAssetsRowDto fixedAssetsRowDto : fixedAssetsRowDtos) {
          if (StringUtils.equals(fixedAssetsRowDto.fixedAssetsType, fixedAssetsType)) {
              total.count(fixedAssetsRowDto);
          }
      }
      return total;
  }
}
