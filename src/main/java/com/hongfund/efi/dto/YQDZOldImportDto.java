package com.hongfund.efi.dto;

import java.util.ArrayList;
import java.util.List;

/**
 * 导账数据类
 */
public class YQDZOldImportDto {

	/**
	 * 期初余额数据
	 */
	public List<YQDZImportInitBalanceDto> initBalance = new ArrayList<>();
	/**
	 * 凭证数据
	 */
	public List<YQDZImportVoucherDto> voucher = new ArrayList<>();
	/**
	 * 账套信息数据
	 */
	public List<YQDZImportBookInfoDto> info = new ArrayList<>();
	/**
	 * 辅助核算数据
	 */
	public List<YQDZImportDimDto> dim = new ArrayList<>();
	/**
	 * 科目数据
	 */
	public List<YQDZImportSubjectDto> subject = new ArrayList<>();

}
