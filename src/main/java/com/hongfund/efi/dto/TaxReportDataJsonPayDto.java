package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;

/**
 * 缴款信息
 */
public class TaxReportDataJsonPayDto {
	/**
	 * 征收项目名称
	 */
	public String zsxmMc;
	/**
	 * 征收品目名称
	 */
	public String zspmMc;
	/**
	 * 税款所属期起
	 */
	public String skssqq;
	/**
	 * 税款所属期止
	 */
	public String skssqz;
	/**
	 * 实缴金额
	 */
	public BigDecimal sjje;
	/**
	 * 税款种类
	 */
	public String skzl;
	/**
	 * 缴款日期
	 */
	public String jkrq;

	public TaxReportDataJsonPayDto() {
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
