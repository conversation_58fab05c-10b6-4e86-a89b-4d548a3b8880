package com.hongfund.efi.dto;

import java.util.ArrayList;
import java.util.List;

public class SubsidiaryLedgerDto {

	public String title;   //批量下载的明细账标题
	public String longText;//科目编码加名称
	public String time; //起止期间
	public String mesuringment; //计量单位
	public String currSymbol; //外币符号
	public String bookName; //编制单位
	public List<SubsidiaryLedgerRowDto> subsidiarys = new ArrayList<>();//明细账数据列表
	public List<List<String>> multiDatas = new ArrayList<>();
}
