package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

public class IncomeStatementRowDto {
	
	public String name;					 //项目名
	public String row;					 //行次
	public String yearCashs;             //本年累计金额
	public String monthCashs;            //本月金额
	public String lastYearMonthCashs;    //上年同期金额
	public String monthUnLimit;		   	 //本月非限定性
	public String monthLimit;			 //本月限定性
	public String monthSum;			   	 //本月合计
	public String yearUnLimit;		   	 //本年非限定性
	public String yearLimit;			 //本年限定性
	public String yearSum;			   	 //本年合计
	
	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

}
