package com.hongfund.efi.dto;

import com.hongfund.efi.domain.OldImportEndBalance;
import com.hongfund.efi.domain.OldImportInitBalance;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 期初余额数据
 */
public class OldImportInitBalanceDto {
    /**
     * id
     */
    public Long id;
    /**
     * oldImportId
     */
    public Long oldImportId;
    /**
     * 科目编码
     */
    public String no;
    /**
     * 科目名称
     */
    public String name;
    /**
     * 计量单位
     */
    public String measuringUnit;
    /**
     * 规格型号
     */
    public String model;
    /**
     * 外币符号
     */
    public String currSymbol;

    /**
     * 辅助核算名称
     */
    public String assName;
    /**
     * 项目
     */
    public String itemName;
    /**
     * 客户
     */
    public String customerName;
    /**
     * 供应商
     */
    public String providerName;
    /**
     * 部门
     */
    public String departmentName;
    /**
     * 员工
     */
    public String employeeName;
    /**
     * 存货
     */
    public String inventoryName;
    public Date gmtCreate;//创建
    public Date gmtModified; //修改时间

    /**
     * 本年累计借方发生额
     */
    public BigDecimal jfljbalance = BigDecimal.ZERO;
    /**
     * 本年累计借方发生额原币
     */
    public BigDecimal jfljbalanceOrg = BigDecimal.ZERO;
    /**
     * 本年累计借方数量
     */
    public BigDecimal jfljnumber = BigDecimal.ZERO;
    /**
     * 本年累计贷方发生额
     */
    public BigDecimal dfljbalance = BigDecimal.ZERO;
    /**
     * 本年累计贷方发生额原币
     */
    public BigDecimal dfljbalanceOrg = BigDecimal.ZERO;
    /**
     * 本年累计贷方数量
     */
    public BigDecimal dfljnumber = BigDecimal.ZERO;
    /**
     * 期末借方余额
     */
    public BigDecimal endjfbalance = BigDecimal.ZERO;
    /**
     * 期末借方余额原币
     */
    public BigDecimal endjfbalanceOrg = BigDecimal.ZERO;
    /**
     * 期末借方数量
     */
    public BigDecimal endjfnumber = BigDecimal.ZERO;
    /**
     * 期末贷方余额
     */
    public BigDecimal enddfbalance = BigDecimal.ZERO;
    /**
     * 期末贷方余额原币
     */
    public BigDecimal enddfbalanceOrg = BigDecimal.ZERO;
    /**
     * 期末贷方数量
     */
    public BigDecimal enddfnumber = BigDecimal.ZERO;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public boolean hasBalance() {
        return jfljbalance.compareTo(BigDecimal.ZERO) != 0 ||
                jfljbalanceOrg.compareTo(BigDecimal.ZERO) != 0 ||
                jfljnumber.compareTo(BigDecimal.ZERO) != 0 ||
                dfljbalance.compareTo(BigDecimal.ZERO) != 0 ||
                dfljbalanceOrg.compareTo(BigDecimal.ZERO) != 0 ||
                dfljnumber.compareTo(BigDecimal.ZERO) != 0 ||
                endjfbalance.compareTo(BigDecimal.ZERO) != 0 ||
                endjfbalanceOrg.compareTo(BigDecimal.ZERO) != 0 ||
                endjfnumber.compareTo(BigDecimal.ZERO) != 0 ||
                enddfbalance.compareTo(BigDecimal.ZERO) != 0 ||
                enddfbalanceOrg.compareTo(BigDecimal.ZERO) != 0 ||
                enddfnumber.compareTo(BigDecimal.ZERO) != 0;
    }

    /**
     * 是否有辅助
     */
    public boolean countDimAccountType() {
        return assName != null || itemName != null || customerName != null || providerName != null || departmentName != null || employeeName != null || inventoryName != null;
    }

    /**
     * 是否有数量
     */
    public boolean countMeasuringUnit() {
        return jfljnumber.compareTo(BigDecimal.ZERO) != 0 ||
                dfljnumber.compareTo(BigDecimal.ZERO) != 0 ||
                endjfnumber.compareTo(BigDecimal.ZERO) != 0 ||
                enddfnumber.compareTo(BigDecimal.ZERO) != 0;
    }

    /**
     * 是否有外币
     */
    public boolean countCurrSymbol() {
        return jfljbalanceOrg.compareTo(BigDecimal.ZERO) != 0 ||
                dfljbalanceOrg.compareTo(BigDecimal.ZERO) != 0 ||
                endjfbalanceOrg.compareTo(BigDecimal.ZERO) != 0 ||
                enddfbalanceOrg.compareTo(BigDecimal.ZERO) != 0;
    }

    /**
     * 根据科目辅助核算类型名称
     */
    public String getDimAccountType() {
        String result = "";
        if (StringUtils.isNotBlank(itemName)) {
            result += ",项目";
        }
        if (StringUtils.isNotBlank(customerName)) {
            result += ",客户";
        }
        if (StringUtils.isNotBlank(providerName)) {
            result += ",供应商";
        }
        if (StringUtils.isNotBlank(departmentName)) {
            result += ",部门";
        }
        if (StringUtils.isNotBlank(employeeName)) {
            result += ",员工";
        }
        if (StringUtils.isNotBlank(inventoryName)) {
            result += ",存货";
        }
        return StringUtils.removeStart(result, ",");
    }

    public void map(OldImportInitBalance balance) {
        id = balance.id;
        oldImportId = balance.oldImportId;
        no = balance.no;
        name = balance.name;
        measuringUnit = balance.measuringUnit;
        currSymbol = balance.currSymbol;
        model = balance.model;
        assName = balance.assName;
        itemName = balance.itemName;
        customerName = balance.customerName;
        providerName = balance.providerName;
        departmentName = balance.departmentName;
        employeeName = balance.employeeName;
        inventoryName = balance.inventoryName;
        jfljbalance = balance.jfljbalance;
        jfljbalanceOrg = balance.jfljbalanceOrg;
        jfljnumber = balance.jfljnumber;
        dfljbalance = balance.dfljbalance;
        dfljbalanceOrg = balance.dfljbalanceOrg;
        dfljnumber = balance.dfljnumber;
        endjfbalance = balance.endjfbalance;
        endjfbalanceOrg = balance.endjfbalanceOrg;
        endjfnumber = balance.endjfnumber;
        enddfbalance = balance.enddfbalance;
        enddfbalanceOrg = balance.enddfbalanceOrg;
        enddfnumber = balance.enddfnumber;
    }

    public static List<OldImportInitBalanceDto> mapList(List<OldImportInitBalance> balances) {
        List<OldImportInitBalanceDto> result = new ArrayList<>();
        for (OldImportInitBalance balance : balances) {
            OldImportInitBalanceDto balanceDto = new OldImportInitBalanceDto();
            balanceDto.map(balance);
            result.add(balanceDto);
        }
        return result;
    }

    public void map(OldImportEndBalance balance) {
        id = balance.id;
        oldImportId = balance.oldImportId;
        no = balance.no;
        name = balance.name;
        measuringUnit = balance.measuringUnit;
        currSymbol = balance.currSymbol;
        model = balance.model;
        assName = balance.assName;
        itemName = balance.itemName;
        customerName = balance.customerName;
        providerName = balance.providerName;
        departmentName = balance.departmentName;
        employeeName = balance.employeeName;
        inventoryName = balance.inventoryName;
        jfljbalance = balance.jfljbalance;
        jfljbalanceOrg = balance.jfljbalanceOrg;
        jfljnumber = balance.jfljnumber;
        dfljbalance = balance.dfljbalance;
        dfljbalanceOrg = balance.dfljbalanceOrg;
        dfljnumber = balance.dfljnumber;
        endjfbalance = balance.endjfbalance;
        endjfbalanceOrg = balance.endjfbalanceOrg;
        endjfnumber = balance.endjfnumber;
        enddfbalance = balance.enddfbalance;
        enddfbalanceOrg = balance.enddfbalanceOrg;
        enddfnumber = balance.enddfnumber;
    }

    public static List<OldImportInitBalanceDto> mapList2(List<OldImportEndBalance> balances) {
        List<OldImportInitBalanceDto> result = new ArrayList<>();
        for (OldImportEndBalance balance : balances) {
            OldImportInitBalanceDto balanceDto = new OldImportInitBalanceDto();
            balanceDto.map(balance);
            result.add(balanceDto);
        }
        return result;
    }
}
