package com.hongfund.efi.dto;

import java.math.BigDecimal;

import org.apache.commons.lang3.builder.ToStringBuilder;

public class AccountBookStatementRowDto {
	public Long id;
	public String bookName;					/*客户名称*/
	public String chargeType;				/*项目：记账费、账本费*/
	public String paymentMethod;			/*付款方式*/
	public BigDecimal actualCharge;			/*实收金额*/			
	public BigDecimal totalCharge;			/*服务费即合计*/
	public String actualChargeTemp;			/*实收金额*/			
	public String totalChargeTemp;			/*服务费即合计*/
	public String chargeDate;				/*收费日期*/
	public String chargeNo;					/*流水号*/
	public String writtenDate;				/*录入日期*/

	public AccountBookStatementRowDto(Long id, String bookName, String chargeType, String paymentMethod,
			BigDecimal actualCharge, BigDecimal totalCharge, String chargeDate, String chargeNo, String writtenDate) {
		super();
		this.id = id;
		this.bookName = bookName;
		this.chargeType = chargeType;
		this.paymentMethod = paymentMethod;
		this.actualCharge = actualCharge;
		this.totalCharge = totalCharge;
		this.chargeDate = chargeDate;
		this.chargeNo = chargeNo;
		this.writtenDate = writtenDate;
	}
	
	public AccountBookStatementRowDto(){
		
	}


	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
