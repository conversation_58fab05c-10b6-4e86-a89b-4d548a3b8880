package com.hongfund.efi.dto;

public class EnterpriseIncomeTaxQuarterFourDto {

    public Long id;
    public Long accountBookId;                      //帐套
    public String startPeriod;                      /*起始期间*/
    public String endPeriod;                        /*结束期间*/
    public String companyName;                      /*企业名称*/
    public String taxPayerCode;                     /*纳税人识别号/社会信用代码*/
    public String foreignCompanyNameChinese;        /*外国企业名称（中文）*/
    public String foreignCompanyNameEnglish;        /*外国企业名称（外文）*/
    public String foreignTaxPayerCode;              /*所在国纳税识别号*/
    public String establishPlaceChinese;            /*成立地（中文）*/
    public String establishPlaceEnglish;            /*成立地（外文）*/
    public String mainBusinessType;                 /*主营业务类型*/
    public String reporterShareholdingRatio;        /*报告人持股比例*/
    public String shareholderNameChinese;           /*持股股东名称（中文）*/
    public String shareholderNameEnglish;           /*持股股东名称（外文）*/
    public String residencePlaceChinese;            /*居住地或成立地（中文）*/
    public String residencePlaceEnglish;            /*居住地或成立地（外文）*/
    public String shareholdingType;                 /*持股类型*/
    public String shareholdingRatio;                /*持股比例*/
    public String achieveTenPercentDate;            /*达到10%以上权益份额的起始日期*/
    public String chinesePersonalName;              /*中国居民个人姓名*/
    public String usualResidenceInChina;            /*中国境内常住地*/
    public String idType;                           /*身份证件类型*/
    public String idNumber;                         /*身份证件号码*/
    public String position;                         /*职务*/
    public String officeDate;                       /*任职日期起*/
    public String resignationDate;                  /*任职日期止*/
    public String acquiredShareType;                /*被收购股份类型*/
    public String transactionDate;                  /*交易日期*/
    public String acquisitionMethod;                /*收购方式*/
    public String beforeAcquisitionShareholding;    /*收购前报告人在外国企业持股份额*/
    public String afterAcquisitionShareholding;     /*收购后报告人在外国企业持股份额*/
    public String disposedShareType;                /*被处置股份类型*/
    public String disposalDate;                     /*处置日期*/
    public String disposalMethod;                   /*处置方式*/
    public String beforeDisposalShareholding;       /*处置前报告人在外国企业持股份额*/
    public String afterDisposalShareholding;        /*被收购股份类型*/


    public void setNull() {
        if (companyName == null) {
            companyName = "";
        }
        if (taxPayerCode == null) {
            taxPayerCode = "";
        }
        if (foreignCompanyNameChinese == null) {
            foreignCompanyNameChinese = "";
        }
        if (foreignCompanyNameEnglish == null) {
            foreignCompanyNameEnglish = "";
        }
        if (foreignTaxPayerCode == null) {
            foreignTaxPayerCode = "";
        }
        if (establishPlaceChinese == null) {
            establishPlaceChinese = "";
        }
        if (establishPlaceEnglish == null) {
            establishPlaceEnglish = "";
        }
        if (mainBusinessType == null) {
            mainBusinessType = "";
        }
        if (reporterShareholdingRatio == null) {
            reporterShareholdingRatio = "";
        }
        if (shareholderNameChinese == null) {
            shareholderNameChinese = "";
        }
        if (shareholderNameEnglish == null) {
            shareholderNameEnglish = "";
        }
        if (residencePlaceChinese == null) {
            residencePlaceChinese = "";
        }
        if (residencePlaceEnglish == null) {
            residencePlaceEnglish = "";
        }
        if (shareholdingType == null) {
            shareholdingType = "";
        }
        if (shareholdingRatio == null) {
            shareholdingRatio = "";
        }
        if (achieveTenPercentDate == null) {
            achieveTenPercentDate = "";
        }
        if (chinesePersonalName == null) {
            chinesePersonalName = "";
        }
        if (usualResidenceInChina == null) {
            usualResidenceInChina = "";
        }
        if (idType == null) {
            idType = "";
        }
        if (idNumber == null) {
            idNumber = "";
        }
        if (position == null) {
            position = "";
        }
        if (officeDate == null) {
            officeDate = "";
        }
        if (resignationDate == null) {
            resignationDate = "";
        }
        if (acquiredShareType == null) {
            acquiredShareType = "";
        }
        if (transactionDate == null) {
            transactionDate = "";
        }
        if (acquisitionMethod == null) {
            acquisitionMethod = "";
        }
        if (beforeAcquisitionShareholding == null) {
            beforeAcquisitionShareholding = "";
        }
        if (afterAcquisitionShareholding == null) {
            afterAcquisitionShareholding = "";
        }
        if (disposedShareType == null) {
            disposedShareType = "";
        }
        if (disposalDate == null) {
            disposalDate = "";
        }
        if (disposalMethod == null) {
            disposalMethod = "";
        }
        if (beforeDisposalShareholding == null) {
            beforeDisposalShareholding = "";
        }
        if (afterDisposalShareholding == null) {
            afterDisposalShareholding = "";
        }
    }

}
