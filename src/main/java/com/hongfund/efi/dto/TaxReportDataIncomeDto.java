package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;

/**
 * 风险报告利润表
 */
public class TaxReportDataIncomeDto {
	/**
	 * 项目名称
	 */
	public String name;
	/**
	 * 本期名称
	 */
	public String monthName;
	/**
	 * 本年名称
	 */
	public String yearName;
	/**
	 * 本期金额
	 */
	public BigDecimal monthBalance;
	/**
	 * 本年金额
	 */
	public BigDecimal yearBalance;


	public TaxReportDataIncomeDto() {
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
