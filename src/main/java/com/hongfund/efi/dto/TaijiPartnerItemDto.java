package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;


public class TaijiPartnerItemDto {
    public Long id;
    /** 统一社会信用代码*/
    public String creditNo;
    /** 股东姓名*/
    public String name;
    /** 总实缴*/
    public String totalRealCapi;
    /** 企业证照号*/
    public String identifyNo;
    /** 总认缴*/
    public String totalShouldCapi;
    /** 股比*/
    public String stockPercent;
    /** 类型*/
    public String identifyType;
    /** 股东类型*/
    public String stockType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public TaijiPartnerItemDto() {
    }
}
