package com.hongfund.efi.dto;

import com.hongfund.efi.domain.AppCompany;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单表DTO类
 */
public class AppOrdersDto {

    
    public Long id;
    public Date gmtCreate; 
    public Date gmtModified;

    /**
     * 订单编号
     */
    public String orderNo;

    /**
     * 用户ID
     */
    public Long userId;
    /**
     * 公司id
     */
    public Long companyId;
    public String companyName;
    /**
     * 服务ID
     */
    public Long serviceId;

    /**
     * 服务名称
     */
    public String serviceName;

    /**
     * 订单金额
     */
    public BigDecimal amount;

    /**
     * 订单状态：1-待支付，2-已支付，3-已取消
     */
    public Integer orderStatus = 1;

    /**
     * 交易流水号
     */
    public String transactionId;
    /**
     * 支付时间
     */
    public Date paymentTime;
    /**
     * 取消时间
     */
    public Date cancelTime;
    /**
     * 购买时间文本格式
     */
    public String paymentTimeStr;
    /**
     * 交易类型
     * JSAPI：公众号支付、小程序支付
     * NATIVE：Native支付
     * APP：APP支付
     * MICROPAY：付款码支付
     * MWEB：H5支付
     * FACEPAY：刷脸支付
     */
    public String tradeType;

    /**
     * 支付者openid（小程序支付和公众号支付时必填）
     */
    public String payerOpenid;

    /**
     * 报告状态
     */
    public Integer reportStatus;
    /**
     * 报告下载地址
     */
    public String reportUrl;
    /**
     * 结果信息
     */
    public String resultMsg;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    /**
     * 获取公司信息
     */
    public AppCompany getAppCompany(List<AppCompany> companyList, Long companyId) {
        for (AppCompany company : companyList) {
            if (companyId.equals(company.id)) {
                return company;
            }
        }
        return null;
    }
}
