package com.hongfund.efi.dto;

import java.math.BigDecimal;

import org.apache.commons.lang3.builder.ToStringBuilder;

public class CashFlowStatementRowDto {
	public Long id;
    public String code;                    //现金流量项目编码                                   
    public String item;                    //现金流量项目名称            
    public Integer row;                    //页面显示行次           
    public Integer fold;                   //折叠到第n行里  n：从上往下数的行编号，不是显示的行次           
    public Integer foldFlag;               //0为不显示“>”（是末级），1为显示“>”（非末级）              
    public Integer rowIndex;               //位于第几行                
    public BigDecimal yearCashBD;          //本年累计金额                  
    public BigDecimal monthCashBD;         //本月金额                 
    public BigDecimal lyearCashBD;         //上年同期累计                  
    public String formula;                 //计算公式 
    public BigDecimal initbalance;		   //期初金额
    public Integer isInit;				   //是否可编辑的期初流量项目
    


	public CashFlowStatementRowDto(Long id, String code, String item, Integer row, Integer fold, Integer foldFlag,
			Integer rowIndex, BigDecimal yearCashBD, BigDecimal monthCashBD, BigDecimal lyearCashBD, String formula,
			BigDecimal initbalance, Integer isInit) {
		super();
		this.id = id;
		this.code = code;
		this.item = item;
		this.row = row;
		this.fold = fold;
		this.foldFlag = foldFlag;
		this.rowIndex = rowIndex;
		this.yearCashBD = yearCashBD;
		this.monthCashBD = monthCashBD;
		this.lyearCashBD = lyearCashBD;
		this.formula = formula;
		this.initbalance = initbalance;
		this.isInit = isInit;
	}

	public CashFlowStatementRowDto() {
	}
    
	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
