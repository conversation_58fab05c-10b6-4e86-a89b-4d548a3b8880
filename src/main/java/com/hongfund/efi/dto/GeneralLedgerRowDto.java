package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;

public class GeneralLedgerRowDto {

    public String summary;
    public Long subjectId; /* 科目id */
    public BigDecimal initbalance = BigDecimal.ZERO; /* 期初余额 */
    public BigDecimal jfbalance = BigDecimal.ZERO; /* 借方余额 */
    public BigDecimal dfbalance = BigDecimal.ZERO; /* 贷方余额 */
    public String year; /* 年份 */
    public String  direction; /* 方向 */

    public GeneralLedgerRowDto(String summary, Long subjectId, BigDecimal initbalance, BigDecimal jfbalance, BigDecimal dfbalance, String year, String direction) {
        this.summary = summary;
        this.subjectId = subjectId;
        this.initbalance = initbalance;
        this.jfbalance = jfbalance;
        this.dfbalance = dfbalance;
        this.year = year;
        this.direction = direction;
    }

    public GeneralLedgerRowDto() {
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
