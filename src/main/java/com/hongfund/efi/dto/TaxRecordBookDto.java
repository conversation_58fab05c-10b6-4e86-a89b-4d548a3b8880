package com.hongfund.efi.dto;

import com.hongfund.efi.domain.TaxInfo;
import com.hongfund.efi.domain.TaxRecord;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 报税记录表
 */
public class TaxRecordBookDto {
	public Long id;
	public Date gmtCreate;
	public Date gmtModified;
	/**
	 * 开始运行时间
	 */
	public Date startTime;
	public Long accountBookId;
	public String accountBookName;
	public Long accountId;
	/**
	 * 税种 1：增值税
	 */
	public Integer taxType;
	/**
	 * 区域编号
	 */
	public Integer areaId;
	/**
	 * 操作编号
	 */
	public Integer actionId;
	/**
	 * 申报源（1：电子税务局 2：自然人系统）
	 */
	public Integer sourceId;
	public Integer workerIndex = 0;
	/**
	 * 优先级
	 */
	public Integer orderLevel = 0;
	/**
	 * 税种描述
	 */
	public String taxTypeStr;
	public String period;
	/**
	 * 申报状态
	 */
	public Integer taxState;
	/**
	 * 申报税额
	 */
	public BigDecimal payBalance;
	/**
	 * 结果信息
	 */
	public String resultMessage;
	/**
	 * 结果下载地址
	 */
	public String resultUrl;
	/**
	 * 结果图片地址
	 */
	public String resultImageUrl;
	/**
	 * 个税是否按上月申报
	 */
	public Integer isLastMonth;
	/**
	 * 个税是否首次申报
	 */
	public Integer isFirstTime;
	/**
	 * 账套信息
	 */
	public AccountBookDto accountBookDto;
	/**
	 * 财税表单
	 */
	public TaxFormDto taxFormDto;
	/**
	 * 财税表单2
	 */
	public Map<String, TaxInfo> taxInfoMap;
	/**
	 * 工资表信息
	 */
	public List<PaySheetDto> paySheetDtoList;
	/**
	 * 修改过的员工信息
	 */
	public List<PaySheetDto> modifiedEmployeeList;
	/**
	 * 生产经营所得
	 */
	public IncomeTaxDeclarationOperatingDto incomeOperating;
	/**
	 * 报税信息
	 */
	public TaxDataDto taxDataDto;
	/**
	 * 是否零申报
	 */
	public Boolean isZeroPeriod;
	/**
	 * 办税人手机号
	 */
	public String taxPayerPhone;
	/**
	 * 电子税务局登录名
	 */
	public String nationalName;
	/**
	 * 城市
	 */
	public String city;
	/**
	 * 运行中的任务
	 */
	public List<TaxRecord> runningTaxRecords;
	/**
	 * 账套相关的所有任务
	 */
	public List<TaxRecord> allTaxRecords;

	public TaxRecordBookDto() {
	}

	public TaxRecordBookDto(Date gmtModified, Long accountBookId, String taxPayerPhone, String city, Integer areaId, String resultMessage, Integer orderLevel, Integer sourceId, Long id) {
		this.gmtModified = gmtModified;
		this.accountBookId = accountBookId;
		this.taxPayerPhone = taxPayerPhone;
		this.areaId = areaId;
		this.resultMessage = resultMessage;
		this.orderLevel = orderLevel;
		this.id = id;
		this.sourceId = sourceId;
		this.city = city;
	}

	public TaxRecordBookDto(Date gmtModified, Long accountBookId, String taxPayerPhone, String city, Integer areaId, String resultMessage, Integer orderLevel, Integer sourceId, Long id, String period) {
		this.gmtModified = gmtModified;
		this.accountBookId = accountBookId;
		this.taxPayerPhone = taxPayerPhone;
		this.areaId = areaId;
		this.resultMessage = resultMessage;
		this.orderLevel = orderLevel;
		this.id = id;
		this.sourceId = sourceId;
		this.period = period;
		this.city = city;
	}

	public TaxRecordBookDto(Long id, Long accountBookId, String accountBookName, String period, Integer taxState, Integer actionId, Date gmtModified, Date gmtCreate, String resultMessage, String resultUrl, String resultImageUrl) {
		this.id = id;
		this.accountBookId = accountBookId;
		this.accountBookName = accountBookName;
		this.period = period;
		this.taxState = taxState;
		this.actionId = actionId;
		this.gmtModified = gmtModified;
		this.gmtCreate = gmtCreate;
		this.resultMessage = resultMessage;
		this.resultUrl = resultUrl;
		this.resultImageUrl = resultImageUrl;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
