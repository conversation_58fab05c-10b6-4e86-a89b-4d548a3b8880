package com.hongfund.efi.dto;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class CashFlowStatementListDto {
    public Integer isShow = 0;                   //是否显示“期初”按钮，0：不显示，1：显示

    public List<CashFlowStatementRowDto> cashList = new ArrayList<>();

    /**
     * 不同月份相加
     */
    public void add(CashFlowStatementListDto cash) {
        if (cashList.size() == 0) {
            cashList = cash.cashList;
            return;
        }
        if (cash.cashList.size() == 0) {
            return;
        }
        for (int i = 0;i < cashList.size();i++) {
            cashList.get(i).monthCashBD = cashList.get(i).monthCashBD.add(cash.cashList.get(i).monthCashBD);
        }
    }

    /**
     * 计算期初数=本年累计-本期数
     */
    public void calculateInitData() {
        for (CashFlowStatementRowDto cash : cashList) {
            cash.monthCashBD = cash.yearCashBD.subtract(cash.monthCashBD);
        }
    }

    /**
     * 计算某一行数据
     */
    public BigDecimal calculateData(int i) {
        if (cashList == null) {
            return null;
        }
        if (cashList.size() == 0) {
            return BigDecimal.ZERO;
        }
        return cashList.get(i).monthCashBD;
    }

    /**
     * 清理数据
     */
    public void clear() {
        cashList = null;
    }

    /**
     * 根据给出的期初数，设置期初现金余额和期末现金余额
     */
    public void calculateCashBalance(BigDecimal initBalance) {
        if (cashList != null && cashList.size() > 0) {
            cashList.get(cashList.size()-2).monthCashBD = initBalance;
            cashList.get(cashList.size()-1).monthCashBD = initBalance.add(cashList.get(cashList.size()-3).monthCashBD);
        }
    }

    public BigDecimal findAddBalance() {
        if (cashList != null && cashList.size() > 0) {
            return cashList.get(cashList.size()-3).monthCashBD;
        }
        return BigDecimal.ZERO;
    }
}
