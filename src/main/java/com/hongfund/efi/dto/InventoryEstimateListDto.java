package com.hongfund.efi.dto;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 暂估回冲列表
 */
public class InventoryEstimateListDto {
	public Long accountBookId;
	public String period;
	public Integer dimInventoryType;
	/**
	 * 按钮：估价
	 */
	public boolean isGj;
	/**
	 * 按钮：重新估价
	 */
	public boolean isCxgj;
	/**
	 * 按钮：暂估入库
	 */
	public boolean isZgrk;
	/**
	 * 按钮：回冲
	 */
	public boolean isHc;
	/**
	 * 按钮：重新回冲
	 */
	public boolean isCxhc;
	/**
	 * 数据行
	 */
	public List<InventoryEstimateRowDto> list = new ArrayList<>();
	/**
	 * 合计
	 */
	public InventoryEstimateRowDto total = new InventoryEstimateRowDto();

	public void countButtons() {
		isGj = true;
		isCxgj = false;
		isZgrk = true;
		isHc = false;
		isCxhc = false;
		for (InventoryEstimateRowDto row : list) {
			if (StringUtils.equals(row.dir, "待入库") || StringUtils.equals(row.dir, "已入库")) {
				isGj = false;
				isCxgj = true;
			}
			if (StringUtils.equals(row.dir, "已入库")) {
				isZgrk = false;
			}
			if (row.numberInit.subtract(row.numberBack).compareTo(BigDecimal.ZERO) > 0) {
				isHc = true;
			}
			if (row.numberBack.compareTo(BigDecimal.ZERO) != 0 || row.balanceBack.compareTo(BigDecimal.ZERO) != 0) {
				isCxhc = true;
			}
		}
	}
}
