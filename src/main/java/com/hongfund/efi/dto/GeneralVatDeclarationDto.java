package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class GeneralVatDeclarationDto {
	public Long id;
	/**
	 *帐套id
	 */
	public Long accountBookId;
	/**
	 *end期间
	 */
	public String period;
	/**
	 *栏次
	 */
	public Integer row;
	/**
	 * 数据
	 */
	public BigDecimal data;

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
