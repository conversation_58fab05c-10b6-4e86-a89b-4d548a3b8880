package com.hongfund.efi.dto;

import com.hongfund.efi.domain.BaseBalance;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

import static com.hongfund.efi.utils.BigDecimalUtils.getPercentage;
import static com.hongfund.efi.utils.BigDecimalUtils.multiply;

/**
 * 产成品入库、结转生产生本表
 */
public class InventoryCostListDto {
	public Long accountBookId;
	public String period;
	/**
	 * 账面金额
	 */
	public InventoryCostRowDto amount = new InventoryCostRowDto();
	/**
	 * 待分配金额
	 */
	public InventoryCostRowDto distributedAmount = new InventoryCostRowDto();
	/**
	 * 数据行
	 */
	public List<InventoryCostRowDto> list = new ArrayList<>();
	/**
	 * 合计
	 */
	public InventoryCostRowDto total = new InventoryCostRowDto();
	/**
	 * 结转生产成本方式（0：按成本分配系数 1：按照全部结转来【全额结转】）
	 */
	public Integer costType;
	/**
	 * 本月主营业务收入
	 */
	public BigDecimal balanceMain = BigDecimal.ZERO;
	/**
	 * 销售成本结转百分比（主营业务收入）
	 */
	public Integer costPer;
	/**
	 * 预计本月销售成本
	 */
	public BigDecimal balanceCost = BigDecimal.ZERO;
	/**
	 * 生产成本余额
	 */
	public BigDecimal costBalance = BigDecimal.ZERO;
	/**
	 * 成本分配系数=预计本期销售成本/生产成本余额=2.********
	 */
	public BigDecimal costAllocation = BigDecimal.ZERO;
	/**
	 * 结转生产成本是否再次结转（0：否 1：是）
	 */
	public Integer isContinue;

	public void calculate(BaseBalance main, BaseBalance cost, BigDecimal balanceDM, BigDecimal balanceDL, BigDecimal balanceMF, BigDecimal balanceOther) {
		BigDecimal percentage = getPercentage(costPer);
		// 主营业务收入是根据主营业务收入这个科目的贷方发生额来获取数据的
		balanceMain = main.dfbalance;
		// 生产成本余额是根据生产成本这个科目的期末余额来的
		costBalance = cost.endbalance;
		// 预计本月销售成本=主营业务收入*销售成本结转百分比
		balanceCost = balanceMain.multiply(percentage);
		// 成本分配系数=预计本期销售成本/生产成本余额=**（保留八位小数）
		costAllocation = costBalance == null || costBalance.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : balanceCost.divide(costBalance, 8, RoundingMode.HALF_UP);

		// 账面金额 这一行，只有生产成本这栏是有数据的，
		// 成本合计是根据生产成本这个科目的期末余额，
		amount.costCount = cost.endbalance;
		// 直接材料是根据生产成本这个科目下面的下级科目直接材料的期末余额，
		amount.directMaterials = balanceDM;
		// 直接人工是根据生产成本这个科目下面的下级科目直接人工的期末余额，
		amount.directLabour = balanceDL;
		// 制造费用是根据生产成本这个科目下面的下级科目制造费用的期末余额，
		amount.manufactureFee = balanceMF;
		// 其他小计是根据生产成本这个科目下面的下级科目除了直接材料，直接人工与制造费用之外的期末余额；
		amount.otherCount = balanceOther;

		// 方式一、按照成本分配系数来 待分配金额这一行，只有生产成本这栏是有数据的，
		if (costType == 0) {
			// 成本合计就是预计本月销售成本=主营业务收入*销售成本结转百分比，
			distributedAmount.costCount = balanceCost;
			//直接材料是账面金额这一行直接材料*成本分配系数，
			distributedAmount.directMaterials = multiply(balanceDM, costAllocation);
			// 直接人工是账面金额这一行直接人工*成本分配系数，
			distributedAmount.directLabour = multiply(balanceDL, costAllocation);
			// 制造费用是账面金额这一行制造费用*成本分配系数，
			distributedAmount.manufactureFee = multiply(balanceMF, costAllocation);
			// 其他小计是账面金额这一行其他小计*成本分配系数。
			distributedAmount.otherCount = multiply(balanceOther, costAllocation);
		} else {
			// 方式二、按照全部结转来  待分配金额这一行，只有生产成本这栏是有数据的，
			// 成本合计=账面金额这一行成本合计，
			distributedAmount.costCount = amount.costCount;
			// 直接材料=账面金额这一行直接材料，
			distributedAmount.directMaterials = amount.directMaterials;
			// 直接人工=账面金额这一行直接人工，
			distributedAmount.directLabour = amount.directLabour;
			// 制造费用=账面金额这一行制造费用，
			distributedAmount.manufactureFee = amount.manufactureFee;
			// 其他小计=账面金额这一行其他小计。
			distributedAmount.otherCount = amount.otherCount;
		}
	}
}
