package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.Date;

/**
 * 报税记录表
 */
public class AccountBookTaxDto {
	public Long accountBookId;
	public String bookName;
	/**
	 * 锁定时间
	 */
	public String lockTime;
	/**
	 * 锁定状态 0：空闲 1：锁定
	 */
	public Integer lockState = 0;
	/**
	 * 电子税务局登录名
	 */
	public String nationalName;
	/**
	 * 是否首次点击
	 */
	public Boolean isFirst = true;

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
