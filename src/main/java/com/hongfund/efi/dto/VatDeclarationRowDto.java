package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

/**
 * <AUTHOR>
 */
public class VatDeclarationRowDto {
	/**
	 * 主键
	 */
	public String id;
	/**
	 * 帐套id
	 */
	public String accountBookId;
	/**
	 * end期间
	 */
	public String endPeriod;
	/**
	 * 栏次
	 */
	public String row;
	/**
	 * 货物及劳务
	 */
	public String goods;
	/**
	 * 服务、不动产和无形资产
	 */
	public String service;


	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

}
