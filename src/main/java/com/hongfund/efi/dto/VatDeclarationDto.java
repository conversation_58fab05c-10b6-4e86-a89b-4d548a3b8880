package com.hongfund.efi.dto;

import com.hongfund.efi.utils.DBCache;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.ArrayList;
import java.util.List;

import static com.hongfund.efi.profile.BusinessType.GOODS;
import static com.hongfund.efi.profile.BusinessType.SERVICE;
import static com.hongfund.efi.utils.BigDecimalUtils.sum;


/**
 * <AUTHOR>
 */
public class VatDeclarationDto {

	public List<String> currentGoods = new ArrayList<>();
	public List<String> currentService = new ArrayList<>();
	public List<String> totalGoods = new ArrayList<>();
	public List<String> totalService = new ArrayList<>();
	public String startPeriod;
	public String endPeriod;
	public Integer isSave = 0;
	public String goods3;
	public String service3;
	// 货物销项含税金额
	public String goods;
	// 货物专票含税金额
	public String specialGoods;
	// 服务销项含税金额
	public String service;
	// 服务专票含税金额
	public String specialService;

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

	public void setData(DBCache cache, int businessType, String startPeriod, String endPeriod) {
		// 销项3%不含税金额
		goods3 = cache.calculateByInvoices(-1, 3, GOODS, businessType, 0, -1, startPeriod, endPeriod, true);
		String goodsTax3 = cache.calculateByInvoices(-1, 3, GOODS, businessType, 1, -1, startPeriod, endPeriod, true);
		String goods1 = cache.calculateByInvoices(-1, 1, GOODS, businessType, 0, -1, startPeriod, endPeriod, true);
		String goodsTax1 = cache.calculateByInvoices(-1, 1, GOODS, businessType, 1, -1, startPeriod, endPeriod, true);
		// 所有销项票含税金额
		if (StringUtils.isNotBlank(goods3) || StringUtils.isNotBlank(goodsTax3) || StringUtils.isNotBlank(goods1) || StringUtils.isNotBlank(goodsTax1)) {
			goods = sum(goods3, goodsTax3, goods1, goodsTax1);
		}

		String specialGoods3 = cache.calculateByInvoices(1, 3, GOODS, businessType, 0, -1, startPeriod, endPeriod, true);
		String specialGoodsTax3 = cache.calculateByInvoices(1, 3, GOODS, businessType, 1, -1, startPeriod, endPeriod, true);
		String specialGoods1 = cache.calculateByInvoices(1, 1, GOODS, businessType, 0, -1, startPeriod, endPeriod, true);
		String specialGoodsTax1 = cache.calculateByInvoices(1, 1, GOODS, businessType, 1, -1, startPeriod, endPeriod, true);
		// 所有销项专票含税金额
		if (StringUtils.isNotBlank(specialGoods3) || StringUtils.isNotBlank(specialGoodsTax3) || StringUtils.isNotBlank(specialGoods1) || StringUtils.isNotBlank(specialGoodsTax1)) {
			specialGoods = sum(specialGoods3, specialGoodsTax3, specialGoods1, specialGoodsTax1);
		}

		// 销项3%不含税金额
		service3 = cache.calculateByInvoices(-1, 3, SERVICE, businessType, 0, -1, startPeriod, endPeriod, true);
		String serviceTax3 = cache.calculateByInvoices(-1, 3, SERVICE, businessType, 1, -1, startPeriod, endPeriod, true);
		String service1 = cache.calculateByInvoices(-1, 1, SERVICE, businessType, 0, -1, startPeriod, endPeriod, true);
		String serviceTax1 = cache.calculateByInvoices(-1, 1, SERVICE, businessType, 1, -1, startPeriod, endPeriod, true);
		// 所有销项票含税金额
		if (StringUtils.isNotBlank(service3) || StringUtils.isNotBlank(serviceTax3) || StringUtils.isNotBlank(service1) || StringUtils.isNotBlank(serviceTax1)) {
			service = sum(service3, serviceTax3, service1, serviceTax1);
		}

		String specialService3 = cache.calculateByInvoices(1, 3, SERVICE, businessType, 0, -1, startPeriod, endPeriod, true);
		String specialServiceTax3 = cache.calculateByInvoices(1, 3, SERVICE, businessType, 1, -1, startPeriod, endPeriod, true);
		String specialService1 = cache.calculateByInvoices(1, 1, SERVICE, businessType, 0, -1, startPeriod, endPeriod, true);
		String specialServiceTax1 = cache.calculateByInvoices(1, 1, SERVICE, businessType, 1, -1, startPeriod, endPeriod, true);
		// 所有销项专票含税金额
		if (StringUtils.isNotBlank(specialService3) || StringUtils.isNotBlank(specialServiceTax3) || StringUtils.isNotBlank(specialService1) || StringUtils.isNotBlank(specialServiceTax1)) {
			specialService = sum(specialService3, specialServiceTax3, specialService1, specialServiceTax1);
		}
	}
}
