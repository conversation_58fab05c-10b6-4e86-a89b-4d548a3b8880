package com.hongfund.efi.dto;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TaxReportBookDto {
    public Long id;
    public Date gmtCreate;
    public Date gmtModified;
    public Long accountBookId;
    public String accountBookName;
    public Long accountId;
    public String resultMessage;
    public String addtax;
    public String accountingSystem;
    public String accounterNo;
    /**
     * 验证状态
     */
    public Integer authState = -1;
    /**
     * 登录最终用户名（用于连表查询）nationalNameFinal = loginWay == 3 ? socialCreditCodeAgent : taxPayerPhoneAuth
     */
    public String nationalNameFinal;
    /**
     * 电子税务局登录方式 1:旧版登录；2：新版登录；3：代理登录
     */
    public Integer loginWay;
    /**
     * 	标签
     */
    public String label;
    /**
     * 社会信用代码
     */
    public String socialCreditCode;
    /**
     * 检测报告生成状态 1:未生成；2:等待中；3:生成中；4:已生成；5:生成失败
     */
    public Integer taxReportState;
    /**
     * 详情
     */
    public List<TaxReportDto> reports = new ArrayList<>();

    public TaxReportBookDto() {
    }

    // 用于AccountBookDao查询的构造函数
    public TaxReportBookDto(Long accountBookId, Long accountId, String accountBookName, String addtax, String accountingSystem,
                           String accounterNo, String label, String socialCreditCode, String nationalNameFinal,
                           Integer loginWay, Date gmtCreate, Date gmtModified, Integer taxReportState, String resultMessage) {
        this.accountBookId = accountBookId;
        this.accountId = accountId;
        this.accountBookName = accountBookName;
        this.addtax = addtax;
        this.accountingSystem = accountingSystem;
        this.accounterNo = accounterNo;
        this.label = label;
        this.socialCreditCode = socialCreditCode;
        this.nationalNameFinal = nationalNameFinal;
        this.loginWay = loginWay;
        this.resultMessage = resultMessage;
        this.gmtCreate = gmtCreate;
        this.taxReportState = taxReportState;
        this.gmtModified = gmtModified;
    }
} 