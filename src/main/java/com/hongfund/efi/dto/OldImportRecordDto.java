package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.Date;

public class OldImportRecordDto {
    public Long id;
    public Date gmtCreate;//创建
    public Date gmtModified; //修改时间
    public Long accountId;//用户id
    public Long importStatus;//导账状态
    public Integer oldImportType;//导账类型
    public String  middleDataUrl; //中间数据url
    public String finalDataUrl; //最终数据url
    public String resultMessage; //结果信息
    public String accountBookName;//账套名称
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
