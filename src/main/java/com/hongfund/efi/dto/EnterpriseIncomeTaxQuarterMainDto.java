package com.hongfund.efi.dto;

import java.util.Date;

public class EnterpriseIncomeTaxQuarterMainDto {
    public Long id;
    public Long accountBookId;            //帐套
    public String startPeriod;            /*起始期间*/
    public String endPeriod;              /*结束期间*/
    public Date writtenDate;              /*填报日期*/
    public Date declareDate;              /*申报日期*/
    public Integer payType = 0;           /*预缴方式 0：按照实际利润额预缴 1：按照上一纳税年度应纳税得额平均额预缴 2：按照税务机关确定的其他方法预缴*/
    public Integer companyType = 0;       /*企业类型 0：一般企业 1：跨地区经营汇总纳税企业总机构 2：跨地区经营汇总纳税企业分支机构*/
    public Integer isTechSmall = 0;       /*科技型中小企业 默认否*/
    public Integer isTechHigh = 0;        /*高新技术企业 默认否*/
    public Integer isTechShare = 0;       /*技术入股递延纳税事项 默认否*/
    public Integer startPersonNum1;       /*1季度季初从业人数*/
    public Integer endPersonNum1;         /*1季度季末从业人数*/
    public String startAssets1;           /*1季度季初资产总额（万元）*/
    public String endAssets1;             /*1季度季末资产总额（万元）*/
    public Integer startPersonNum2;       /*2季度季初从业人数*/
    public Integer endPersonNum2;         /*2季度季末从业人数*/
    public String startAssets2;           /*2季度季初资产总额（万元）*/
    public String endAssets2;             /*2季度季末资产总额（万元）*/
    public Integer startPersonNum3;       /*3季度季初从业人数*/
    public Integer endPersonNum3;         /*3季度季末从业人数*/
    public String startAssets3;           /*3季度季初资产总额（万元）*/
    public String endAssets3;             /*3季度季末资产总额（万元）*/
    public Integer startPersonNum4;       /*4季度季初从业人数*/
    public Integer endPersonNum4;         /*4季度季末从业人数*/
    public String startAssets4;           /*4季度季初资产总额（万元）*/
    public String endAssets4;             /*4季度季末资产总额（万元）*/
    public Integer personNumAvg;          /*季度平均数从业人数*/
    public String assetsAvg;              /*季度平均数资产总额（万元）*/
    public Integer isStateLimit = 0;      /*国家限制或禁止行业 默认否*/
    public Integer isSmallLowProfit = 1;  /*小型微利企业 默认是*/
    public String previousLoss;           /*以前年度亏损额*/
    public String changeTypeName;         /*修改的字段名称*/
    public String row1;
    public String row2;
    public String row3;
    public String row4;
    public String row5;
    public String row6;
    public String row7;
    public String row8;
    public String row9;
    public String row10;
    public String row11;
    public String row12;
    public String row13;
    public String row14;
    public String rowL15;
    public String row15;
    public String row16;
    public String row17;
    public String row18;
    public String row19;
    public String row20;
    public String row21;
}
