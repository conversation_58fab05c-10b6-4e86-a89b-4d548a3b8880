package com.hongfund.efi.dto;

import com.hongfund.efi.profile.SalesDetailFormula;
import com.hongfund.efi.utils.BigDecimalUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;

import static com.hongfund.efi.service.SalesDetailService.NO_VALUE;

/**
 * 销售明细表（一般纳税人增值税申报表附表一）
 * <AUTHOR>
 */
public class SalesDetailRowDto {
	/**
	 * 主键
	 */
	public String id;
	/**
	 * /帐套id
	 */
	public String accountBookId;
	/**
	 * start期间
	 */
	public String startPeriod;
	/**
	 * end期间
	 */
	public String endPeriod;
	/**
	 * 序号
	 */
	public String no;
	/**
	 * 开具增值税专用发票的销售额
	 */
	public String invoiceSales;
	/**
	 * 开具增值税专用发票的销项（应纳）税额
	 */
	public String invoiceTax;
	/**
	 * 开具其他发票的销售额
	 */
	public String otherInvoiceSales;
	/**
	 * 开具其他发票的销项（应纳）税额
	 */
	public String otherInvoiceTax;
	/**
	 * 未开具发票的销售额
	 */
	public String noInvoiceSales;
	/**
	 * 未开具发票的销项（应纳）税额
	 */
	public String noInvoiceTax;
	/**
	 * 纳税检查调整的销售额
	 */
	public String taxInspectionSales;
	/**
	 * 纳税检查调整的销项（应纳）税额
	 */
	public String taxInspectionTax;
	/**
	 * 合计销售额
	 */
	public String totalSales;
	/**
	 * 合计销项（应纳）税额
	 */
	public String totalTax;
	/**
	 * 合计价税合计
	 */
	public String totalSalesAndTax;
	/**
	 * 本期实际扣除金额
	 */
	public String deductBalance;
	/**
	 * 扣除后销售额
	 */
	public String afterDeductSales;
	/**
	 * 扣除后销项（应纳）税额
	 */
	public String afterDeductTax;
	/**
	 * 修改的列
	 */
	public Integer modifiedColumn;

	public void setZero() {
		if (StringUtils.equals(invoiceSales, NO_VALUE)) {
			invoiceSales = "0.00";
		}
		if (StringUtils.equals(invoiceTax, NO_VALUE)) {
			invoiceTax = "0.00";
		}
		if (StringUtils.equals(otherInvoiceSales, NO_VALUE)) {
			otherInvoiceSales = "0.00";
		}
		if (StringUtils.equals(otherInvoiceTax, NO_VALUE)) {
			otherInvoiceTax = "0.00";
		}
		if (StringUtils.equals(noInvoiceSales, NO_VALUE)) {
			noInvoiceSales = "0.00";
		}
		if (StringUtils.equals(noInvoiceTax, NO_VALUE)) {
			noInvoiceTax = "0.00";
		}
		if (StringUtils.equals(taxInspectionSales, NO_VALUE)) {
			taxInspectionSales = "0.00";
		}
		if (StringUtils.equals(taxInspectionTax, NO_VALUE)) {
			taxInspectionTax = "0.00";
		}
		if (StringUtils.equals(totalSales, NO_VALUE)) {
			totalSales = "0.00";
		}
		if (StringUtils.equals(totalTax, NO_VALUE)) {
			totalTax = "0.00";
		}
		if (StringUtils.equals(totalSalesAndTax, NO_VALUE)) {
			totalSalesAndTax = "0.00";
		}
		if (StringUtils.equals(deductBalance, NO_VALUE)) {
			deductBalance = "0.00";
		}
		if (StringUtils.equals(afterDeductSales, NO_VALUE)) {
			afterDeductSales = "0.00";
		}
		if (StringUtils.equals(afterDeductTax, NO_VALUE)) {
			afterDeductTax = "0.00";
		}
	}

	public void setNoValue(SalesDetailFormula formula) {
		if (StringUtils.equals(formula.invoiceSales, NO_VALUE)) {
			invoiceSales = NO_VALUE;
		}
		if (StringUtils.equals(formula.invoiceTax, NO_VALUE)) {
			invoiceTax = NO_VALUE;
		}
		if (StringUtils.equals(formula.otherInvoiceSales, NO_VALUE)) {
			otherInvoiceSales = NO_VALUE;
		}
		if (StringUtils.equals(formula.otherInvoiceTax, NO_VALUE)) {
			otherInvoiceTax = NO_VALUE;
		}
		if (StringUtils.equals(formula.noInvoiceSales, NO_VALUE)) {
			noInvoiceSales = NO_VALUE;
		}
		if (StringUtils.equals(formula.noInvoiceTax, NO_VALUE)) {
			noInvoiceTax = NO_VALUE;
		}
		if (StringUtils.equals(formula.taxInspectionSales, NO_VALUE)) {
			taxInspectionSales = NO_VALUE;
		}
		if (StringUtils.equals(formula.taxInspectionTax, NO_VALUE)) {
			taxInspectionTax = NO_VALUE;
		}
		if (StringUtils.equals(formula.totalSales, NO_VALUE)) {
			totalSales = NO_VALUE;
		}
		if (StringUtils.equals(formula.totalTax, NO_VALUE)) {
			totalTax = NO_VALUE;
		}
		if (StringUtils.equals(formula.totalSalesAndTax, NO_VALUE)) {
			totalSalesAndTax = NO_VALUE;
		}
		if (StringUtils.equals(formula.deductBalance, NO_VALUE)) {
			deductBalance = NO_VALUE;
		}
		if (StringUtils.equals(formula.afterDeductSales, NO_VALUE)) {
			afterDeductSales = NO_VALUE;
		}
		if (StringUtils.equals(formula.afterDeductTax, NO_VALUE)) {
			afterDeductTax = NO_VALUE;
		}
	}

	/**
	 * 格式化数字
	 */
	public void formatDecimal() {
		invoiceSales = BigDecimalUtils.format(invoiceSales);
		invoiceTax = BigDecimalUtils.format(invoiceTax);
		otherInvoiceSales = BigDecimalUtils.format(otherInvoiceSales);
		otherInvoiceTax = BigDecimalUtils.format(otherInvoiceTax);
		noInvoiceSales = BigDecimalUtils.format(noInvoiceSales);
		noInvoiceTax = BigDecimalUtils.format(noInvoiceTax);
		taxInspectionSales = BigDecimalUtils.format(taxInspectionSales);
		taxInspectionTax = BigDecimalUtils.format(taxInspectionTax);
		totalSales = BigDecimalUtils.format(totalSales);
		totalTax = BigDecimalUtils.format(totalTax);
		totalSalesAndTax = BigDecimalUtils.format(totalSalesAndTax);
		deductBalance = BigDecimalUtils.format(deductBalance);
		afterDeductSales = BigDecimalUtils.format(afterDeductSales);
		afterDeductTax = BigDecimalUtils.format(afterDeductTax);
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}


	/**
	 * 根据关键字，获取附表一对应数据（主表用）
	 */
	public String getColumn(String column){
		switch (column){
			case "M" :
				return totalSales;
			case "E" :
				return invoiceSales;
			case "K" :
				return taxInspectionSales;
			case "G" :
				return otherInvoiceSales;
			case "I" :
				return noInvoiceSales;
			case "N" :
				return totalTax;
			case "L" :
				return taxInspectionTax;
				default:
					return null;
		}
	}

}
