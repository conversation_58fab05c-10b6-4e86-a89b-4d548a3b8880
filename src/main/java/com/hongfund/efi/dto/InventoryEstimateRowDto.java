package com.hongfund.efi.dto;

import com.hongfund.efi.domain.InventoryBack;
import com.hongfund.efi.domain.InventoryEstimate;
import com.hongfund.efi.utils.DBCache;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * 暂估回冲列表行
 */
public class InventoryEstimateRowDto {
	public Long id;
	public Long accountBookId;
	public String period;
	/**
	 * 名称
	 */
	public String name;
	/**
	 * 编码
	 */
	public String no;
	/**
	 * 计量单位
	 */
	public String measureUnit;
	/**
	 * 规格型号
	 */
	public String model;
	/**
	 * 期初暂估：金额
	 */
	public BigDecimal balanceInit = BigDecimal.ZERO;
	/**
	 * 期初暂估：数量
	 */
	public BigDecimal numberInit = BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);
	/**
	 * 本期暂估：方向
	 */
	public String dir;
	/**
	 * 本期暂估：金额
	 */
	public BigDecimal balanceEstimate = BigDecimal.ZERO;
	/**
	 * 本期暂估：数量
	 */
	public BigDecimal numberEstimate = BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);
	/**
	 * 本期暂估：单价
	 */
	public BigDecimal unitPriceEstimate = BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);
	/**
	 * 入库：单价
	 */
	public BigDecimal unitPriceIn = BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);
	/**
	 * 销售：单价
	 */
	public BigDecimal unitPriceOut = BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);
	/**
	 * 本期回冲：金额
	 */
	public BigDecimal balanceBack = BigDecimal.ZERO;
	/**
	 * 本期回冲：数量
	 */
	public BigDecimal numberBack = BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);
	/**
	 * 期末暂估：金额
	 */
	public BigDecimal balanceEnd = BigDecimal.ZERO;
	/**
	 * 期末暂估：数量
	 */
	public BigDecimal numberEnd = BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);

	public void add(InventoryEstimateRowDto row) {
		balanceInit = balanceInit.add(row.balanceInit);
		numberInit = numberInit.add(row.numberInit);
		balanceEstimate = balanceEstimate.add(row.balanceEstimate);
		numberEstimate = numberEstimate.add(row.numberEstimate);
		balanceBack = balanceBack.add(row.balanceBack);
		numberBack = numberBack.add(row.numberBack);
		balanceEnd = balanceEnd.add(row.balanceEnd);
		numberEnd = numberEnd.add(row.numberEnd);
	}

	public static boolean isEstimate(DBCache cache, Long id, String period) {
		for (InventoryEstimate inventoryEstimate : cache.inventoryEstimates) {
			if (Objects.equals(inventoryEstimate.dimInventoryId, id) && StringUtils.equals(period, inventoryEstimate.period)) {
				return true;
			}
		}
		return false;
	}

	public void countRow(DBCache cache, Long id, String period) {
		for (InventoryEstimate inventoryEstimate : cache.inventoryEstimates) {
			if (Objects.equals(inventoryEstimate.dimInventoryId, id)) {
				if (StringUtils.equals(period, inventoryEstimate.period)) {
					numberEstimate = numberEstimate.add(inventoryEstimate.number);
					balanceEstimate = balanceEstimate.add(inventoryEstimate.balance);
					if (inventoryEstimate.voucherId == null) {
						dir = "待入库";
					}
					else {
						dir = "已入库";
					}
				}
				else if (StringUtils.compare(inventoryEstimate.period, period) < 0) {
					numberInit = numberInit.add(inventoryEstimate.number);
					balanceInit = balanceInit.add(inventoryEstimate.balance);
				}
			}
		}
		for (InventoryBack inventoryBack : cache.inventoryBacks) {
			if (Objects.equals(inventoryBack.dimInventoryId, id)) {
				if (StringUtils.equals(period, inventoryBack.period)) {
					numberBack = numberBack.add(inventoryBack.number);
					balanceBack = balanceBack.add(inventoryBack.balance);
				}
				else if (StringUtils.compare(inventoryBack.period, period) < 0) {
					numberInit = numberInit.subtract(inventoryBack.number);
					balanceInit = balanceInit.subtract(inventoryBack.balance);
				}
			}
		}
	}

	public void countEnd() {
		balanceEnd = balanceInit;
		numberEnd = numberInit;
		if (StringUtils.equals("已入库", dir)) {
			balanceEnd = balanceEnd.add(balanceEstimate);
			numberEnd = numberEnd.add(numberEstimate);
		}
		balanceEnd = balanceEnd.subtract(balanceBack);
		numberEnd = numberEnd.subtract(numberBack);
	}

	public void countPrice() {
		if (numberEstimate.compareTo(BigDecimal.ZERO) != 0) {
			unitPriceEstimate = balanceEstimate.divide(numberEstimate, 4, RoundingMode.HALF_UP);
		}
	}
}
