package com.hongfund.efi.dto;

import com.hongfund.efi.domain.BaseBalance;
import com.hongfund.efi.domain.InventoryCost;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static com.hongfund.efi.utils.BigDecimalUtils.*;

/**
 * 产成品入库、结转生产生本表行
 */
public class InventoryCostRowDto {
	public Long id;
	public Long accountBookId;
	public String period;
	/**
	 * 名称
	 */
	public String name;
	/**
	 * 编码
	 */
	public String no;
	/**
	 * 计量单位
	 */
	public String measureUnit;
	/**
	 * 规格型号
	 */
	public String model;
	/**
	 * 产成品期初余额：数量
	 */
	public BigDecimal initNumber = BigDecimal.ZERO;
	/**
	 * 产成品期初余额：单价
	 */
	public BigDecimal initUnitPrice = BigDecimal.ZERO;
	/**
	 * 产成品期初余额：金额
	 */
	public BigDecimal initBalance = BigDecimal.ZERO;
	/**
	 * 本月销售：销售数量
	 */
	public BigDecimal sellNumber = BigDecimal.ZERO;
	/**
	 * 本月销售：单价
	 */
	public BigDecimal sellUnitPrice = BigDecimal.ZERO;
	/**
	 * 本月销售：销售收入
	 */
	public BigDecimal sellBalance = BigDecimal.ZERO;
	/**
	 * 本月完工：完工数量
	 */
	public BigDecimal finishNumber = BigDecimal.ZERO;
	/**
	 * 本月完工：完工产值
	 */
	public BigDecimal finishValue = BigDecimal.ZERO;
	/**
	 * 本月完工：完工产值百分比
	 */
	public BigDecimal finishValuePer = BigDecimal.ZERO;
	/**
	 * 生产成本：成本合计
	 */
	public BigDecimal costCount = BigDecimal.ZERO;
	/**
	 * 生产成本：直接材料
	 */
	public BigDecimal directMaterials = BigDecimal.ZERO;
	/**
	 * 生产成本：直接人工
	 */
	public BigDecimal directLabour = BigDecimal.ZERO;
	/**
	 * 生产成本：制造费用
	 */
	public BigDecimal manufactureFee = BigDecimal.ZERO;
	/**
	 * 生产成本：其他小计
	 */
	public BigDecimal otherCount = BigDecimal.ZERO;
	/**
	 * 单位成本：单价
	 */
	public BigDecimal unitPriceAvg = BigDecimal.ZERO;
	/**
	 * 期末转存：数量
	 */
	public BigDecimal endNumber = BigDecimal.ZERO;
	/**
	 * 期末转存：单价
	 */
	public BigDecimal endUnitPrice = BigDecimal.ZERO;
	/**
	 * 期末转存：金额
	 */
	public BigDecimal endBalance = BigDecimal.ZERO;
	/**
	 * 毛利率
	 */
	public BigDecimal grossProfitRate = BigDecimal.ZERO;

	public void add(InventoryCostRowDto row) {
		initBalance = initBalance.add(row.initBalance);
		sellBalance = sellBalance.add(row.sellBalance);
		finishValue = finishValue.add(row.finishValue);
		costCount = costCount.add(row.costCount);
		directMaterials = directMaterials.add(row.directMaterials);
		directLabour = directLabour.add(row.directLabour);
		manufactureFee = manufactureFee.add(row.manufactureFee);
		otherCount = otherCount.add(row.otherCount);
		endBalance = endBalance.add(row.endBalance);
	}

	public void calculate(InventoryCost inventoryCost, BaseBalance balance, BaseBalance balanceMain) {
		// 产成品期初余额是根据各个库存商品对应的存货的数量金额余额表的期初数据的获取的
		initBalance = balance.initbalance;
		initNumber = balance.initnumber;
		if (initNumber.compareTo(BigDecimal.ZERO) != 0) {
			initUnitPrice = initBalance.divide(initNumber, 4, RoundingMode.HALF_UP);
		}
		// 本月销售是根据主营业务收入对应的的存货的数量金额余额表的贷方发生额数据的获取的（当某个存货销售多次的话，单价就是平均值）
		sellNumber = balanceMain.dfnumber;
		sellBalance = balanceMain.dfbalance;
		if (sellNumber.compareTo(BigDecimal.ZERO) != 0) {
			sellUnitPrice = sellBalance.divide(sellNumber, 4, RoundingMode.HALF_UP);
		}

		// 本月完工里面的完工数量是可以填写的，保留两位小数（默认值是本月销售数量，以销定产，如果某个存货有期初但没有销售的话，是不会结转生产成本），
		// 填写好之后，点击查询，对应存货的完工产值，产值百分比，成本合计，直接材料，直接人工，制造费用，其他小计都会自动计算出来
		// 完工产值=完工数量*本月销售的单价，本期产值百分比=完工产值/产值总额（产值总额等于各个存货的完工产值合计），
		if (inventoryCost != null) {
			finishNumber = inventoryCost.number;
			finishValue = inventoryCost.value;
			finishValuePer = inventoryCost.valuePer;
		} else {
			finishNumber = sellNumber;
			finishValue = multiply(finishNumber, sellUnitPrice);
		}
	}

	public void calculate(InventoryCostListDto listDto, BigDecimal totalFinishValue) {
		if (finishValuePer.compareTo(BigDecimal.ZERO) == 0 && finishValue.compareTo(BigDecimal.ZERO) != 0) {
			BigDecimal per = divide(finishValue, totalFinishValue, 4);
			finishValuePer = per.multiply(getDecimalValue("100")).setScale(4, RoundingMode.HALF_UP);
		}
		BigDecimal percentage = getPercentage(finishValuePer, 4);
		// 单项结转生产成本=总预计结转生产成本*本期单项完工产值百分比（例如A商品，完工数量默认销售数量35，完工产值=35*40=1400，本期产值百分比=1400/3400=41.1765%，直接材料=858.42*41.1765%=353.47，直接人工=841.58*41.1765%=346.53）
		if (percentage.compareTo(BigDecimal.ZERO) != 0) {
			// 生产成本：成本合计 = 待分配金额的成本合计*辅助核算的产值百分比
			costCount = multiply(listDto.distributedAmount.costCount, percentage);
			// 生产成本：直接材料 = 待分配金额的直接材料*辅助核算的产值百分比
			directMaterials = multiply(listDto.distributedAmount.directMaterials, percentage);
			// 生产成本：直接人工 = 待分配金额的直接人工*辅助核算的产值百分比
			directLabour = multiply(listDto.distributedAmount.directLabour, percentage);
			// 生产成本：制造费用 = 待分配金额的制造费用*辅助核算的产值百分比
			manufactureFee = multiply(listDto.distributedAmount.manufactureFee, percentage);
			// 生产成本：其他小计 = 待分配金额的其他小计*辅助核算的产值百分比
			otherCount = multiply(listDto.distributedAmount.otherCount, percentage);
		}

		if (finishNumber.compareTo(BigDecimal.ZERO) != 0) {
			// 单项单位成本=单项结转成本合计/本期（月）完工数量
			unitPriceAvg = divide(costCount, finishNumber);
			// 期末结存数量=库存数量（期初数量）-本期销售数量+本期完工数量
			endNumber = initNumber.subtract(sellNumber).add(finishNumber);
			// 期末结存单价=（库存金额(期初金额)+结转成本（单项合计成本那列数据））/（库存数量+本期完工数量）
			endUnitPrice = divide(sum(initNumber, costCount), sum(initNumber, finishNumber));
		}
		// 期末结存金额=期末结存单价*期末结存数量
		endBalance = multiply(endNumber, endUnitPrice);
		// 销售毛利润=（本期销售金额-本期销售数量*成本单价）/本期销售金额
		grossProfitRate = sellBalance.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : divide(sellBalance.subtract(multiply(sellNumber, unitPriceAvg)), sellBalance);

	}

	public void setBigDecimal(){
		initNumber = initNumber.setScale(4, RoundingMode.HALF_UP);
		initUnitPrice = initUnitPrice.setScale(4, RoundingMode.HALF_UP);
		initBalance = initBalance.setScale(2, RoundingMode.HALF_UP);
		sellNumber = sellNumber.setScale(4, RoundingMode.HALF_UP);
		sellUnitPrice = sellUnitPrice.setScale(4, RoundingMode.HALF_UP);
		sellBalance = sellBalance.setScale(2, RoundingMode.HALF_UP);
		finishNumber = finishNumber.setScale(4, RoundingMode.HALF_UP);
		finishValue = finishValue.setScale(2, RoundingMode.HALF_UP);
		finishValuePer = finishValuePer.setScale(4, RoundingMode.HALF_UP);
		costCount = costCount.setScale(2, RoundingMode.HALF_UP);
		directMaterials = directMaterials.setScale(2, RoundingMode.HALF_UP);
		directLabour = directLabour.setScale(2, RoundingMode.HALF_UP);
		manufactureFee = manufactureFee.setScale(2, RoundingMode.HALF_UP);
		otherCount = otherCount.setScale(2, RoundingMode.HALF_UP);
		unitPriceAvg = unitPriceAvg.setScale(4, RoundingMode.HALF_UP);
		endNumber = endNumber.setScale(4, RoundingMode.HALF_UP);
		endUnitPrice = endUnitPrice.setScale(4, RoundingMode.HALF_UP);
		endBalance = endBalance.setScale(2, RoundingMode.HALF_UP);
		grossProfitRate = grossProfitRate.setScale(4, RoundingMode.HALF_UP);
	}
}
