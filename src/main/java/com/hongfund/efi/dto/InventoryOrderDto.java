package com.hongfund.efi.dto;

import com.hongfund.efi.domain.SubjectDimRelation;
import com.hongfund.efi.domain.VoucherRecord;
import com.hongfund.efi.utils.DBCache;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class InventoryOrderDto {

    public Long id;
    public Date gmtCreate;
    public Date gmtModified;
    /**
     * 单据号（保存用，格式：00001）
     */
    public String no;
    /**
     * 单据号（前缀）
     * 1、出库：CK + 年月日 示例：CK20240712
     * 1、入库：RK + 年月日 示例：RK20240712
     */
    public String dateNo;
    /**
     * 帐套id
     */
    public Long accountBookId;
    /**
     * 期间
     */
    public String period;
    /**
     * 凭证id
     */
    public Long voucherId;
    /**
     * 凭证号
     */
    public String voucherNo;
    /**
     * 票据id
     */
    public Long invoiceId;
    /**
     * 发票号
     */
    public String invoiceNo;
    /**
     * 存货辅助核算id
     */
    public Long dimInventoryId;
    /**
     * 单据日期
     */
    public Date orderDate;
    /**
     * 单据日期
     */
    public String orderDateStr;
    /**
     * 存货编码
     */
    public String dimInventoryNo;
    /**z
     * 存货名称
     */
    public String dimInventoryName;
    /**
     * 商品类型
     */
    public Integer dimInventoryType;
    /**
     * 商品类型字符串（新）
     */
    public String dimInventoryTypeNames;
    /**
     * 计量单位
     */
    public String measuringUnit;
    /**
     * 数量
     */
    public String number;
    /**
     * 金额
     */
    public String money;
    /**
     * 来源  1：凭证 2：票据 3：录入
     */
    public String source;
    /**
     * 类型 0:入库 1 出库
     */
    public int type;
    /**
     * 对应产品
     */
    public Long oppositeInventory;
    /**
     * 对应产品名称
     */
    public String oppositeInventoryName;
    /**
     * 单价
     */
    public String price;
    /**
     * 单据类型 1：采购入库，2：产品入库，3：销售出库，4：领料出库
     */
    public Integer receiptType;
    /**
     * 规格
     */
    public String specification;
    /**
     * 税率
     */
    public BigDecimal taxRate;
    /**
     * 税额
     */
    public BigDecimal taxMoney;

    /**
     * 供应商
     */
    public Long supplier;
    /**
     * 供应商名称
     */
    public String supplierName;
    /**
     * 对方科目
     */
    public Long oppositeSubject;
    /**
     * 对方科目名称
     */
    public String oppositeSubjectName;
    /**
     * 税金科目
     */
    public Long taxSubject;
    /**
     * 税金科目名称
     */
    public String taxSubjectName;
    /**
     * 合计金额（加税合计金额）
     */
    public BigDecimal totalAmount;
    /**
     * 存货明细
     */
    public List<InventoryOrderDetailDto> details = new ArrayList<>();

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public void setOppositeSubject(DBCache cache,  List<VoucherRecord> oppositeVrs) {
        if (oppositeVrs.size() > 0) {
            VoucherRecord v = oppositeVrs.get(0);
            oppositeSubject = v.subjectId;
            if (v.subjectDimRelationId != null){
                SubjectDimRelation relation = cache.getRelationById(v.subjectDimRelationId);
                if (relation.dimCustomerId != null) {
                    supplier = relation.dimCustomerId;
                }
                if (relation.dimDepartmentId != null) {
                    supplier = relation.dimDepartmentId;
                }
                if (relation.dimEmployeeId != null) {
                    supplier = relation.dimEmployeeId;
                }
                if (relation.dimInventoryId != null) {
                    supplier = relation.dimInventoryId;
                }
                if (relation.dimItemId != null) {
                    supplier = relation.dimItemId;
                }
                if (relation.dimProviderId != null) {
                    supplier = relation.dimProviderId;
                }
            }
        }
    }
}
