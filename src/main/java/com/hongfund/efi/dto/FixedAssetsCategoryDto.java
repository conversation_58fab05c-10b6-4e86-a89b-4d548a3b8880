package com.hongfund.efi.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.hongfund.efi.domain.Subject;
import com.hongfund.efi.profile.SubjectLongTextType;
import com.hongfund.efi.utils.DBCache;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class FixedAssetsCategoryDto {
    public Long id;
    public long accountBookId;           /*帐套id*/
    public String pno;                   /*父类别编码*/
    public String no;					 /*编码*/
    public String name;		             /*名称*/
    public Integer useMonth;			 /*使用期限（月）*/
    public BigDecimal oldValueRate;		 /*残值率*/
    public String depreciateSubjectText; /*折旧科目:no + longText(前端显示用)*/
    public String feeSubjectText;        /*费用科目:no + longText(前端显示用)*/
    public String depreciateSubject;	 /*折旧科目*/
    public String feeSubject;            /*费用科目*/
    public String depreciateLongText;	 /*折旧科目LongText*/
    public String feeLongText;           /*费用科目LongText*/
    public String method;				 /*折旧方法：1、年限平均；2、双倍余额递减；3、不折旧*/
    public Integer isInit;               /*是否是预置0:否 1：是 预置类别不可修改*/
    public Integer leaf;                 /*是否叶子类别*/
    public Integer assetType;            /*资产类型 1:固定资产；2:无形资产；3:长期待摊费用*/
    /**
     * 费用科目、成本费用科目
     */
    public FixedAssetsSubjectDto feeSubjectOBJ;
    /**
     * 折旧科目、摊销科目
     */
    public FixedAssetsSubjectDto depreciateSubjectOBJ;

    public void setSubject(DBCache cache) {
        Subject fee;
        Subject depreciate;

        // 成本费用科目 有longText优先用LongText，其次使用科目id，没有就根据资产类型设置默认值
        if (StringUtils.isNotBlank(feeLongText)) {
            fee = cache.getSubjectByLongText(feeLongText);
        }
        else if (StringUtils.isNotBlank(feeSubject)) {
            fee = cache.getSubjectById(Long.parseLong(feeSubject));
        }
        // 新增时会出现既没有longText又没有科目id的情况需要设置默认科目
        else {
            String longText = SubjectLongTextType.getSubjectLongText(cache.accountBook.accountingSystem, assetType, "费用");
            fee = cache.getSubjectByLongText(longText);
        }

        // 折旧科目
        if (StringUtils.isNotBlank(depreciateLongText)) {
            depreciate = cache.getSubjectByLongText(depreciateLongText);
        }
        else if (StringUtils.isNotBlank(depreciateSubject)) {
            depreciate = cache.getSubjectById(Long.parseLong(depreciateSubject));
        }
        // 设置默认科目
        else {
            String longText = SubjectLongTextType.getSubjectLongText(cache.accountBook.accountingSystem, assetType, "折旧");
            depreciate = cache.getSubjectByLongText(longText);
        }

        depreciateSubjectText = depreciate.no + " " + depreciate.longText;
        feeSubjectText = fee.no + " " + fee.longText;
        depreciateSubject = String.valueOf(depreciate.id);
        feeSubject = String.valueOf(fee.id);
        depreciateLongText = depreciate.longText;
        feeLongText = fee.longText;
    }
}
