package com.hongfund.efi.dto;

import com.hongfund.efi.utils.BigDecimalUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static com.hongfund.efi.profile.AccountingSystem.*;
import static com.hongfund.efi.utils.ListUtils.addList;
import static com.hongfund.efi.utils.ListUtils.subtractList;

public class IncomeStatementDto {
	
	public List<String> yearCashs = new ArrayList<>();             //本年累计金额
	public List<String> monthCashs = new ArrayList<>();            //本月金额
	public List<String> lastYearMonthCashs = new ArrayList<>();    //上年同期金额
	public List<String> monthUnLimit = new ArrayList<>();		   //本月非限定性
	public List<String> monthLimit = new ArrayList<>();			   //本月限定性
	public List<String> monthSum = new ArrayList<>();			   //本月合计
	public List<String>	yearUnLimit = new ArrayList<>();		   //本年非限定性
	public List<String>	yearLimit = new ArrayList<>();			   //本年限定性
	public List<String>	yearSum = new ArrayList<>();			   //本年合计


	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

	/**
	 * 收入--本月金额
	 */
	public BigDecimal calculateMonthIncome() {
		return BigDecimalUtils.getDecimalValue(monthCashs.get(0));
	}

	/**
	 * 支出--本月金额
	 */
	public BigDecimal calculateMonthCost(String accountingSystem) {
		return calculateYearIncome().subtract(calculateYearProfit(accountingSystem));
	}

	/**
	 * 利润--本月金额
	 */
	public BigDecimal calculateMonthProfit(String accountingSystem) {
		if (AS_2013.equals(accountingSystem)) {
			return BigDecimalUtils.getDecimalValue(monthCashs.get(29));
		}
		if (AS_2019.equals(accountingSystem)) {
			return BigDecimalUtils.getDecimalValue(monthCashs.get(21));
		}
		return BigDecimalUtils.getDecimalValue(monthCashs.get(18));
	}

	/**
	 * 收入--本年累计
	 */
	public BigDecimal calculateYearIncome() {
		return BigDecimalUtils.getDecimalValue(yearCashs.get(0));
	}

	/**
	 * 营业总收入--本年累计 营业收入+营业外收入
	 */
	public BigDecimal calculateYearTotalIncome(String accountingSystem) {
		return BigDecimalUtils.sum(BigDecimalUtils.getDecimalValue(yearCashs.get(0)), calculateYearNonOperatingIncome(accountingSystem));
	}

	/**
	 * 营业外收入
	 */
	public BigDecimal calculateYearNonOperatingIncome(String accountingSystem) {
		if (AS_2013.equals(accountingSystem)) {
			return BigDecimalUtils.getDecimalValue(yearCashs.get(21));
		}
		if (AS_2019.equals(accountingSystem)) {
			return BigDecimalUtils.getDecimalValue(yearCashs.get(18));
		}
		return BigDecimalUtils.getDecimalValue(yearCashs.get(16));
	}

	/**
	 * 支出--本年累计
	 */
	public BigDecimal calculateYearCost(String accountingSystem) {
		return calculateYearIncome().subtract(calculateYearProfit(accountingSystem));
	}

	/**
	 * 利润--本年累计
	 */
	public BigDecimal calculateYearProfit(String accountingSystem) {
		if (AS_2013.equals(accountingSystem)) {
			return BigDecimalUtils.getDecimalValue(yearCashs.get(29));
		}
		if (AS_2019.equals(accountingSystem)) {
			return BigDecimalUtils.getDecimalValue(yearCashs.get(21));
		}
		return BigDecimalUtils.getDecimalValue(yearCashs.get(18));
	}

	/**
	 * 净利润--本年累计
	 */
	public BigDecimal calculateYearNetProfit(String accountingSystem) {
		if (AS_2019.equals(accountingSystem)) {
			return BigDecimalUtils.getDecimalValue(yearCashs.get(23));
		}
		if (AS_2007.equals(accountingSystem)) {
			return BigDecimalUtils.getDecimalValue(yearCashs.get(20));
		}
		return BigDecimalUtils.getDecimalValue(yearCashs.get(yearCashs.size() - 1));
	}

	/**
	 * 营业成本--本年累计
	 */
	public BigDecimal calculateYearOperatingCost() {
		return BigDecimalUtils.getDecimalValue(yearCashs.get(1));
	}

	/**
	 * 所得税费用--本年累计
	 */
	public BigDecimal calculateYearIncomeTaxExpense(String accountingSystem) {
		if (AS_2013.equals(accountingSystem)) {
			return BigDecimalUtils.getDecimalValue(yearCashs.get(30));
		}
		if (AS_2019.equals(accountingSystem)) {
			return BigDecimalUtils.getDecimalValue(yearCashs.get(22));
		}
		return BigDecimalUtils.getDecimalValue(yearCashs.get(19));
	}

	/**
	 * 所得税费用--本月金额
	 */
	public BigDecimal calculateMonthIncomeTaxExpense(String accountingSystem) {
		if (AS_2013.equals(accountingSystem)) {
			return BigDecimalUtils.getDecimalValue(monthCashs.get(30));
		}
		if (AS_2019.equals(accountingSystem)) {
			return BigDecimalUtils.getDecimalValue(monthCashs.get(22));
		}
		return BigDecimalUtils.getDecimalValue(monthCashs.get(19));
	}

	/**
	 * 获取本年累计的数据
	 */
	public IncomeStatementDto calculateYearData() {
		IncomeStatementDto result = new IncomeStatementDto();
		result.monthCashs = yearCashs;
		result.monthLimit = yearLimit;
		result.monthUnLimit = yearUnLimit;
		result.monthSum = yearSum;
		return result;
	}

	/**
	 * 获取期初数，等于本年累计数-本月数
	 */
	public IncomeStatementDto calculateInitData(int size) {
		IncomeStatementDto result = new IncomeStatementDto();
		result.monthCashs = subtractList(yearCashs, monthCashs, size);
		result.monthLimit = subtractList(yearLimit, monthLimit, size);
		result.monthUnLimit = subtractList(yearUnLimit, monthUnLimit, size);
		result.monthSum = subtractList(yearSum, monthSum, size);
		return result;
	}

	/**
	 * 清理数据
	 */
	public void clear(int size) {
		this.monthCashs.clear();
		this.monthLimit.clear();
		this.monthUnLimit.clear();
		this.monthSum.clear();
		for (int i = 0; i < size; i++) {
			this.monthCashs.add(null);
			this.monthLimit.add(null);
			this.monthUnLimit.add(null);
			this.monthSum.add(null);
		}
	}

	/**
	 * 计算季度数据
	 */
	public static IncomeStatementDto sum(IncomeStatementDto month1, IncomeStatementDto month2, int size) {
		IncomeStatementDto result = new IncomeStatementDto();
		result.monthCashs = addList(month1.monthCashs, month2.monthCashs, size);
		result.monthLimit = addList(month1.monthLimit, month2.monthLimit, size);
		result.monthUnLimit = addList(month1.monthUnLimit, month2.monthUnLimit, size);
		result.monthSum = addList(month1.monthSum, month2.monthSum, size);
		return result;
	}

	/**
	 * 计算季度数据
	 */
	public static IncomeStatementDto sum(IncomeStatementDto month1, IncomeStatementDto month2, IncomeStatementDto month3, int size) {
		IncomeStatementDto result = new IncomeStatementDto();
		result.monthCashs = addList(month1.monthCashs, month2.monthCashs, month3.monthCashs, size);
		result.monthLimit = addList(month1.monthLimit, month2.monthLimit, month3.monthLimit, size);
		result.monthUnLimit = addList(month1.monthUnLimit, month2.monthUnLimit, month3.monthUnLimit, size);
		result.monthSum = addList(month1.monthSum, month2.monthSum, month3.monthSum, size);
		return result;
	}

}
