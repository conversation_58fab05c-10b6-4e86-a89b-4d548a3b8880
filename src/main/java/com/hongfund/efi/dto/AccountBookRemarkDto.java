package com.hongfund.efi.dto;

import java.util.Date;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

import org.apache.commons.lang3.builder.ToStringBuilder;

public class AccountBookRemarkDto {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	public Long id;
	public Long accountBookId;                 				/*账套id*/
	public Date writtenDate; 								/*备注时间*/
	public String writtenPerson; 							/*备注人*/
	public Integer type;									/*备注类型*/
	public String content;									/*备注内容*/
	public String writtenPersonNo;							/*备注人编号*/
	
	public AccountBookRemarkDto(){
		
	}
	
	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
