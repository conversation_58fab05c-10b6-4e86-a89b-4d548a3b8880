package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

public class YQDZImportDimDto {
    /**
     * "\u539f\u6750\u6599", 科目名称
     */
    public String name;
    /**
     * "9213572138142028927",  编码
     */
    public String code;
    /**
     * "", 科目全称
     */
    public String fullName;
    /**
     * "", 存货类型
     */
    public String inventoryType;
    /**
     * "Stock",  辅助类型
     * Customer	客户
     * Supplier	供应商
     * Department	部门
     * Person	员工
     * Stock	存货
     * Project	项目
     */
    public String assistantType;
    /**
     * "", 规格型号
     */
    public String specification;
    /**
     * "", 计量单位
     */
    public String unit;
    /**
     * "", 备注
     */
    public String remark;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
