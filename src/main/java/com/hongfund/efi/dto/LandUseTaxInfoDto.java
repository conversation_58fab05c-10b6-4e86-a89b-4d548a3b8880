package com.hongfund.efi.dto;

import java.math.BigDecimal;

/**
 * 房产税申报表
 */
public class LandUseTaxInfoDto {
	public Long id;
	/**
	 * 帐套id
	 */
	public Long accountBookId;
	/**
	 * 土地编号
	 */
	public String no;
	/**
	 * 宗地号
	 */
	public String landParcelNo;
	/**
	 * 土地名称
	 */
	public String name;
	/**
	 * 纳税人类型；1、土地使用权人；2、集体土地使用人；3、无偿使用人；4、代管人；5、实际使用人
	 */
	public Integer type;
	/**
	 * 土地使用权人纳税人识别号（统一社会信用代码）
	 */
	public String socialCreditCode;
	/**
	 * 土地使用权人名称
	 */
	public String useName;
	/**
	 * 不动产权证号
	 */
	public String certificateNumber;
	/**
	 * 土地性质
	 */
	public Integer landNature;
	/**
	 * 土地用途
	 * 1、商业用地
	 * 2、综合用地
	 * 3、居住用地
	 * 4、工业用地
	 * 5、房地产开发企业的开发用地
	 * 6、其他用地
	 */
	public Integer purpose;
	/**
	 * 土地取得时间
	 */
	public String date;
	/**
	 * 土地取得方式* 1：划拨、2：出让、3：租赁、4：其他、5：转让
	 */
	public Integer getWay;
	/**
	 * 占用土地面积(m²)*
	 */
	public BigDecimal floorSpace;
	/**
	 * 地坐落地址（行政区划）*
	 */
	public String city;
	/**
	 * 土地坐落地址（所处街乡）*
	 */
	public String street;
	/**
	 * 土地坐落详细地址*
	 */
	public String address;
	/**
	 * 主管税务所（科，分局）
	 */
	public String taxOffice;

}
