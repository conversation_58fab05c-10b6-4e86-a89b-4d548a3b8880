package com.hongfund.efi.dto;

import java.util.ArrayList;
import java.util.List;

public class VoucherGeneralDto {
	
	public List<BalanceRowDto> balances = new ArrayList<>();		// 余额表数据列表

	public Integer isNum = 0;												// 下拉框是否有“数量金额余额表”
	public Integer isCurr = 0;												// 下拉框是否有“外币金额余额表”
	public Integer isNumCurr = 0;											// 下拉框是否有“数量外币余额表”
	public Integer isDim = 0;												// 下拉框是否有“科目辅助余额表”
	public Integer pageType = 0;											// 页面表格的样式    1：普通    2：外币金额    3：数量金额    4：数量外币
	public int totalAccessoryNo;      										//附件数汇总
	public int totalVoucherNum;      										//凭证总数
}
