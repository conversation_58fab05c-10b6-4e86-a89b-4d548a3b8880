package com.hongfund.efi.dto;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;

import com.hongfund.efi.domain.Invoice;
import com.hongfund.efi.profile.ImportStatus;


public class InvoiceImportResponseDto {
    public Integer success = 0;
    public Integer error = 0;
    /**
     * 销项
     */
    public Map<String, List<String>> errorMap = new HashMap<>();
    /**
     * 进项
     */
    public Map<String, List<String>> errorMap2 = new HashMap<>();
    public List<Invoice> successLists = new ArrayList<>();

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public void addErrorMap(String message, String no, Integer isProviderOrCustomer) {
        if (isProviderOrCustomer == 0) {
            addErrorMap(message, no);
        }
        else {
            addErrorMap2(message, no);
        }
    }

    public void addErrorMap(String message, String no) {
        if (errorMap.containsKey(message)) {
            errorMap.get(message).add(no);
        }
        else {
            List<String> list = new ArrayList<>();
            list.add(no);
            errorMap.put(message, list);
        }
    }

    public void addErrorMap2(String message, String no) {
        if (errorMap2.containsKey(message)) {
            errorMap2.get(message).add(no);
        }
        else {
            List<String> list = new ArrayList<>();
            list.add(no);
            errorMap2.put(message, list);
        }
    }

    public String getResultStr() {
        StringBuilder result = new StringBuilder("本次成功导入" + success + "张发票。");
        if (error > 0) {
            result.append("跳过").append(error).append("张发票");
        }
        if (!errorMap.isEmpty()) {
            result.append("（销项发票：");
            result = getStringBuilder(result, errorMap);
        }
        if (!errorMap2.isEmpty()) {
            result.append("（进项发票：");
            result = getStringBuilder(result, errorMap2);
        }
        return result.toString();
    }

    private StringBuilder getStringBuilder(StringBuilder result, Map<String, List<String>> errorMap) {
        for (String message : errorMap.keySet()) {
            List<String> nos = errorMap.get(message);
            result.append(message).append("#").append(nos.toString());
        }
        result = new StringBuilder(StringUtils.removeEnd(result.toString(), ","));
        result.append("）");
        return result;
    }

    public Integer getImportStatus() {
        if (success > 0 && error == 0) {
            return ImportStatus.SUCCESS.code;
        } else if (success > 0 && error > 0) {
            return ImportStatus.PART_SUCCESS.code;
        } else if (success == 0 && error > 0) {
            return ImportStatus.FAIL.code;
        }
        return ImportStatus.NO_IMPORT.code;
    }
}
