package com.hongfund.efi.dto;

import java.math.BigDecimal;

import org.apache.commons.lang3.builder.ToStringBuilder;

public class CashFlowDetailRowDto {
       
	public Long detailId;            //明细id
	public String summary;           /* 凭证摘要 */
	public String subjectNo;         //科目编码
	public String foresubText;       //全级次名称
	public Integer flowDir;          // 1:贷方金额   -1:借方金额
	public String cashFlowItemName;  //现金流量项目名称
	public Long orderNo;             //现金流量明细序号
	public BigDecimal flowCash;      //现金流量金额
	
	public CashFlowDetailRowDto() {
	}


	public CashFlowDetailRowDto(Long detailId, String summary, String subjectNo, String foresubText, Integer flowDir,
			String cashFlowItemName, Long orderNo, BigDecimal flowCash) {
		super();
		this.detailId = detailId;
		this.summary = summary;
		this.subjectNo = subjectNo;
		this.foresubText = foresubText;
		this.flowDir = flowDir;
		this.cashFlowItemName = cashFlowItemName;
		this.orderNo = orderNo;
		this.flowCash = flowCash;
	}




	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
