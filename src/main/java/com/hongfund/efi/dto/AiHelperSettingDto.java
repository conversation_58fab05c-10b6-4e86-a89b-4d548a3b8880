package com.hongfund.efi.dto;

public class AiHelperSettingDto {
    public Long id;
    public Long accountBookId;
    /**
     * 勾选确认方式 0：人工确认（默认）,1:自动确认
     */
    public Integer tickWay = 0;
    /**
     * 发票采集方式 0：人工提交 （默认）,1:自动提交
     */
    public Integer invoiceCollectWay = 0;
    /**
     * 银行采集方式 0：人工提交 （默认）,1:自动提交
     */
    public Integer bankCollectWay = 0;
    /**
     * 增值税发票生成凭证方式 1：自动生成，0：人工生成
     */
    public Integer invoiceGenerateVoucherWay = 1;
    /**
     * 结转方式 0:人工，1：自动
     */
    public Integer carryForwardWay = 0;
    /**
     * 发票导入方式 0：人工导入:1：自动导入
     */
    public Integer invoiceImportWay = 0;
    /**
     * 复制工资方式 0：人工复制，1：自动复制
     */
    public Integer copySalaryWay = 0;
    /**
     * 工资生成标准凭证 1：自动生成:0：人工生成
     */
    public Integer salaryGenerateStandardWay = 1;
    /**
     * 结账 0：人工，1：默认
     */
    public Integer payWay = 0;
    /**
     * 个税申报方式 0：人工提交 （默认）,1:自动提交
     */
    public Integer individualTaxApplyWay = 0;
    /**
     * 生产经营申报方式 0：人工提交 （默认）,1:自动提交
     */
    public Integer productionOperationWay = 0;
    /**
     * 税局申报方式 0：人工提交 （默认）,1:自动提交
     */
    public Integer taxBureauWay = 0;
    /**
     * 税局检查方式 0：人工提交 （默认）,1:自动提交
     */
    public Integer taxBureauInspectWay = 0;
    /**
     * 个税扣款方式 0：人工提交 （默认）,1:自动提交
     */
    public Integer individualTaxPayWay = 0;
    /**
     * 生产经营扣款方式 0：人工提交 （默认）,1:自动提交
     */
    public Integer productionOperationPayWay = 0;
    /**
     * 税局扣款方式 0：人工提交 （默认）,1:自动提交
     */
    public Integer taxBureauPayWay = 0;
    /**
     * 申报方式 0：人工提交 （默认）,1:自动提交
     */
    public Integer applyWay = 0;
    /**
     * 扣款方式 0：人工提交 （默认）,1:自动提交
     */
    public Integer deductionWay = 0;
    /**
     * 记账报税启动日期 ，默认2
     */
    public Integer jzbsqdDate = 2;
    /**
     * 社保启动日期，默认2
     */
    public Integer sbqdDate = 2;
}
