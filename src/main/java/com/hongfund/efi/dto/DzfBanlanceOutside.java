package com.hongfund.efi.dto;

import java.util.ArrayList;
import java.util.List;

public class DzfBanlanceOutside {
    public boolean success;
    public int status;
    public String msg;
    public List<DzfBalanceDto> o0 = new ArrayList<>();
    public List<DzfBalanceDto> o1 = new ArrayList<>();
    public List<DzfBalanceDto> o2 = new ArrayList<>();
    public List<DzfBalanceDto> o3 = new ArrayList<>();
    public List<DzfBalanceDto> o4 = new ArrayList<>();
    public List<DzfBalanceDto> o5 = new ArrayList<>();
    public List<DzfBalanceDto> fzqc = new ArrayList<>();
}
