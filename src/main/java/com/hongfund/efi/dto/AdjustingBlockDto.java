package com.hongfund.efi.dto;

import com.hongfund.efi.utils.FormatUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
/**
 * 期末结转界面的小模块
 * <AUTHOR>
 *
 */
public class AdjustingBlockDto {

	public BigDecimal balanceNumber;						//金额（为null时前端显示问号）
	public String balance;									//金额（为null时前端显示问号）
	public String voucherNo;								//凭证号（记-003）
	public int button = 1;									//按钮（1：灰色生成凭证      2：绿色生成凭证      3：绿色查看凭证）
	public Long voucherId;									//凭证id（用来点查看按钮时传参）
	public List<AdjustingRowDto> texts = new ArrayList<>(); //显示信息
	public VoucherDto voucher = new VoucherDto();			//生成的凭证
	
	public void setBalanceString() {
		if(balanceNumber != null) {
			balance = FormatUtils.DECIMAL_FORMAT_2.format(balanceNumber);
		}
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

}
