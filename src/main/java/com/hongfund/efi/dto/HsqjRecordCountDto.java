package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 汇算清缴统计表
 */
public class HsqjRecordCountDto {
	public Long id;
	public Date gmtCreate;
	public Date gmtModified;
	public Long accountBookId;
	public String year;
	public String socialCreditCode;
	public String nationalNameFinal;
	/**
	 * 电子税务局登录方式 1:旧版登录；2：新版登录；3：代理登录
	 */
	public Integer loginWay;
	/**
	 * 开通状态
	 */
	public Integer openState;
	/**
	 * 验证状态
	 */
	public Integer authState = -1;
	/**
	 * 采集状态
	 */
	public Integer infoState = 1;
	/**
	 * 余额表上传状态
	 */
	public Integer balanceState = 1;
	/**
	 * 取数状态
	 */
	public Integer countState = 1;
	/**
	 * 申报状态
	 */
	public Integer reportState = 1;
	/**
	 * 扣款状态
	 */
	public Integer payState = 1;
	/**
	 * 检查状态
	 */
	public Integer checkState = 1;
	/**
	 * 申报税额
	 */
	public BigDecimal payBalance;

	public String bookName;
	/**
	 * 成立日期
	 */
	public String establishDate;
	/**
	 * 当前期间
	 */
	public String currentPeriod;

	public HsqjRecordCountDto() {
	}

	public HsqjRecordCountDto(Long accountBookId, Integer infoState, Integer balanceState, Integer countState, Integer reportState, Integer payState, Integer checkState, String bookName, String establishDate, String currentPeriod, BigDecimal payBalance) {
		this.accountBookId = accountBookId;
		this.infoState = infoState;
		this.countState = countState;
		this.reportState = reportState;
		this.payState = payState;
		this.checkState = checkState;
		this.bookName = bookName;
		this.establishDate = establishDate;
		this.currentPeriod = currentPeriod;
		this.balanceState = balanceState;
		this.payBalance = payBalance;
	}

	public HsqjRecordCountDto(Long accountBookId, Integer infoState, Integer balanceState, Integer countState, Integer reportState, Integer payState, Integer checkState, String bookName, String socialCreditCode, Integer loginWay, String establishDate, String currentPeriod, BigDecimal payBalance, String nationalNameFinal) {
		this.accountBookId = accountBookId;
		this.infoState = infoState;
		this.countState = countState;
		this.reportState = reportState;
		this.payState = payState;
		this.checkState = checkState;
		this.bookName = bookName;
		this.socialCreditCode = socialCreditCode;
		this.loginWay = loginWay;
		this.establishDate = establishDate;
		this.currentPeriod = currentPeriod;
		this.balanceState = balanceState;
		this.payBalance = payBalance;
		this.nationalNameFinal = nationalNameFinal;
	}

	public void setDefault() {
		if (infoState == null) {
			infoState = 1;
		}
		if (countState == null) {
			countState = 1;
		}
		if (reportState == null) {
			reportState = 1;
		}
		if (payState == null) {
			payState = 1;
		}
		if (checkState == null) {
			checkState = 1;
		}
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
