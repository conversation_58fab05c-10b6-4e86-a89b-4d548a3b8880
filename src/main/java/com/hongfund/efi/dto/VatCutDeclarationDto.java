package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.List;

/**
 * <AUTHOR>
 */
public class VatCutDeclarationDto {

	public List<VatCutDeclarationRowDto> data;
	public Integer isSave = 0;

	/**
	 * 起始期间
	 */
	public String startPeriod;
	/**
	 * 结束期间
	 */
	public String endPeriod;
	/**
	 * 发生额合计
	 */
	public String totalBalance;
	/**
	 * 实际抵减税额合计
	 */
	public String totalActualBalance;
	/**
	 * 应抵减税额合计
	 */
	public String totalCountBalance;
	/**
	 * 期初余额合计
	 */
	public String totalInitBalance;
	/**
	 * 期末余额合计
	 */
	public String totalEndBalance;

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

}