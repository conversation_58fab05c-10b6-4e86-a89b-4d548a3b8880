package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.ArrayList;
import java.util.List;

/**
 * 大账房json格式凭证：children数组下面一级对象
 */

public class DzfChildrenOneDto {
    public String status;
    public String page;
    public String rows;
    public String order;
    public String updatets;
    public String ts;
    public String subj_name;
    public String subj_code;
    public String pk_corp;
    public String direct;
    public String isCur;
    public String tmpzy;
    public String zy;
    public String bid;
    public String fx;
    public String kmid;
    public String dfmny;
    public String pid;
    public Integer rowno;
    public String jfmny;
    public String bzid;
    public String ybjfmny;
    public String ybdfmny;
    public String kmcode;
    public String kmname;
    public String fullname;
    public List<DzfChildrenFzhslistDto> fzhslist = new ArrayList<>();

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }


}
