package com.hongfund.efi.dto;


import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;

import static com.hongfund.efi.utils.BigDecimalUtils.sum;

/**
 * <AUTHOR>
 */
public class FixedAssetsRowDto {
	
	public Long id;
	/**
	 * 账套id
	 */
	public Long accountBookId;
	/**
	 * 编码
	 */
	public String code;
	/**
	 * 资产名称
	 */
    public String assetsName;
	/**
	 * 购买日期
	 */
	public String buyPeriod;
	/**
	 * 原值
	 */
	public String originalValue;
	/**
	 * 期限（月）
	 */
	public String useMonth;
	/**
	 * 残值率
	 */
	public String oldValueRate;
	/**
	 * 累计折旧
	 */
	public String totalDepreciate;
	/**
	 * 净值
	 */
	public String netValue;
	/**
	 * 月折旧额
	 */
	public String monthDepreciate;
	/**
	 * 本月折旧
	 */
	public String thisDepreciate;
	/**
	 * 费用科目id
	 */
	public String feeSubject;
	/**
	 * 折旧科目id
	 */
	public String depreciateSubject;
	/**
	 * 费用科目LongText
	 */
	public String feeLongText;
	/**
	 * 折旧科目LongText
	 */
	public String depreciateLongText;
	/**
	 * 费用科目
	 */
	public FixedAssetsSubjectDto feeSubjectOBJ;
	/**
	 * 折旧科目
	 */
	public FixedAssetsSubjectDto depreciateSubjectOBJ;
	/**
	 * 折旧方法：1、年限平均；2、双倍余额递减；3、不折旧
	 */
	public String method;
	/**
	 * 类别no
	 */
	public String fixedAssetsType;
	/**
	 * 类别名称
	 */
	public String fixedAssetsTypeName;
	/**
	 * 录入期间
	 */
	public String writePeriod;
	/**
	 * 清理期间
	 */
	public String cleanPeriod;
	/**
	 * 清理状态     0:未清理（显示清理）   1:已清理（显示还原）  2:没有清理或还原按钮
	 */
	public int   cleanStatus;
	/**
	 * 是否可以删除:0:删除，1：不可删除
	 */
	public int canDel;
	/**
	 * 当前期间
	 */
	public String endPeriod;
	/**
	 * 提示信息
	 */
	public String message;
	/**
	 * 是否修改期初累计折旧:0:未修改，1：已修改
	 */
	public int isModifyTotal;
	/**
	 * 本年累计折旧
	 */
	public String yearDepreciate;
	/**
	 * 资产类型 1:固定资产；2:无形资产；3:长期待摊费用
	 * */
	public Integer assetType;
	/**
	 * 期初剩余摊销(净值)
	 */
	public String initRemainder;
	/**
	 * 期末剩余摊销（期末净值=原值-残值-期末累计折旧）
	 */
	public String endRemainder;
	/**
	 * 期末累计摊销（期末累计折旧=期初累计折旧+本月折旧）
	 */
	public String endTotal;
	/**
	 * 资产数量
	 */
	public String number;
	/**
	 * 备注
	 */
	public String remark;
	/**
	 * 已折旧期间
	 */
	public Integer depreciatePeriod;


	public FixedAssetsRowDto() {

	}

	public FixedAssetsRowDto(Long id, Long accountBookId, String code, String assetsName, String buyPeriod, String originalValue, String useMonth, String oldValueRate, String totalDepreciate, String netValue, String monthDepreciate, String thisDepreciate, String feeSubject, String depreciateSubject, FixedAssetsSubjectDto feeSubjectOBJ, FixedAssetsSubjectDto depreciateSubjectOBJ, String method, String fixedAssetsType, String fixedAssetsTypeName, String writePeriod, String cleanPeriod, int cleanStatus, int canDel, String endPeriod, String message, int isModifyTotal, String yearDepreciate) {
		this.id = id;
		this.accountBookId = accountBookId;
		this.code = code;
		this.assetsName = assetsName;
		this.buyPeriod = buyPeriod;
		this.originalValue = originalValue;
		this.useMonth = useMonth;
		this.oldValueRate = oldValueRate;
		this.totalDepreciate = totalDepreciate;
		this.netValue = netValue;
		this.monthDepreciate = monthDepreciate;
		this.thisDepreciate = thisDepreciate;
		this.feeSubject = feeSubject;
		this.depreciateSubject = depreciateSubject;
		this.feeSubjectOBJ = feeSubjectOBJ;
		this.depreciateSubjectOBJ = depreciateSubjectOBJ;
		this.method = method;
		this.fixedAssetsType = fixedAssetsType;
		this.fixedAssetsTypeName = fixedAssetsTypeName;
		this.writePeriod = writePeriod;
		this.cleanPeriod = cleanPeriod;
		this.cleanStatus = cleanStatus;
		this.canDel = canDel;
		this.endPeriod = endPeriod;
		this.message = message;
		this.isModifyTotal = isModifyTotal;
		this.yearDepreciate = yearDepreciate;
	}

	public FixedAssetsRowDto(Long id, Long accountBookId, String code, String assetsName, String number, String buyPeriod, String originalValue, String useMonth, String oldValueRate, String totalDepreciate, String netValue, String monthDepreciate, String thisDepreciate, String yearDepreciate, String initRemainder, String endRemainder, String endTotal, FixedAssetsSubjectDto feeSubjectOBJ, FixedAssetsSubjectDto depreciateSubjectOBJ, String writePeriod, String cleanPeriod, int cleanStatus, int canDel, String endPeriod) {
		this.id = id;
		this.accountBookId = accountBookId;
		this.code = code;
		this.assetsName = assetsName;
		this.number = number;
		this.buyPeriod = buyPeriod;
		this.originalValue = originalValue;
		this.useMonth = useMonth;
		this.oldValueRate = oldValueRate;
		this.totalDepreciate = totalDepreciate;
		this.netValue = netValue;
		this.monthDepreciate = monthDepreciate;
		this.thisDepreciate = thisDepreciate;
		this.yearDepreciate = yearDepreciate;
		this.initRemainder = initRemainder;
		this.endRemainder = endRemainder;
		this.endTotal = endTotal;
		this.feeSubjectOBJ = feeSubjectOBJ;
		this.depreciateSubjectOBJ = depreciateSubjectOBJ;
		this.writePeriod = writePeriod;
		this.cleanPeriod = cleanPeriod;
		this.cleanStatus = cleanStatus;
		this.canDel = canDel;
		this.endPeriod = endPeriod;
	}

	public String getValue(String key) {
		if (StringUtils.equals(key, "originalValue")) {
			return originalValue;
		}
		if (StringUtils.equals(key, "oldValueRate")) {
			return oldValueRate;
		}
		if (StringUtils.equals(key, "totalDepreciate")) {
			return totalDepreciate;
		}
		if (StringUtils.equals(key, "netValue")) {
			return netValue;
		}
		if (StringUtils.equals(key, "monthDepreciate")) {
			return monthDepreciate;
		}
		if (StringUtils.equals(key, "thisDepreciate")) {
			return thisDepreciate;
		}
		return "0.00";
	}

	public void count(FixedAssetsRowDto fixedAssetsRowDto) {
		fixedAssetsRowDto.originalValue = sum(fixedAssetsRowDto.originalValue, originalValue);
		fixedAssetsRowDto.yearDepreciate = sum(fixedAssetsRowDto.yearDepreciate, yearDepreciate);
		fixedAssetsRowDto.totalDepreciate = sum(fixedAssetsRowDto.totalDepreciate, totalDepreciate);
		fixedAssetsRowDto.endTotal = sum(fixedAssetsRowDto.endTotal, endTotal);
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
