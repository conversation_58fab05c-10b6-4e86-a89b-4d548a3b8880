package com.hongfund.efi.dto;

import com.hongfund.efi.domain.OldImportSubject;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OldImportSubjectDto {
    /**
     * id
     */
    public Long id;
    /**
     * oldImportId
     */
    public Long oldImportId;
    /**
     * 科目编码
     */
	public String no;
    /**
     * 父科目编码
     */
    public String pno;
    /**
     * 科目名称
     */
	public String text;
    /**
     * 长名称
     */
    public String longText;
    public Date gmtCreate;//创建
    public Date gmtModified; //修改时间
    /**
     * 科目级次
     */
	public int level;
    /**
     * 是否预置
     */
	public int isInit;
    /**
     * 是否叶子节点0:否 1：是
     */
    public int leaf;
    /**
     * 计量单位（可空）
     */
    public String measuringUnit;
    /**
     * 外币符号（可空）
     */
	public String currSymbol;
    /**
     * 辅助核算（可空）
     */
	public String dimAccountType;
    /**
     * 是否数量科目
     */
    public Integer isMeasuringUnit = 0;
    /**
     * 是否外币科目
     */
    public Integer isCurrSymbol = 0;
    /**
     * 是否辅助核算科目
     */
    public Integer isDimAccountType = 0;
    /**
     * 已匹配的科目
     */
    public OldImportSubjectDto matchSubject;
    /**
     * 已匹配的科目Id
     */
    public Long  matchSubjectId;
    /**
     * 区分新旧科目
     */
    public Integer isOld = 0;

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

    public void map(OldImportSubject subject) {
        id = subject.id;
        oldImportId = subject.oldImportId;
        no = subject.no;
        pno = subject.pno;
        text = subject.text;
        longText = subject.longText;
        level = subject.level;
        isInit = subject.isInit;
        leaf = subject.leaf;
        measuringUnit = subject.measuringUnit;
        currSymbol = subject.currSymbol;
        dimAccountType = subject.dimAccountType;
        isMeasuringUnit = subject.isMeasuringUnit;
        isCurrSymbol = subject.isCurrSymbol;
        isDimAccountType = subject.isDimAccountType;
        matchSubjectId = subject.matchSubjectId;
        isOld = subject.isOld;
    }

    public static List<OldImportSubjectDto> mapList(List<OldImportSubject> subjects) {
        List<OldImportSubjectDto> result = new ArrayList<>();
        for (OldImportSubject subject : subjects) {
            OldImportSubjectDto subjectDto = new OldImportSubjectDto();
            subjectDto.map(subject);
            result.add(subjectDto);
        }
        return result;
    }
}
