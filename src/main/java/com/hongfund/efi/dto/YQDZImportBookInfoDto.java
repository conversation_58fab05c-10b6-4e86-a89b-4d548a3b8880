package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

public class YQDZImportBookInfoDto {
    /**
     * "202101" 建账年月
     */
    public String createPeriod;
    /**
     * "202112",  当前期间
     */
    public String currentPeriod;
    /**
     * "\u5c0f\u4f01\u4e1a\u4f1a\u8ba1\u51c6\u5219", 企业制度
     */
    public String systemAccount;
    /**
     * "", 备注
     */
    public String remark;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
