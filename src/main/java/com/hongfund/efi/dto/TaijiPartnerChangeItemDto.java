package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

public class TaijiPartnerChangeItemDto {
	public Long id;
	/** 统一社会信用代码*/
	public String creditNo;
	/** 变更项目*/
	public String changeItem;
	/** 变更类型（默认全部），企业名称变更，企业类型变更 ，证照号变更 ，注册资金变更 ，地址变更 ，联系方式变更 ，经营范围变更 ，负责人变更 ，股东股权变更 ，人员变更 ，分支机构变更 ，隶属关系变更 ，期限变更 ，其他变更*/
	public String type;
	/** 变更日期*/
	public String changeDate;
	/** 变更前内容*/
	public String beforeContent;
	/** 变更后内容*/
	public String afterContent;
	/** 历史信息标签（历史信息/非历史信息）*/
	public String tag;

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

}
