package com.hongfund.efi.dto;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;

public class AppUserDto {
    public Long id;
    public Date gmtCreate;// 注册时间
    public Date gmtModified;
    public String phone;
    public String password;
    public String code;// 验证码
    public String endTime;// 到期时间
    public String reference;// 推荐人
    public String referenceCompany;// 推荐人公司
    public String registerTime;// 注册时间
    public Long companyId;// 公司id
    public AppCompanyDto appCompanyDto;// 当前公司信息
    public Date  aiEndTime;//ai到期时间
    public Date manualConsultEndTime;//人工税务咨询到期时间

    public void removeSpace() {
        phone = StringUtils.trim(phone);
        password = StringUtils.trim(password);
        code = StringUtils.trim(code);
    }
}