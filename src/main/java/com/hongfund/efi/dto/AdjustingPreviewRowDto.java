package com.hongfund.efi.dto;

import com.hongfund.efi.domain.BaseBalance;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 成本结转详情
 */
public class AdjustingPreviewRowDto {
	/**
	 * 名称
	 */
	public String name;
	/**
	 * 编码
	 */
	public String no;
	/**
	 * 库存商品余额：金额
	 */
	public BigDecimal balance = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
	/**
	 * 库存商品余额：数量
	 */
	public BigDecimal number = BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);
	/**
	 * 库存商品余额：单价
	 */
	public BigDecimal unitPrice = BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);
	/**
	 * 本期结转：金额
	 */
	public BigDecimal balanceAdjusting = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
	/**
	 * 本期结转：数量
	 */
	public BigDecimal numberAdjusting = BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);
	/**
	 * 本月结转后：金额
	 */
	public BigDecimal balanceEnd = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
	/**
	 * 本月结转后：数量
	 */
	public BigDecimal numberEnd = BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);
	/**
	 * 本月结转后：单价
	 */
	public BigDecimal unitPriceEnd = BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);

	public void add(AdjustingPreviewRowDto row) {
		balance = balance.add(row.balance);
		number = number.add(row.number);
		balanceAdjusting = balanceAdjusting.add(row.balanceAdjusting);
		numberAdjusting = numberAdjusting.add(row.numberAdjusting);
		balanceEnd = balanceEnd.add(row.balanceEnd);
		numberEnd = numberEnd.add(row.numberEnd);
	}

	public void count(BaseBalance baseBalance) {
		balance = baseBalance.endbalance;
		number = baseBalance.endnumber;
		if (number.compareTo(BigDecimal.ZERO) != 0) {
			unitPrice = balance.divide(number, 4, RoundingMode.HALF_UP);
		}
	}

	public void countAdjustingByPer(BigDecimal percentage) {
		balanceAdjusting = balance.multiply(percentage).setScale(2, RoundingMode.HALF_UP);
		numberAdjusting = number.multiply(percentage).setScale(4, RoundingMode.HALF_UP);
	}

	public void countAdjustingByNumber(BigDecimal numberAdjusting) {
		this.numberAdjusting = numberAdjusting;
		balanceAdjusting = this.numberAdjusting.multiply(unitPrice);
	}

	/**
	 * 计算结转后
	 */
	public void countEnd() {
		balanceEnd = balance.subtract(balanceAdjusting);
		numberEnd = number.subtract(numberAdjusting);
		if (numberEnd.compareTo(BigDecimal.ZERO) != 0) {
			unitPriceEnd = balanceEnd.divide(numberEnd, 4, RoundingMode.HALF_UP);
		}
	}
}
