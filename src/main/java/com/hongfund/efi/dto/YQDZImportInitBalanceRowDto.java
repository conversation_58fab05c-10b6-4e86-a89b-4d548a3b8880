package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;

public class YQDZImportInitBalanceRowDto {

    /**
     * "1122", 科目编码
     */
    public String titleCode;
    /**
     * "", 外币编码（USD...）
     */
    public String fcurCode;
    /**
     * "2020", 年份
     */
    public String year;
    /**
     * "", 计量单位
     */
    public String unit;
    /**
     * "",  辅助类型
     */
    public String assistantType;

    /**
     * "", 辅助编码
     */
    public String assistantCode;
    /**
     * 原币借方期初
     */
    public BigDecimal fcurDebitOpeningBalance = BigDecimal.ZERO;
    /**
     * 借方期初
     */
    public BigDecimal debitOpeningBalance = BigDecimal.ZERO;
    /**
     * 贷方本期
     */
    public BigDecimal creditAmount = BigDecimal.ZERO;
    /**
     * 借方本期
     */
    public BigDecimal debitAmount = BigDecimal.ZERO;
    /**
     * 原币借方本期
     */
    public BigDecimal fcurDebitAmount = BigDecimal.ZERO;
    /**
     * 数量贷方期末
     */
    public BigDecimal quantityCreditEndBalance = BigDecimal.ZERO;
    /**
     * 借方期末
     */
    public BigDecimal debitEndBalance = BigDecimal.ZERO;
    /**
     * 数量贷方本期
     */
    public BigDecimal quantityCreditAmount = BigDecimal.ZERO;
    /**
     * 贷方期末
     */
    public BigDecimal creditEndBalance = BigDecimal.ZERO;
    /**
     * 原币贷方期初
     */
    public BigDecimal fcurCreditOpeningBalance = BigDecimal.ZERO;
    /**
     * 借方本年累计
     */
    public BigDecimal debitYearTotalAmount = BigDecimal.ZERO;
    /**
     * 原币借方期末
     */
    public BigDecimal fcurDebitEndBalance = BigDecimal.ZERO;
    /**
     * 数量借方期末
     */
    public BigDecimal quantityDebitEndBalance = BigDecimal.ZERO;
    /**
     * 数量借方本期
     */
    public BigDecimal quantityDebitAmount = BigDecimal.ZERO;
    /**
     * 原币贷方期末
     */
    public BigDecimal fcurCreditEndBalance = BigDecimal.ZERO;
    /**
     * 原币贷方本期
     */
    public BigDecimal fcurCreditAmount = BigDecimal.ZERO;
    /**
     * 数量借方期初
     */
    public BigDecimal quantityDebitOpeningBalance = BigDecimal.ZERO;
    /**
     * 数量借方本年累计
     */
    public BigDecimal quantityDebitYearTotalAmount = BigDecimal.ZERO;
    /**
     * 贷方期初
     */
    public BigDecimal creditOpeningBalance = BigDecimal.ZERO;
    /**
     * 贷方本年累计
     */
    public BigDecimal creditYearTotalAmount = BigDecimal.ZERO;
    /**
     * 数量贷方本年累计
     */
    public BigDecimal quantityCreditYearTotalAmount = BigDecimal.ZERO;
    /**
     * 原币借方本年累计
     */
    public BigDecimal fcurDebitYearTotalAmount = BigDecimal.ZERO;
    /**
     * 原币贷方本年累计
     */
    public BigDecimal fcurCreditYearTotalAmount = BigDecimal.ZERO;
    /**
     * 数量贷方期初
     */
    public BigDecimal quantityCreditOpeningBalance = BigDecimal.ZERO;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public boolean hasInitBalance() {
        return debitOpeningBalance.compareTo(BigDecimal.ZERO) != 0 ||
                creditAmount.compareTo(BigDecimal.ZERO) != 0 ||
                debitAmount.compareTo(BigDecimal.ZERO) != 0 ||
                fcurDebitAmount.compareTo(BigDecimal.ZERO) != 0 ||
                quantityCreditEndBalance.compareTo(BigDecimal.ZERO) != 0 ||
                debitEndBalance.compareTo(BigDecimal.ZERO) != 0 ||
                quantityCreditAmount.compareTo(BigDecimal.ZERO) != 0 ||
                creditEndBalance.compareTo(BigDecimal.ZERO) != 0 ||
                fcurCreditOpeningBalance.compareTo(BigDecimal.ZERO) != 0 ||
                debitYearTotalAmount.compareTo(BigDecimal.ZERO) != 0 ||
                fcurDebitEndBalance.compareTo(BigDecimal.ZERO) != 0 ||
                quantityDebitEndBalance.compareTo(BigDecimal.ZERO) != 0 ||
                quantityDebitAmount.compareTo(BigDecimal.ZERO) != 0 ||
                fcurCreditEndBalance.compareTo(BigDecimal.ZERO) != 0 ||
                fcurCreditAmount.compareTo(BigDecimal.ZERO) != 0 ||
                quantityDebitOpeningBalance.compareTo(BigDecimal.ZERO) != 0 ||
                quantityDebitYearTotalAmount.compareTo(BigDecimal.ZERO) != 0 ||
                creditOpeningBalance.compareTo(BigDecimal.ZERO) != 0 ||
                creditYearTotalAmount.compareTo(BigDecimal.ZERO) != 0 ||
                quantityCreditYearTotalAmount.compareTo(BigDecimal.ZERO) != 0 ||
                fcurDebitYearTotalAmount.compareTo(BigDecimal.ZERO) != 0 ||
                fcurCreditYearTotalAmount.compareTo(BigDecimal.ZERO) != 0 ||
                quantityCreditOpeningBalance.compareTo(BigDecimal.ZERO) != 0;
    }
}
