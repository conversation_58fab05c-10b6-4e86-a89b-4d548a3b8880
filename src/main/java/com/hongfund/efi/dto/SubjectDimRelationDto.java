package com.hongfund.efi.dto;

import com.hongfund.efi.utils.DBCache;

import java.util.Objects;

import static com.hongfund.efi.profile.DimAccountType.*;

/**
 * @Author: chenbin
 * @Date: 2018/12/19 14:38
 */
public class SubjectDimRelationDto {
    public Long id;
    public Long accountBookId;
    public Long dimCustomerId;
    public DimAccountDto dimCustomer;
    public Long dimDepartmentId;
    public DimAccountDto dimDepartment;
    public Long dimEmployeeId;
    public DimAccountDto dimEmployee;
    public Long dimInventoryId;
    public DimAccountDto dimInventory;
    public Long dimItemId;
    public DimAccountDto dimItem;
    public Long dimProviderId;
    public DimAccountDto dimProvider;
    public Long subjectId;
    public int dir = 1;

    public SubjectDimRelationDto() {
    }

    public SubjectDimRelationDto(Long dimCustomerId, Long dimDepartmentId, Long dimEmployeeId, Long dimInventoryId, Long dimItemId, Long dimProviderId, Long subjectId) {
        this.dimCustomerId = dimCustomerId;
        this.dimDepartmentId = dimDepartmentId;
        this.dimEmployeeId = dimEmployeeId;
        this.dimInventoryId = dimInventoryId;
        this.dimItemId = dimItemId;
        this.dimProviderId = dimProviderId;
        this.subjectId = subjectId;
    }

    public SubjectDimRelationDto(DimAccountDto dimCustomer, DimAccountDto dimDepartment, DimAccountDto dimEmployee, DimAccountDto dimInventory, DimAccountDto dimItem, DimAccountDto dimProvider) {
        this.dimCustomer = dimCustomer;
        this.dimDepartment = dimDepartment;
        this.dimEmployee = dimEmployee;
        this.dimInventory = dimInventory;
        this.dimItem = dimItem;
        this.dimProvider = dimProvider;
    }

    public void setDimAccounts(DBCache cache) {
        if (this.dimItemId != null) {
            this.dimItem = cache.getDimAccountById(this.dimItemId, ITEM);
        }
        if (this.dimCustomerId != null) {
            this.dimCustomer = cache.getDimAccountById(this.dimCustomerId, CUSTOMER);
        }
        if (this.dimProviderId != null) {
            this.dimProvider = cache.getDimAccountById(this.dimProviderId, PROVIDER);
        }
        if (this.dimDepartmentId != null) {
            this.dimDepartment = cache.getDimAccountById(this.dimDepartmentId, DEPARTMENT);
        }
        if (this.dimEmployeeId != null) {
            this.dimEmployee = cache.getDimAccountById(this.dimEmployeeId, EMPLOYEE);
        }
        if (this.dimInventoryId != null) {
            this.dimInventory = cache.getDimAccountById(this.dimInventoryId, INVENTORY);
        }
    }

    public void setAssId(Long assId, String dimAccountType) {
        switch (dimAccountType) {
            case ITEM : dimItemId = assId;break;
            case CUSTOMER : dimCustomerId = assId;break;
            case PROVIDER : dimProviderId = assId;break;
            case DEPARTMENT : dimDepartmentId = assId;break;
            case EMPLOYEE : dimEmployeeId = assId;break;
            case INVENTORY : dimInventoryId = assId;break;
            default:
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SubjectDimRelationDto that = (SubjectDimRelationDto) o;
        return Objects.equals(dimCustomerId, that.dimCustomerId) &&
                Objects.equals(dimDepartmentId, that.dimDepartmentId) &&
                Objects.equals(dimEmployeeId, that.dimEmployeeId) &&
                Objects.equals(dimInventoryId, that.dimInventoryId) &&
                Objects.equals(dimItemId, that.dimItemId) &&
                Objects.equals(dimProviderId, that.dimProviderId) &&
                Objects.equals(subjectId, that.subjectId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(dimCustomerId, dimDepartmentId, dimEmployeeId, dimInventoryId, dimItemId, dimProviderId, subjectId);
    }
}
