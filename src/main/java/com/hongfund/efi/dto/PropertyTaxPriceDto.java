package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;

import static com.hongfund.efi.utils.BigDecimalUtils.sum;

/**
 * 房产税——从价计税申报表
 */
public class PropertyTaxPriceDto {
	public Long id;
	/**
	 * 帐套id
	 */
	public Long accountBookId;
	/**
	 * 房产税id
	 */
	public Long propertyTaxId;
	/**
	 * 起始期间
	 */
	public String startPeriod;
	/**
	 * 结束期间
	 */
	public String endPeriod;
	/**
	 * 房产编号
	 */
	public String no;
	/**
	 * 房产原值
	 */
	public BigDecimal originValue;
	/**
	 * 其中：出租房产原值
	 */
	public BigDecimal rentOriginValue;
	/**
	 * 本期申报租金收入
	 */
	public BigDecimal rentIncome;
	/**
	 * 计税比例
	 */
	public BigDecimal taxationRatio;
	/**
	 * 计税依据
	 */
	public BigDecimal taxationBasis;
	/**
	 * 税目
	 */
	public String taxItem;
	/**
	 * 税率
	 */
	public BigDecimal taxRate;
	/**
	 * 本期应纳税额
	 */
	public BigDecimal currentTaxPay;
	/**
	 * 本期减免性质代码
	 */
	public String codeDeduction;
	/**
	 * 减免税额
	 */
	public BigDecimal taxDeduction;
	/**
	 * 本期已缴税额
	 */
	public BigDecimal currentTaxPaid;
	/**
	 * 本期应补（退）税额
	 */
	public BigDecimal fillRefundTaxAmount;
	/**
	 * 需要修改的字段
	 */
	public String changeTypeName;

	public PropertyTaxPriceDto() {
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

	public void count(PropertyTaxPriceDto priceDto) {
		originValue = sum(originValue, priceDto.originValue);
		rentOriginValue = sum(rentOriginValue, priceDto.rentOriginValue);
		rentIncome = sum(rentIncome, priceDto.rentIncome);
		taxationBasis = sum(taxationBasis, priceDto.taxationBasis);
		currentTaxPay = sum(currentTaxPay, priceDto.currentTaxPay);
		taxDeduction = sum(taxDeduction, priceDto.taxDeduction);
		currentTaxPaid = sum(currentTaxPaid, priceDto.currentTaxPaid);
		fillRefundTaxAmount = sum(fillRefundTaxAmount, priceDto.fillRefundTaxAmount);
	}

	public void setDecimal(){
		if (originValue == null) {
			originValue = BigDecimal.ZERO;
		}
		if (rentOriginValue == null) {
			rentOriginValue = BigDecimal.ZERO;
		}
		if (rentIncome == null) {
			rentIncome = BigDecimal.ZERO;
		}
		if (taxationRatio == null) {
			taxationRatio = BigDecimal.ZERO;
		}
		if (taxationBasis == null) {
			taxationBasis = BigDecimal.ZERO;
		}
		if (taxRate == null) {
			taxRate = BigDecimal.ZERO;
		}
		if (currentTaxPay == null) {
			currentTaxPay = BigDecimal.ZERO;
		}
		if (taxDeduction == null) {
			taxDeduction = BigDecimal.ZERO;
		}
		if (currentTaxPaid == null) {
			currentTaxPaid = BigDecimal.ZERO;
		}
		if (fillRefundTaxAmount == null) {
			fillRefundTaxAmount = BigDecimal.ZERO;
		}
	}

}
