package com.hongfund.efi.dto;

import java.math.BigDecimal;

/**
 * 房产税申报表
 */
public class PropertyTaxInfoDto {
	public Long id;
	/**
	 * 帐套id
	 */
	public Long accountBookId;
	/**
	 * 房产编号
	 */
	public String no;
	/**
	 * 产权证书号
	 */
	public String code;
	/**
	 * 房产名称
	 */
	public String name;
	/**
	 * 纳税人类型 1：产权所有人、2： 经营管理人、3：承典人、4：房屋代管人、5：房屋使用人、6：融资租赁承租人
	 */
	public Integer type;
	/**
	 * 房产用途 1：工业、2：商业及办公、3：住房、4：其他
	 */
	public Integer purpose;
	/**
	 * 房产取得时间
	 */
	public String date;
	/**
	 * 建筑面积(m²)
	 */
	public BigDecimal coveredArea;
	/**
	 * "房屋坐落地址（行政区划）"
	 */
	public String city;
	/**
	 * "房屋坐落地址（所处街道）"
	 */
	public String street;
	/**
	 * 房屋坐落详细地址
	 */
	public String address;
	/**
	 * "所属主管税务所
	 * （科、分局）"
	 */
	public String taxOffice;
	/**
	 * 房屋所在土地编号
	 */
	public String landCode;

}
