package com.hongfund.efi.dto;

import com.hongfund.efi.domain.DimInventory;

public class DimSubsidiaryLedgerDto extends SubsidiaryLedgerRowDto {
	public String subjectName;
	public String dimInventoryName;
	public String measureUnit;
	public String model;

	public void setDataByInventory(DimInventory inventory) {
		if (inventory != null) {
			dimInventoryName = inventory.name;
			measureUnit = inventory.measureUnit;
			model = inventory.model;
		}
	}
}
