package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

public class YQDZImportSubjectDto {

    /**
     * "",  上级科目编码
     */
    public String pCode;
    /**
     * "1001",  本级科目编码
     */
    public String code;
    /**
     * "\u5e93\u5b58\u73b0\u91d1", 科目名称（本级科目名称）
     */
    public String name;
    /**
     * "\u5e93\u5b58\u73b0\u91d1", 科目全称
     */
    public String fullName;
    /**
     * "Y", 是否是末级科目
     */
    public String last;
    /**
     * "",  辅助核算类型（部门、存货、、、）
     */
    public String assistantType;
    /**
     * "",  计量单位
     */
    public String unit;
    /**
     * "\u8d44\u4ea7", 科目类型（资产，负债....）
     */
    public String type;
    /**
     * 1,  科目级次
     */
    public String level;
    /**
     * "Y",  《科目是否启用》
     */
    public String status;
    /**
     * "",  外币编码(USD....)
     */
    public String fcurCode;
    /**
     * 1,  借贷方向（1借方-1贷方）
     */
    public String direction;
    /**
     * "",  备注
     */
    public String remark;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
