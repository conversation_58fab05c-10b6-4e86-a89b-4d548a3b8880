package com.hongfund.efi.dto;

import java.math.BigDecimal;

public class CountTaxRecordDto {
    /**
     * 会计编号
     */
    public String accountNo;
    /**
     * 会计名称
     */
    public String accountName;
    /**
     * 角色
     */
    public String role;
    /**
     * 客户总量
     */
    public int allNum = 0;
    /**
     * 未申报数量
     */
    public int unStartNum = 0;
    /**
     * 等待中数量
     */
    public int waitingNum = 0;
    /**
     * 已完成数量
     */
    public int finishedNum = 0;
    /**
     * 无需申报数量
     */
    public int unNeedNum = 0;
    /**
     * 异常数量
     */
    public int errorNum = 0;
    /**
     * 确认率
     */
    public BigDecimal confirmPer;
    /**
     * 成功率
     */
    public BigDecimal finishedPer;
}
