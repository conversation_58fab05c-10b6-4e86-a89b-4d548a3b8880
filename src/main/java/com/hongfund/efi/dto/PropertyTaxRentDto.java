package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;

import static com.hongfund.efi.utils.BigDecimalUtils.sum;

/**
 * 房产税——从价计税申报表
 */
public class PropertyTaxRentDto {
	public Long id;
	/**
	 * 帐套id
	 */
	public Long accountBookId;
	/**
	 * 房产税id
	 */
	public Long propertyTaxId;
	/**
	 * 起始期间
	 */
	public String startPeriod;
	/**
	 * 结束期间
	 */
	public String endPeriod;
	/**
	 * 房产编号
	 */
	public String no;
	/**
	 * 合同出租总面积（平方米）*
	 */
	public BigDecimal totalArea;
	/**
	 * 合同租金总收入*
	 */
	public BigDecimal totalIncome;
	/**
	 * 本期申报租金收入
	 */
	public BigDecimal rentIncome;
	/**
	 * 申报租金所属租赁期起*
	 */
	public String declarationStartDate;
	/**
	 * 申报租金所属租赁期止*
	 */
	public String declarationEndDate;
	/**
	 * 合同约定租赁期起*
	 */
	public String contractStartDate;
	/**
	 * 合同约定租赁期止*
	 */
	public String contractEndDate;
	/**
	 * 计税依据
	 */
	public BigDecimal taxationBasis;
	/**
	 * 税目
	 */
	public String taxItem;
	/**
	 * 税率
	 */
	public BigDecimal taxRate;
	/**
	 * 本期应纳税额
	 */
	public BigDecimal currentTaxPay;
	/**
	 * 本期减免性质代码
	 */
	public String codeDeduction;
	/**
	 * 减免税额
	 */
	public BigDecimal taxDeduction;
	/**
	 * 本期已缴税额
	 */
	public BigDecimal currentTaxPaid;
	/**
	 * 本期应补（退）税额
	 */
	public BigDecimal fillRefundTaxAmount;
	/**
	 * 需要修改的字段
	 */
	public String changeTypeName;

	public PropertyTaxRentDto() {
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

	public void count(PropertyTaxRentDto rentDto) {
		totalArea = sum(totalArea, rentDto.totalArea);
		totalIncome = sum(totalIncome, rentDto.totalIncome);
		rentIncome = sum(rentIncome, rentDto.rentIncome);
		taxationBasis = sum(taxationBasis, rentDto.taxationBasis);
		currentTaxPay = sum(currentTaxPay, rentDto.currentTaxPay);
		taxDeduction = sum(taxDeduction, rentDto.taxDeduction);
		currentTaxPaid = sum(currentTaxPaid, rentDto.currentTaxPaid);
		fillRefundTaxAmount = sum(fillRefundTaxAmount, rentDto.fillRefundTaxAmount);
	}

	public void setDecimal() {
		if (totalArea == null) {
			totalArea = BigDecimal.ZERO;
		}
		if (totalIncome == null) {
			totalIncome = BigDecimal.ZERO;
		}
		if (rentIncome == null) {
			rentIncome = BigDecimal.ZERO;
		}
		if (taxationBasis == null) {
			taxationBasis = BigDecimal.ZERO;
		}
		if (taxRate == null) {
			taxRate = BigDecimal.ZERO;
		}
		if (currentTaxPay == null) {
			currentTaxPay = BigDecimal.ZERO;
		}
		if (taxDeduction == null) {
			taxDeduction = BigDecimal.ZERO;
		}
		if (currentTaxPaid == null) {
			currentTaxPaid = BigDecimal.ZERO;
		}
		if (fillRefundTaxAmount == null) {
			fillRefundTaxAmount = BigDecimal.ZERO;
		}
	}
}
