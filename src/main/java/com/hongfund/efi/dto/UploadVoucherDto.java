package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class UploadVoucherDto {
	/**帐套id*/
	public Long accountBookId;
	/**凭证日期字符串*/
	public String voucherDateStr;
	/**凭证编号*/
	public String voucherNo;
	/**摘要*/
	public String summary;
	/**科目编码*/
	public String subjectNo;
	/**科目长名称*/
	public String longText;
	/**辅助项*/
	public String dimAccount;
	/**项目*/
	public String item;
	/**客户*/
	public String customer;
	/**供应商*/
	public String provider;
	/**部门*/
	public String department;
	/**员工*/
	public String employee;
	/**存货*/
	public String inventory;
	/**借方金额*/
	public BigDecimal jfBalance = new BigDecimal(0);
	/**贷方金额*/
	public BigDecimal dfBalance = new BigDecimal(0);
	/**数量*/
	public BigDecimal number = new BigDecimal(0);
	/**计量单位（可空）*/
	public String measuringUnit;
	/**单价*/
	public BigDecimal unitPrice = new BigDecimal(0);
	/**外币金额*/
	public BigDecimal moneyOrg = new BigDecimal(0);
	/** 外币符号（可空）*/
	public String currSymbol;
	/**汇率*/
	public BigDecimal exchangeRate = new BigDecimal(0);
	/**制单人*/
	public String writtenPerson;
	/**审核人*/
	public String auditPerson;
	/**附件数*/
	public String accessoryNo;
	/**规格*/
	public String model;

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

	public UploadVoucherDto() {
	}
}
