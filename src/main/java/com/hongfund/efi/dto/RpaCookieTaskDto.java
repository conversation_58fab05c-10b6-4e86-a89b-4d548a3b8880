package com.hongfund.efi.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.Date;

/**
 * cookie认证任务
 */
public class RpaCookieTaskDto {
	public Long id;
	public Date gmtCreate;
	public Date gmtModified;

	/**
	 * 环境
	 */
	public String env;
	/**
	 * ip地址
	 */
	public String ip;
	/**
	 * 验证码
	 */
	public String smsCode;
	/**
	 * 异步任务id
	 */
	public String taskId;
	/**
	 * 账套id
	 */
	public Long accountBookId;
	/**
	 * 账套名称
	 */
	public String accountBookName;
	/**
	 * 税号
	 */
	public String taxCode;

	/**
	 * 电子税务局登录名
	 */
	public String nationalName;
	/**
	 * 电子税局密码
	 */
	public String nationalPwd;
	/**
	 * 办税人手机号
	 */
	public String taxPayerPhone;
	/**
	 * 主管税务机关
	 */
	public String taxAuthorities;

	/**
	 * 所属省份
	 */
	public String province;
	/**
	 * 所属城市
	 */
	public String city;

	/**
	 * 区域编号
	 */
	public Integer areaId;
	/**
	 * 操作编号
	 */
	public Integer actionId;
	/**
	 * 状态
	 */
	public Integer state;
	/**
	 * 结果信息
	 */
	public String resultMessage;
	/**
	 * 电子税务局登录方式 1:旧版登录；2：新版登录；3：代理登录；
	 */
	public Integer loginWay;
	/**
	 * 代理：社会信用代码
	 */
	public String socialCreditCodeAgent;
	/**
	 * 来源 0：财务软件 1：小程序 2：app
	 */
	public Integer source = 0;
	/**
	 * 风险报告id
	 */
	public Long taxReportId;

	public RpaCookieTaskDto() {
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
