package com.hongfund.efi.dto;

import java.math.BigDecimal;

import com.hongfund.efi.utils.PeriodUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;

/**
 * <AUTHOR>
 */
public class QuarterlyCashFlowStatementDto {
    public Long id;
    /**
     * 项目名称
     */
    public String item;
    /**
     * 第几行
     */
    public Integer row;
    /**
     * 本年累计
     */
    public BigDecimal yearCash;
    /**
     * 第一季度
     */
    public BigDecimal oneQuarterCash;
    /**
     * 第二季度
     */
    public BigDecimal twoQuarterCash;
    /**
     * 第三季度
     */
    public BigDecimal threeQuarterCash;
    /**
     * 第四季度
     */
    public BigDecimal fourQuarterCash;
    /**
     * 折叠到哪一行
     */
    public Integer fold;
    /**
     * 是否有展开标记  0：没有  1：有
     */
    public Integer foldFlag;
    /**
     * 公式 科目：主营业务收入+其他业务收入
     */
    public String fomularDetail;

    public QuarterlyCashFlowStatementDto() {
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public BigDecimal getMonthCash(String period) {
        switch (PeriodUtils.getQuarterIntValue(period)) {
            case 1:
                return oneQuarterCash;
            case 2:
                return twoQuarterCash;
            case 3:
                return threeQuarterCash;
            default:
                return fourQuarterCash;
        }
    }
}
