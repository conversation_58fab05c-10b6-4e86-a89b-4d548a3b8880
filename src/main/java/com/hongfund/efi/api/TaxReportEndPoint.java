package com.hongfund.efi.api;

import com.hongfund.efi.dto.ResponseDto;
import com.hongfund.efi.dto.TaxReportBookDto;
import com.hongfund.efi.dto.TaxReportDataDto;
import com.hongfund.efi.dto.TaxReportDto;
import com.hongfund.efi.service.TaxReportService;
import com.hongfund.efi.service.exception.ServiceException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping(value = "/api/tax/report")
public class TaxReportEndPoint {

    @Autowired
    private TaxReportService taxReportService;

    @GetMapping("/info")
    public ResponseDto info(String areaId, String bookName, Integer actionId, HttpServletRequest request) {
        return taxReportService.info(areaId, bookName, actionId, request);
    }

    @PostMapping("/set/result")
    public ResponseDto setResult(@RequestBody TaxReportDto taxReportDto, HttpServletRequest request) {
        return taxReportService.setResult(taxReportDto, request);
    }

    @PostMapping("/save")
    public ResponseDto saveTaxReport(@RequestBody TaxReportDto taxReportDto) {
        return taxReportService.saveTaxReport(taxReportDto);
    }

    @PostMapping("/retry")
    public ResponseDto retryTaxReports(@RequestBody TaxReportDto taxReportDto) {
        return taxReportService.retryTaxReports(taxReportDto);
    }

    @PostMapping("/delete")
    public ResponseDto deleteTaxReports(@RequestBody TaxReportDto taxReportDto) {
        return taxReportService.deleteTaxReports(taxReportDto);
    }

    @GetMapping("/list")
    public Page<TaxReportBookDto> listTaxReports(Long accountId, String accounterNo, String query, String addtax, Integer loginWay,
                                                 Integer authState, String startPeriod, String endPeriod, Integer taxReportState, Boolean isSortByCode, Pageable pageable) {
        return taxReportService.listTaxReports(accountId, accounterNo, query, addtax, loginWay, authState, startPeriod, endPeriod, taxReportState, isSortByCode, pageable);
    }

    @PostMapping("/save/list")
    public String saveTaxReportList(String ids) {
        String[] idList = StringUtils.split(ids, ",");
        int success = 0;
        int error = 0;
        for (String idStr : idList) {
            long id = Long.parseLong(idStr);
            try {
                TaxReportDto taxReportDto = new TaxReportDto();
                taxReportDto.id = id;
                taxReportService.saveTaxReport(taxReportDto);
                success++;
            } catch (ServiceException e) {
                error++;
            }
        }
        return "批量提交任务" + success + "家，提交异常" + error + "家！";
    }

    @PostMapping("/data/save")
    public ResponseDto saveTaxReportData(@RequestBody List<TaxReportDataDto> dataList) {
        return taxReportService.saveTaxReportData(dataList);
    }

    @GetMapping("/data/find")
    public ResponseDto findTaxReportData(String taxCode, String type, String startPeriod, String endPeriod) {
        return taxReportService.findTaxReportData(taxCode, type, startPeriod, endPeriod);
    }

    @GetMapping("/data/form/year")
    public ResponseDto findHomeYear(String taxCode, String year) {
        return taxReportService.findHomeYear(taxCode, year);
    }

    @GetMapping("/data/form/quarter")
    public ResponseDto findHomeQuarter(String taxCode, String startPeriod, String endPeriod) {
        return taxReportService.findHomeQuarter(taxCode, startPeriod, endPeriod);
    }

    @GetMapping("/data/form/income")
    public ResponseDto findIncome(String taxCode, String startPeriod, String endPeriod) {
        return taxReportService.findIncome(taxCode, startPeriod, endPeriod);
    }

    @GetMapping("/data/form/balance/sheet")
    public ResponseDto findBalanceSheet(String taxCode, String startPeriod, String endPeriod, String type) {
        return taxReportService.findBalanceSheet(taxCode, startPeriod, endPeriod, type);
    }

    @GetMapping("/data/form/echarts")
    public ResponseDto findEcharts(String taxCode, String year) {
        return taxReportService.findEcharts(taxCode, year);
    }

    @GetMapping("/data/form/tax")
    public ResponseDto findTax(String taxCode, String startPeriod, String endPeriod) {
        return taxReportService.findTax(taxCode, startPeriod, endPeriod);
    }

}
