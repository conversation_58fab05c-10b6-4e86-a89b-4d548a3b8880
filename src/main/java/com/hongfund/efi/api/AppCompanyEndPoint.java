package com.hongfund.efi.api;
import com.hongfund.efi.dto.AppCompanyDto;
import com.hongfund.efi.dto.ResponseDto;
import com.hongfund.efi.service.AppCompanyService;
import com.hongfund.efi.utils.MediaTypes;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;


/**
 * 章鱼问账企业列表
 */
@RestController
@RequestMapping(value = "/api/app/company")
public class AppCompanyEndPoint {
    @Autowired
    private AppCompanyService appCompanyService;

    @ApiOperation(value="查询用户公司列表", notes="根据用户ID查询该用户的所有公司信息", httpMethod = "GET")
    @GetMapping(value = "/find/list", produces = MediaTypes.JSON_UTF_8)
    public ResponseDto findAppCompanyList(Long userId, Pageable pageable) {
        return appCompanyService.findAppCompanyList(userId, pageable);
    }

    @ApiOperation(value="查询指定公司信息", notes="根据公司ID查询指定的公司详细信息", httpMethod = "GET")
    @GetMapping(value = "/find/one", produces = MediaTypes.JSON_UTF_8)
    public ResponseDto findAppCompanyById(Long id) {
        return appCompanyService.findAppCompanyById(id);
    }

    @ApiOperation(value="保存公司信息", notes="新增或更新公司信息", httpMethod = "POST")
    @PostMapping(value = "/save", produces = MediaTypes.JSON_UTF_8)
    public ResponseDto saveAppCompany(@RequestBody AppCompanyDto appCompanyDto) {
        return appCompanyService.saveAppCompany(appCompanyDto);
    }

    @ApiOperation(value="删除公司信息", notes="根据公司ID删除指定的公司信息", httpMethod = "DELETE")
    @DeleteMapping(value = "/delete", produces = MediaTypes.JSON_UTF_8)
    public ResponseDto deleteAppCompany(Long id) {
        return appCompanyService.deleteAppCompany(id);
    }

    @ApiOperation(value="获取服务类型列表", notes="获取所有可用的服务类型及其价格信息", httpMethod = "GET")
    @GetMapping(value = "/find/service", produces = MediaTypes.JSON_UTF_8)
    public ResponseDto findAppCompanyService() {
        return appCompanyService.findAppCompanyService();
    }
}
