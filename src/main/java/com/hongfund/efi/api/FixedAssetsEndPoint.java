package com.hongfund.efi.api;


import com.hongfund.efi.dto.DetailDto;
import com.hongfund.efi.dto.FixedAssetsDto;
import com.hongfund.efi.dto.FixedAssetsRowDto;
import com.hongfund.efi.dto.MessageDto;
import com.hongfund.efi.service.FixedAssetsService;
import com.hongfund.efi.service.PermissionService;
import com.hongfund.efi.utils.MediaTypes;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;

import static com.hongfund.efi.profile.AccountType.ACCOUNT_TYPE_1;

@RestController
public class FixedAssetsEndPoint {

    @Autowired
    private FixedAssetsService fixedAssetsService;

    @Autowired
    private PermissionService permissionService;

    /**
     * 根据帐套id和期间查询固定资产
     */
    @ApiOperation(value = "固定资产查询", notes = "查询某个期间的所有固定资产信息", httpMethod = "GET")
    @RequestMapping(value = "/api/fixedAssets/{id}/find", produces = MediaTypes.JSON_UTF_8)
    public FixedAssetsDto listfixedAssetsByAccountBookIdAndPeriod(@PathVariable(value = "id") Long id, @RequestBody DetailDto detailDto, Pageable pageable) {
        return fixedAssetsService.findByAccountBookIdAndPeriod(null, id, detailDto.endPeriod, detailDto.isClean, detailDto.assetType, detailDto.fixedAssetsType, detailDto.feeSubject, detailDto.query, pageable);
    }

    /**
     * 新增/修改固定资产
     */
    @ApiOperation(value = "新增/修改固定资产", notes = "新增/修改某个期间的固定资产信息", httpMethod = "POST")
    @RequestMapping(value = "/api/fixedassets/create", method = RequestMethod.POST, consumes = MediaTypes.JSON_UTF_8)
    public FixedAssetsDto addFixedAssets(@RequestBody FixedAssetsRowDto fixedAssetsDto, HttpServletRequest request) {
        permissionService.checkFranchisee(request, Collections.singletonList(ACCOUNT_TYPE_1.typeCode), null);
        return fixedAssetsService.addFixedAssets(null, fixedAssetsDto);
    }

    /**
     * 根据固定资产id删除固定资产
     */
    @ApiOperation(value = "删除固定资产", notes = "删除选中的某个固定资产", httpMethod = "GET")
    @RequestMapping(value = "/api/fixedassets/{id}/delete")
    public void deleteFixedAssets(@PathVariable(value = "id") Long id) {
        fixedAssetsService.deleteFixedAssets(id);
    }

    /**
     * 批量删除固定资产
     */
    @ApiOperation(value = "批量删除固定资产", notes = "批量删除固定资产", httpMethod = "POST")
    @RequestMapping(value = "/api/fixedassets/delete")
    public void deleteFixedAssets(String ids) {
        String[] idList = StringUtils.split(ids, ",");
        for (String idStr : idList) {
            Long id = Long.parseLong(idStr);
            fixedAssetsService.deleteFixedAssets(id);
        }
    }

    /**
     * 还原某个固定资产
     */
    @ApiOperation(value = "还原固定资产", notes = "还原某个固定资产的信息", httpMethod = "POST")
    @RequestMapping(value = "/api/fixedassets/reduction", method = RequestMethod.POST, consumes = MediaTypes.JSON_UTF_8)
    public void reductionFixedAssets(@RequestBody FixedAssetsRowDto fixedAssetsDto) {
        fixedAssetsService.reductionFixedAssets(fixedAssetsDto);
    }

    /**
     * 根据id清理某个固定资产
     */
    @ApiOperation(value = "清理固定资产", notes = "清理某个期间的某个固定资产的信息", httpMethod = "POST")
    @RequestMapping(value = "/api/fixedassets/{id}/clean", method = RequestMethod.POST, consumes = MediaTypes.JSON_UTF_8)
    public FixedAssetsDto cleanFixedAssets(@PathVariable(value = "id") Long id, @RequestBody DetailDto detailDto) {
        return fixedAssetsService.cleanFixedAssets(id, detailDto);
    }


    /**
     * 对账
     *
     * @param id        账套id
     * @param detailDto 期间信息
     */
    @ApiOperation(value = "对账", notes = "对某个期间的固定资产对账", httpMethod = "POST")
    @RequestMapping(value = "/api/fixedassets/{id}/reconciliation", method = RequestMethod.POST, consumes = MediaTypes.JSON_UTF_8)
    public MessageDto reconciliationFixedAssets(@PathVariable(value = "id") Long id, @RequestBody DetailDto detailDto) {
        return fixedAssetsService.reconciliationFixedAssets(null, id, detailDto.endPeriod, detailDto.assetType);
    }


}
