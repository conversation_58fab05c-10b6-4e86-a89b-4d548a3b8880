package com.hongfund.efi.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.hongfund.efi.dto.AppUserDto;
import com.hongfund.efi.dto.ResponseDto;
import com.hongfund.efi.service.AppUserService;

@RestController
@RequestMapping("/api/app/user")
public class AppUserEndPoint {
    @Autowired
    AppUserService appUserService;

    @PostMapping (value = "/login")
    public ResponseDto findAppUser(@RequestBody AppUserDto appUserDto) {
        return appUserService.login(appUserDto);
    }

    @PostMapping("/register")
    public ResponseDto saveAppUser(@RequestBody AppUserDto appUserDto) {
        return appUserService.register(appUserDto);
    }

    @PostMapping("/send/code")
    public ResponseDto sendCode(@RequestBody AppUserDto appUserDto) {
        return appUserService.getVerifyCode(appUserDto.phone);
    }
    @PostMapping("/switch/company")
    public ResponseDto switchCompany(@RequestBody AppUserDto appUserDto ) {
        return appUserService.switchCompany(appUserDto.id,appUserDto.companyId);
    }
    @GetMapping("/find")
    public ResponseDto find(Long userId) {
        return appUserService.find(userId);
    }
}