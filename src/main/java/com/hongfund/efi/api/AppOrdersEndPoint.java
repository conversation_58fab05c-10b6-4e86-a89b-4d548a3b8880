package com.hongfund.efi.api;

import com.hongfund.efi.dto.AppOrdersDto;
import com.hongfund.efi.dto.ResponseDto;
import com.hongfund.efi.service.AppOrdersService;
import com.hongfund.efi.utils.MediaTypes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 订单管理
 * <AUTHOR>
 */
@Api(tags = "订单管理")
@RestController
@RequestMapping("/api/app/orders")
public class AppOrdersEndPoint {

    @Autowired
    private AppOrdersService appOrdersService;

    /**
     * 创建订单（支持APP支付和小程序支付）
     */
    @ApiOperation(value = "创建订单", notes = "创建订单，支持APP支付和小程序支付。小程序支付时需要传入payerOpenid和tradeType='JSAPI'", httpMethod = "POST")
    @RequestMapping(value = "/create", method = RequestMethod.POST, consumes = MediaTypes.JSON_UTF_8)
    public ResponseDto createOrder(@RequestBody AppOrdersDto orderDto) {
        return appOrdersService.createOrder(orderDto);
    }

    /**
     * 支付成功后回调
     */
    @ApiOperation(value = "微信支付回调", notes = "微信支付成功后回调接口，用于更新订单状态", httpMethod = "POST")
    @RequestMapping(value = "/notify", method = RequestMethod.POST, consumes = MediaTypes.JSON_UTF_8)
    public ResponseDto wxAppNotify(@RequestBody String requestBody, HttpServletRequest request) {
        return appOrdersService.wxAppNotify(requestBody, request);
    }

    /**
     * 查询订单列表
     */
    @ApiOperation(value = "查询订单列表", notes = "查询订单列表", httpMethod = "POST")
    @RequestMapping(value = "/find", method = RequestMethod.POST, consumes = MediaTypes.JSON_UTF_8)
    public ResponseDto findOrders(@RequestBody AppOrdersDto orderDto, Pageable pageable) {
        return appOrdersService.findOrders(orderDto, pageable);
    }
}
