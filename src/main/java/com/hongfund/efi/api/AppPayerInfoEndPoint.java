package com.hongfund.efi.api;

import com.hongfund.efi.dto.ResponseDto;
import com.hongfund.efi.service.AppPayerInfoService;
import com.hongfund.efi.utils.MediaTypes;
import com.hongfund.efi.utils.wxUtitls.requeset.FetchPayerInfoRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 支付者信息管理接口
 */
@Api(tags = "支付者信息管理")
@RestController
@RequestMapping("/api/app/payer")
public class AppPayerInfoEndPoint {

    @Autowired
    private AppPayerInfoService appPayerInfoService;

    /**
     * 获取支付者信息（通过微信code）
     */
    @ApiOperation(value = "获取支付者信息", 
                  notes = "通过微信授权码获取用户的支付者信息并保存到数据库", 
                  httpMethod = "POST")
    @RequestMapping(value = "/fetch", method = RequestMethod.POST, consumes = MediaTypes.JSON_UTF_8)
    public ResponseDto fetchPayerInfo(@RequestBody FetchPayerInfoRequest request) {
        return appPayerInfoService.fetchAndSavePayerInfo(request.userId, request.code);
    }

}
