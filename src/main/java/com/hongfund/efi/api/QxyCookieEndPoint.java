package com.hongfund.efi.api;

import com.hongfund.efi.dto.ResponseDto;
import com.hongfund.efi.dto.RpaCookieTaskDto;
import com.hongfund.efi.service.QxyCookieService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping(value = "/api/qxy")
public class QxyCookieEndPoint {

    @Autowired
    private QxyCookieService qxyCookieService;

    @GetMapping(value = "/check")
    public ResponseDto check(Long accountBookId, Long taxReportId, HttpServletRequest request) {
        return qxyCookieService.check(accountBookId, taxReportId, null, request);
    }

    @PostMapping(value = "/login")
    public ResponseDto login(@RequestBody RpaCookieTaskDto rpaCookieTaskDto, HttpServletRequest request) {
        return qxyCookieService.login(rpaCookieTaskDto.accountBookId, rpaCookieTaskDto.taxReportId, null, request);
    }

    @PostMapping(value = "/push/sms")
    public ResponseDto pushSms(@RequestBody RpaCookieTaskDto rpaCookieTaskDto, HttpServletRequest request) {
        return qxyCookieService.pushSms(rpaCookieTaskDto.accountBookId, rpaCookieTaskDto.smsCode, rpaCookieTaskDto.taskId, null, request);
    }

}
