package com.hongfund.efi.profile;

import java.math.BigDecimal;

/**
 * 合作商类型
 */
public enum AppServiceType {
    /*
      一：报告类。
        1）企业税务风险检测报告 9.9/份

      二：AI类
        人工智能（AI）财务咨询 99/年

      三：税务师一对一服务
        人工税务咨询999/年
     */

    /**
     * 企业税务风险检测报告
     */
    APP_SERVICE_TYPE_1(1, "企业税务风险检测报告", new BigDecimal("0.01")),
    /**
     * 人工智能（AI）财务咨询
     */
    APP_SERVICE_TYPE_2(2, "人工智能（AI）财务咨询", new BigDecimal("99")),
    /**
     * 人工税务咨询
     */
    APP_SERVICE_TYPE_3(3, "人工税务咨询", new BigDecimal("999"));


    /**
     * 类型编号
     */
    public int typeCode;
    /**
     * 商品类型编号
     */
    public String codeValue;

    /**
     * 商品价格
     */
    public BigDecimal price;

    AppServiceType(int typeCode, String codeValue, BigDecimal price) {
        this.typeCode = typeCode;
        this.codeValue = codeValue;
        this.price = price;
    }

    /**
     * 根据编号获取名称
     */
    public static String getName(int typeCode) {
        for (AppServiceType type : values()) {
            if (type.typeCode == typeCode) {
                return type.codeValue;
            }
        }
        return null;
    }
}
