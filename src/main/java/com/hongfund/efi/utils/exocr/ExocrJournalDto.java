package com.hongfund.efi.utils.exocr;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class ExocrJournalDto {
    public String error_code;
    public String description;
    public List<ExocrJournalStatementDto> statement;
    public List<ExocrJournalStatementSimpleDto> simpleStatement;

    public void parseSimple() {
        simpleStatement = new ArrayList<>();
        if (statement != null && !statement.isEmpty()) {
            for (ExocrJournalStatementDto exocrJournalStatementDto : statement) {
                ExocrJournalStatementSimpleDto temp = new ExocrJournalStatementSimpleDto();
                temp.date = exocrJournalStatementDto.date.words;
                temp.time = exocrJournalStatementDto.time.words;
                temp.amount = StringUtils.removeStart(exocrJournalStatementDto.amount.words, "+");
                temp.balance = StringUtils.removeStart(exocrJournalStatementDto.balance.words, "+");
                temp.type = exocrJournalStatementDto.type.words;
                temp.remark = exocrJournalStatementDto.remark.words;
                temp.counterparty_name = exocrJournalStatementDto.counterparty_name.words;
                temp.counterparty_account = exocrJournalStatementDto.counterparty_account.words;
                temp.counterparty_bank = exocrJournalStatementDto.counterparty_bank.words;
                temp.currency = exocrJournalStatementDto.currency.words;
                simpleStatement.add(temp);
            }
        }
        statement = null;
    }
}
