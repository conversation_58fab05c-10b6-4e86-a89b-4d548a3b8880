package com.hongfund.efi.utils.bcmUtils.request.pptm;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.hongfund.efi.utils.bcmUtils.response.pptm.B2BYQFYQFU1016ResponseV1;


public class B2BYQFYQFU1016RequestV1 extends AbstractBocomRequest<B2BYQFYQFU1016ResponseV1> {

  @Override
  public Class<B2BYQFYQFU1016ResponseV1> getResponseClass() {
    return B2BYQFYQFU1016ResponseV1.class;
  }

  @Override
  public boolean isNeedEncrypt() {
    return false;
  }

  @Override
  public String getMethod() {
    return "POST";
  }

  @Override
  public Class<? extends BizContent> getBizContentClass() {
    return B2BYQFYQFU1016RequestV1Biz.class;
  }

  public static class B2BYQFYQFU1016RequestV1Biz implements BizContent {

	/** ""*/
	@JsonProperty("pptm_head")
	private PptmHead pptmHead;

	public static class PptmHead {
     /** 报文格式*/
     @JsonProperty("struct")
     private String struct;

     /** 国家*/
     @JsonProperty("country")
     private String country;

     /** 系统号*/
     @JsonProperty("system_id")
     private String systemId;

     /** 消息类型*/
     @JsonProperty("msg_type")
     private String msgType;

     /** 发送日期*/
     @JsonProperty("send_date")
     private String sendDate;

     /** 开户机构号*/
     @JsonProperty("tx_org_no")
     private String txOrgNo;

     /** 时区*/
     @JsonProperty("time_zone")
     private String timeZone;

     /** 语言*/
     @JsonProperty("language")
     private String language;

     /** 交易流水号*/
     @JsonProperty("trans_no")
     private String transNo;

     /** 优先级*/
     @JsonProperty("priority")
     private String priority;

     /** 柜员号*/
     @JsonProperty("eteller_no")
     private String etellerNo;

     /** 发送时间*/
     @JsonProperty("send_time")
     private String sendTime;

     /** 报文有效时间*/
     @JsonProperty("duration")
     private String duration;

     /** 开户分行号*/
     @JsonProperty("tx_branch_no")
     private String txBranchNo;

     /** 预留位*/
     @JsonProperty("reserve")
     private String reserve;

     /** 渠道ID*/
     @JsonProperty("channel_id")
     private String channelId;

	public String getStruct() {
		return struct;
	}

	public void setStruct(String struct) {
		this.struct = struct;
	}
	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}
	public String getSystemId() {
		return systemId;
	}

	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}
	public String getMsgType() {
		return msgType;
	}

	public void setMsgType(String msgType) {
		this.msgType = msgType;
	}
	public String getSendDate() {
		return sendDate;
	}

	public void setSendDate(String sendDate) {
		this.sendDate = sendDate;
	}
	public String getTxOrgNo() {
		return txOrgNo;
	}

	public void setTxOrgNo(String txOrgNo) {
		this.txOrgNo = txOrgNo;
	}
	public String getTimeZone() {
		return timeZone;
	}

	public void setTimeZone(String timeZone) {
		this.timeZone = timeZone;
	}
	public String getLanguage() {
		return language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}
	public String getTransNo() {
		return transNo;
	}

	public void setTransNo(String transNo) {
		this.transNo = transNo;
	}
	public String getPriority() {
		return priority;
	}

	public void setPriority(String priority) {
		this.priority = priority;
	}
	public String getEtellerNo() {
		return etellerNo;
	}

	public void setEtellerNo(String etellerNo) {
		this.etellerNo = etellerNo;
	}
	public String getSendTime() {
		return sendTime;
	}

	public void setSendTime(String sendTime) {
		this.sendTime = sendTime;
	}
	public String getDuration() {
		return duration;
	}

	public void setDuration(String duration) {
		this.duration = duration;
	}
	public String getTxBranchNo() {
		return txBranchNo;
	}

	public void setTxBranchNo(String txBranchNo) {
		this.txBranchNo = txBranchNo;
	}
	public String getReserve() {
		return reserve;
	}

	public void setReserve(String reserve) {
		this.reserve = reserve;
	}
	public String getChannelId() {
		return channelId;
	}

	public void setChannelId(String channelId) {
		this.channelId = channelId;
	}
}	/** ""*/
	@JsonProperty("pptm_body")
	private PptmBody pptmBody;

	public static class PptmBody {
     /** 平台业务编号*/
     @JsonProperty("plfm_bsn_id")
     private String plfmBsnId;

     /** 账号*/
     @JsonProperty("acct_no")
     private String acctNo;

     /** 开始日期*/
     @JsonProperty("bgn_dt")
     private String bgnDt;

     /** 结束日期*/
     @JsonProperty("end_dt")
     private String endDt;

     /** 操作类型*/
     @JsonProperty("opr_tp")
     private String oprTp;

     /** 业务受理号*/
     @JsonProperty("bsn_acpt_no")
     private String bsnAcptNo;

     /** 偏移量*/
     @JsonProperty("offset")
     private String offset;

	public String getPlfmBsnId() {
		return plfmBsnId;
	}

	public void setPlfmBsnId(String plfmBsnId) {
		this.plfmBsnId = plfmBsnId;
	}
	public String getAcctNo() {
		return acctNo;
	}

	public void setAcctNo(String acctNo) {
		this.acctNo = acctNo;
	}
	public String getBgnDt() {
		return bgnDt;
	}

	public void setBgnDt(String bgnDt) {
		this.bgnDt = bgnDt;
	}
	public String getEndDt() {
		return endDt;
	}

	public void setEndDt(String endDt) {
		this.endDt = endDt;
	}
	public String getOprTp() {
		return oprTp;
	}

	public void setOprTp(String oprTp) {
		this.oprTp = oprTp;
	}
	public String getBsnAcptNo() {
		return bsnAcptNo;
	}

	public void setBsnAcptNo(String bsnAcptNo) {
		this.bsnAcptNo = bsnAcptNo;
	}
	public String getOffset() {
		return offset;
	}

	public void setOffset(String offset) {
		this.offset = offset;
	}
}	public PptmHead getPptmHead() {
		return pptmHead;
	}

	public void setPptmHead(PptmHead pptmHead) {
		this.pptmHead = pptmHead;
	}
	public PptmBody getPptmBody() {
		return pptmBody;
	}

	public void setPptmBody(PptmBody pptmBody) {
		this.pptmBody = pptmBody;
	}
}
}
