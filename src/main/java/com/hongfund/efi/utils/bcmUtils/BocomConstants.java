
package com.hongfund.efi.utils.bcmUtils;

public class BocomConstants {

	/**
	 * 安全域字段定义
	 */

	public static final String SIGN_SHA256RSA_ALGORITHMS = "SHA256WithRSA";

	/**
	 * 请求域字段定义
	 */

	public static final String APP_ID = "app_id";

	public static final String MSG_ID = "msg_id";

	public static final String FMT_TYPE = "fmt_type";

	public static final String IS_ENCRYPT = "is_encrypt";

	public static final String AUTH_TOKEN = "auth_token";

	public static final String CHARSET = "charset";

	public static final String TIMESTAMP = "timestamp";

	public static final String BIZ_CONTENT = "biz_content";

	public static final String FILE_HASHCODE = "file_hashcode";

	public static final String FILE_CONTENT = "file_content";

	public static final String SIGN = "sign";

	public static final String EXT_INFO = "ext_info";

	public static final String API_INDEX = "/api/";

	public static final String NONSTANDARD_INDEX = "/nonstandard/";
	/**
	 * 返回域
	 */
	public static final String RESPONSE_BIZ_CONTENT = "rsp_biz_content";
	/**
	 * 通知域
	 */
	public static final String NOTIFY_BIZ_CONTENT = "notify_biz_content";

	/**
	 * 其他参数集合
	 */
	public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

	public static final String DATE_TIMEZONE = "GMT+8";

	public static final String CHARSET_UTF8 = "UTF-8";

	public static final String CHARSET_GBK = "GBK";

	public static final String FMT_JSON = "json";

	public static final String FMT_FILE = "file";

	public static final String FMT_XML = "xml";

	public static final String VERSION_HEADER_NAME = "BOCOM-OPENAPI-VERSION";

	public static final String BIZ_STATE_S = "S";

	public static final String BIZ_STATE_F = "F";

	public static final String ENCRYPT_KEY = "encrypt_key";

	public static final String ENCRYPT_POLICY = "encrypt_policy";

	public static final String ENCRYPT_TYPE = "encrypt_type";

public static final String BEGIN_RSA_PRIVATE_KEY = "-----BEGIN RSA PRIVATE KEY-----";

    public static final String END_RSA_PRIVATE_KEY = "-----END RSA PRIVATE KEY-----";

    public static final String LINE_FEED = "\n";

    public static final String EMPTY_STRING = "";

    public static final String ENCRYPT_TYPE_RSA = "RSA";

    public static final String ENCRYPT_TYPE_AES = "AES";

    public static final String ALGORITHM = "SHA-256";

    public static final String FILE_HASH_CODE = "fileHashcode";

    public static final String COLON = ":";

    //说明文件后缀
    public static final String FILE_SUFFIX_ATTACH = ".attach";

    //加密文件后缀
    public static final String FILE_SUFFIX_ENCODE = ".encode";

    public static final String FILE_SUFFIX_TEM = ".tem";

    public static final String FILE_SUFFIX = "\\";

    public static final String FILE_SIZE = "fileSize";

    public static final String FILE_PATH_UPLOAD = "/UPLOAD/";

    public static final String FILE_PATH_DOWNLOAD = "/DOWNLOAD/";

}
