package com.hongfund.efi.utils.bcmUtils;

import com.bocom.api.DefaultBocomClient;
import com.hongfund.efi.utils.bcmUtils.request.ecrm.BocomCorpActRegisterUrlv1RequestV1;
import com.hongfund.efi.utils.bcmUtils.response.ecrm.BocomCorpActRegisterUrlv1ResponseV1;

import java.util.UUID;

/**
 * <pre>
 * 交通银行公司账户开立意愿登记URL入参v1
 * </pre>
 * <p>
 *
 * </p>
 */
public class BocomCorpActRegisterUrlv1V1Test {
	public static final String MY_PRIVATE_KEY = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDEu3hCLeQXcWmi+wXaaM0HSJkRxFb/lQl8GU69UiRmf0zI4Xh7/cPBXdm/CWS5SzzTRCQJh23/USxPuyB//wWHgkqUASj12B4mMdx+/KGFmgrAFCy4CBhE2+O4AaiKWuZ8Gpmv+NUseNhy/kyzdNrmqikffr1vM7q1emJ8Y9OihGIAB8toT6b9I/iL93yLUXNLe0ELIIJ1+mmr7j0GvAMZQKL2IrfJ4MCRqg0Ioj2Ql1ckrkmIjgMKZQ1ryzrzcllq5YeaOM8fM3gff9Ntn06N2fsbpZFnGzokNpZkBX8wsLwOHBLM25RxvMPWH9UwWBXkklNHe57PN9G7qtvMMOnPAgMBAAECggEAMeTNuhMDbwKfZOs+78Z2gStjTsM5wTYeIrAUbSVg//G3Gpc8RE32kJrpt259chTnxzjX3ubO+dqvX0U5F90onNZpbaHhrn/yHYO1xSrzF/waT96N/NpotUYDOc6J3RTysXTZl2yHO/EmC/v8neK8BZYQOeGdz48SAiaYxa1XpjZ0QAUX3rCjf9qjkTKux0TZTBk67PcIb+Gwqk4nRWhc0i3yXW4Xw8Z8UJeABgtsOZ16Nv95K7jA836sF+gf4chLGDl8J9Y7Ciz/FpJPksa6Ysoo6jMW9WkggGsNEOwTK6SfYWqIER+NozWG5F4jOlAmdc3zlteOdM3Z9kiYHdlsmQKBgQD78RpE3AEFIC6dgO5isbBeSRNZJUHOp/H49cJgwtk3DiDuN/hLh8Lw3priFiOSlioFRmv6VkQVRQJ38pCbXacc04d34joNy0zXVWFtaaZlureRz74tTbi9Dp1x123NIJ/i/jiJLBdrXfmkNaQpyzfsrIC1vrFpaA5RWKcooZrgzQKBgQDH5rUX9q6tcB/UE3zNfJsNE/yw1QFSAIzcKE3vxWifjbURdVJXpvNoXWkTivwxEbhBDpyAhtUngpN+Ac/3ZP88hY21v+AHmgq9/65z++Wbptg9JfPpGti7kA132D76OfaGca3C1kt1A4Kl+177P52Nx9uAWbANEw+02Dd147pFCwKBgAbzT9Y2e5C4opCVzf3LCSKQwYfkDzy0RWio3wPsXDG4og/wW1k6FZavjXZPRgv1OJOMrc3qM1ai4L6OYo7mtoWFEGeuRgu7YBNQt69dCsojrIzJPmXhjwcoMA/D8bOHAuIwQT8HiF3d6dUt5amBgbl1yNc6u4o85Kg52eK1EqDBAoGARmbZNpRZVpnbi8xFMvWEgemdIAYUjfXFpi/Hqv8iZrb4oypnkpkotoPXYtubEAkGlFr6AUX7Qgv5t/IZdS6ruXvruavW0baNu/aVUSHQ7q2nEHoSWfFrjTshkyiknWnWiej1Yl4oexB+bciG3gO8kFdn1d9AYoppCK7xcWJ/kpsCgYEA2se5A7Oa6JsWmpil+kRSyijoynEnC4IGVEIzboYcBxlyxfpHtwS2zOdXkhIpjd2cqg08BGFcSxot8CaxCOBcRJsI+N+3lok6VffaZDRjttr+NdY/qc7OCmVCx1lJlJ2+WL0Zitv1DV84WqUY2XpQOHvvcmGfuEXtyfkxD1rSJUE=";
	public static final String APIGW_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxSJrF8T/5rKB4NnwMjIxUer+ELf1PQXO2GSdZ/fvuQCclOR9tBlNWL4jFOftebeL+bvMVOJ+JHm/aSes1AN8YNIDGiFUpF6aDkSCaLynDdjK/mQTWhSNa2fO0GGO+ywOBTdYUjVjVtzJ48bbyG3NSylf1EdnBWnMpFa8qpXJXR4ELpVpMkPDC+93HBAlxEgUjhcIJlP5VdKIiudsmhE2T07qtpIQSuE5hntXP6X6GKJReCk+yek2QJITvIBq3cHPw8KDsHHCs7MaR81KI3onJRWyqFtTfVYTiKsd9EcYSxv+Gx5MOF8B/P4iJCD8uzx0FrqoB3k5OYGcz4tXs+h+9wIDAQAB";
	public static final String BOCOM_PRD_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAihC4Qj8mlFxlO54qhUOmwWTKqhTCrfzZIeWFqS+QH9BxiChja6Za8oYVhyP+kiRiWxffBTzT25DPUfaDiQVCnlhdqlfOHHZ2Gp291pKrmF/E4WxLk50zYE1d69osw20GY0EVxhpml5bOxumxughpKwPKCrcYtN7MXAeWUEpl7AzqPNUgV+KlmE7TxB9tWcP6jeSn4/PQ47BfYmi2LI25UXfaFrUSNITkePoIYVZnP6FVpsC2grTdnPeUgfaCB3f/fPjEwRPrCHXCMopEWQQGIvqZuaZkFaQAd5XYfQnRytnF8nPofuRCDOHZYV2ldb5fVfsne/PuWmKBnBghebcw+QIDAQAB";
	public static final String APP_ID = "appoSDHJ202212160001";
//	public static final String APIGW_URL_ADDRESS = "http://**************:8090";//内网uat
	public static final String APIGW_URL_ADDRESS = "https://***************:9443";//外网uat
	//public static final String APIGW_URL_ADDRESS = "http://127.0.0.1:9501";
	//public static final String APIGW_URL_ADDRESS = "https://open.bankcomm.com";//生产地址

		public static void main(String[] args) {

				try {
					 //DefaultBocomClient client = new DefaultBocomClient(APP_ID, new OpenBankSM2SignatureSPI(), MY_PRIVATE_KEY, APIGW_PUBLIC_KEY);
					 //DefaultBocomClient client = new DefaultBocomClient(APP_ID, new OpenBankRSAEMSignatureSPI(), APIGW_PUBLIC_KEY);
					DefaultBocomClient client = new DefaultBocomClient(APP_ID, MY_PRIVATE_KEY, APIGW_PUBLIC_KEY);
					/**
					* 测试环境可以忽略SSL证书告警，生产环境不可忽略
					*/
					client.ignoreSSLHostnameVerifier();

					BocomCorpActRegisterUrlv1RequestV1 request = new BocomCorpActRegisterUrlv1RequestV1();
					request.setServiceUrl(APIGW_URL_ADDRESS + "/api/ecrm/BocomCorpActRegisterUrlv1/v1");

					BocomCorpActRegisterUrlv1RequestV1.BocomCorpActRegisterUrlv1RequestV1Biz bizContent = new BocomCorpActRegisterUrlv1RequestV1.BocomCorpActRegisterUrlv1RequestV1Biz();
					bizContent.setOpenwebSourcePlatform("0002");
					bizContent.setOpenwebBranchCode("***********");
					bizContent.setOpenwebProductType("0002");
					bizContent.setOpenwebCorpName("上海卓顾信息科技有限公司");
					bizContent.setOpenwebCreditNo("913101165964435306");
					bizContent.setOpenwebAddr("上海市金山区金山卫镇秋实路688号1号楼3单元235室C座");
					bizContent.setOpenwebUserName("陈斌");
					bizContent.setOpenwebMobilePhoneNum("13912497291");
//					bizContent.setOpenwebTokenId("token(合肥高新园区特色)");
					bizContent.setOpenwebTerminalType("1");
//					bizContent.setOpenwebTraceNo("流水号");
//					bizContent.setOpenwebExt1("备用字段");
//					bizContent.setOpenwebExt2("备用字段");
//					bizContent.setOpenwebExt3("备用字段");
//					bizContent.setOpenwebExt4("备用字段");
//					bizContent.setOpenwebExt5("备用字段");
					request.setBizContent(bizContent);
					BocomCorpActRegisterUrlv1ResponseV1 response = client.execute(request,UUID.randomUUID().toString().replace("-", ""));
					if (response.isSuccess()) {
						System.out.println("success");
						System.out.println(response.toString());
					} else {
						System.out.println(response.getRspCode());
						System.out.println(response.getRspMsg());
					}
				} catch (Exception xcp) {
						xcp.printStackTrace();
			}
	}
}
