package com.hongfund.efi.utils.bcmUtils.request;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.hongfund.efi.utils.bcmUtils.response.ReplaceAuthResponseV1;


public class ReplaceAuthRequestV1 extends AbstractBocomRequest<ReplaceAuthResponseV1> {

    @Override
    public Class<ReplaceAuthResponseV1> getResponseClass() {
        return ReplaceAuthResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return ReplaceAuthRequestV1Biz.class;
    }

    public static class ReplaceAuthRequestV1Biz implements BizContent {

        /**
         * pkcs1格式公钥
         */
        @JsonProperty("public_key")
        private String publicKey;

        public String getPublicKey() {
            return publicKey;
        }

        public void setPublicKey(String publicKey) {
            this.publicKey = publicKey;
        }
    }
}
