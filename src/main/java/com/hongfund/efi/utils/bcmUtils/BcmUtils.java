package com.hongfund.efi.utils.bcmUtils;

import com.bocom.api.BocomConstants;
import com.bocom.api.DefaultBocomClient;
import com.bocom.api.security.keygen.RSAKeyGen;
import com.bocom.api.utils.Base64;
import com.bocom.api.utils.EncryptUtils;
import com.hongfund.efi.service.exception.ErrorCode;
import com.hongfund.efi.service.exception.ServiceException;
import com.hongfund.efi.utils.OSSUtils;
import com.hongfund.efi.utils.bcmUtils.domain.BankAccountReceiptBcm;
import com.hongfund.efi.utils.bcmUtils.request.ReplaceAuthRequestV1;
import com.hongfund.efi.utils.bcmUtils.request.ecrm.BocomCorpActRegisterUrlv1RequestV1;
import com.hongfund.efi.utils.bcmUtils.request.ppas.PPAS03001RequestV1;
import com.hongfund.efi.utils.bcmUtils.request.ppas.PPAS03002RequestV1;
import com.hongfund.efi.utils.bcmUtils.request.pptm.B2BYQFYQFU1016RequestV1;
import com.hongfund.efi.utils.bcmUtils.response.ReplaceAuthResponseV1;
import com.hongfund.efi.utils.bcmUtils.response.ecrm.BocomCorpActRegisterUrlv1ResponseV1;
import com.hongfund.efi.utils.bcmUtils.response.ppas.PPAS03001ResponseV1;
import com.hongfund.efi.utils.bcmUtils.response.ppas.PPAS03002ResponseV1;
import com.hongfund.efi.utils.bcmUtils.response.pptm.B2BYQFYQFU1016ResponseV1;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import static com.hongfund.efi.utils.ZipUtils.getByte;
import static com.hongfund.efi.utils.icbcUtils.IcbcUtils.BASE_FILE_URL;

/**
 * 交通银行银企付接口工具
 */
public class BcmUtils {
    private static final Logger logger = LoggerFactory.getLogger(BcmUtils.class);
    public static final int SFTP_PORT = 2222;

//    测试
//    public static final String MY_PRIVATE_KEY = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDEu3hCLeQXcWmi+wXaaM0HSJkRxFb/lQl8GU69UiRmf0zI4Xh7/cPBXdm/CWS5SzzTRCQJh23/USxPuyB//wWHgkqUASj12B4mMdx+/KGFmgrAFCy4CBhE2+O4AaiKWuZ8Gpmv+NUseNhy/kyzdNrmqikffr1vM7q1emJ8Y9OihGIAB8toT6b9I/iL93yLUXNLe0ELIIJ1+mmr7j0GvAMZQKL2IrfJ4MCRqg0Ioj2Ql1ckrkmIjgMKZQ1ryzrzcllq5YeaOM8fM3gff9Ntn06N2fsbpZFnGzokNpZkBX8wsLwOHBLM25RxvMPWH9UwWBXkklNHe57PN9G7qtvMMOnPAgMBAAECggEAMeTNuhMDbwKfZOs+78Z2gStjTsM5wTYeIrAUbSVg//G3Gpc8RE32kJrpt259chTnxzjX3ubO+dqvX0U5F90onNZpbaHhrn/yHYO1xSrzF/waT96N/NpotUYDOc6J3RTysXTZl2yHO/EmC/v8neK8BZYQOeGdz48SAiaYxa1XpjZ0QAUX3rCjf9qjkTKux0TZTBk67PcIb+Gwqk4nRWhc0i3yXW4Xw8Z8UJeABgtsOZ16Nv95K7jA836sF+gf4chLGDl8J9Y7Ciz/FpJPksa6Ysoo6jMW9WkggGsNEOwTK6SfYWqIER+NozWG5F4jOlAmdc3zlteOdM3Z9kiYHdlsmQKBgQD78RpE3AEFIC6dgO5isbBeSRNZJUHOp/H49cJgwtk3DiDuN/hLh8Lw3priFiOSlioFRmv6VkQVRQJ38pCbXacc04d34joNy0zXVWFtaaZlureRz74tTbi9Dp1x123NIJ/i/jiJLBdrXfmkNaQpyzfsrIC1vrFpaA5RWKcooZrgzQKBgQDH5rUX9q6tcB/UE3zNfJsNE/yw1QFSAIzcKE3vxWifjbURdVJXpvNoXWkTivwxEbhBDpyAhtUngpN+Ac/3ZP88hY21v+AHmgq9/65z++Wbptg9JfPpGti7kA132D76OfaGca3C1kt1A4Kl+177P52Nx9uAWbANEw+02Dd147pFCwKBgAbzT9Y2e5C4opCVzf3LCSKQwYfkDzy0RWio3wPsXDG4og/wW1k6FZavjXZPRgv1OJOMrc3qM1ai4L6OYo7mtoWFEGeuRgu7YBNQt69dCsojrIzJPmXhjwcoMA/D8bOHAuIwQT8HiF3d6dUt5amBgbl1yNc6u4o85Kg52eK1EqDBAoGARmbZNpRZVpnbi8xFMvWEgemdIAYUjfXFpi/Hqv8iZrb4oypnkpkotoPXYtubEAkGlFr6AUX7Qgv5t/IZdS6ruXvruavW0baNu/aVUSHQ7q2nEHoSWfFrjTshkyiknWnWiej1Yl4oexB+bciG3gO8kFdn1d9AYoppCK7xcWJ/kpsCgYEA2se5A7Oa6JsWmpil+kRSyijoynEnC4IGVEIzboYcBxlyxfpHtwS2zOdXkhIpjd2cqg08BGFcSxot8CaxCOBcRJsI+N+3lok6VffaZDRjttr+NdY/qc7OCmVCx1lJlJ2+WL0Zitv1DV84WqUY2XpQOHvvcmGfuEXtyfkxD1rSJUE=";
//    public static final String APIGW_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxSJrF8T/5rKB4NnwMjIxUer+ELf1PQXO2GSdZ/fvuQCclOR9tBlNWL4jFOftebeL+bvMVOJ+JHm/aSes1AN8YNIDGiFUpF6aDkSCaLynDdjK/mQTWhSNa2fO0GGO+ywOBTdYUjVjVtzJ48bbyG3NSylf1EdnBWnMpFa8qpXJXR4ELpVpMkPDC+93HBAlxEgUjhcIJlP5VdKIiudsmhE2T07qtpIQSuE5hntXP6X6GKJReCk+yek2QJITvIBq3cHPw8KDsHHCs7MaR81KI3onJRWyqFtTfVYTiKsd9EcYSxv+Gx5MOF8B/P4iJCD8uzx0FrqoB3k5OYGcz4tXs+h+9wIDAQAB";
//    public static final String MY_SFTP_PRIVATE_KEY = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCO4uBBrAaNyMnF6wkTIjUep9LAk3HNd/24fPA7MG/inGzJXD58zK55wP3LNF/t7AqN8s/Pfovtieqqxd00yZI9k00GyT4nEytun6FioAT4xUwyeXpgyqzUKxaWzJf5OgSgmllH0zg/SZVuOUF0OZJwAU4NA0sgCud6mQQSDT+bIyiN6tHMtvipUlWTpQikT5cHjqzfCcCxDgqHuMnnJQAg+7D39FGQT4uAfiR8VYOr12aRxKmDpyr+35mU9PhS8d29jpwMA34i4/H6BMiYHcAKXd39Gbq7IRqd5FM3h14OMEA14S61a7LIwk16YDY5pKSmdGgBbEtGe6rXdYNinTUZAgMBAAECggEAVpTjjufOkNu6aFOjjBSOW6UV79toejjbZY9Pgo43PfnLcbiTpMvibaSkJR33gxQk0R7xDaqA389Sg2iiQfpiwy0e353SK5tSFhZklH6ihJwJMAysOzqUv3vclEQlUr/1Tdw5fWxgCPltsvy+zM6WDYi84uW8AA6bdY6DCO0+PaKIJ9zgkzyS+/kTQ35uPpnc1+e6dVoGMYM1StLzkG5i5Rpj5NmBVU86QNVtSAZfZ7fR6GBtXCmEhNN1rxcm/Ld/bw2hecb5YNlW0J35VoBCyg3Js73/cHf79fpk8yUGqjR87Ru5SX+LqOaN3AUh9T95tQHIxtqmyLEJzMin9ZBygQKBgQDKafYqOk5w8yKKqpTEzpTKMx0gXiKgOqySIHGY3LrRf/k3Gi7rYgYz+Sc/0u8Ny5YVijSqPU3uSXz3a57ks6TAozb03U1HITXHIh0odaYwo4tLr9WVFdbYoXYYF5gx0WC7glk0ZVBYw7OjGKxw5X6Qhh7XYAqPKXsRqdy3d5qirwKBgQC0tpeQxtK6F+NebS2mQIh+8h6A+GqPKcnOiksfEnKD58hzrsE7y/zqgassQlZSgqiAD7IZs4hECtbOs+CO2k7+vJrLpa8T4zxaBL2veVq5NghI2rpuXwCIpuJm+enZa0GMUtSst0uxOVYZjYTANTJb/OHabMHGnL7hZFmKH942twKBgQC8ng0DwJ2D5y0S5m4HDzHbUdIcbVv6z3IsIQt6FIYJ3yTOUhnSwd9h4xTOBrM4ubO7krQMx1+tYtGOhbzlBPnLgMwfVXFDjRDd5U4ie69Ht75/4vQ2Hsm7yImRXbs3ARMQ6CBUEOzUKllk7AMUmOZdqn1m2Vo9mbvS8+R3Gn8AhQKBgAxxunQ0zNQVywl4mTkmnn+naqRU9vmO1h/L9CpamdotfudAdHwKqwr3DPOKSel6p+HRsuEENPECkhI/3snXjLMMB9jYOvsynbhRNUK9J5szdlSWA1qQXjgKzfJraUgYk7X3CkAuix7UJPKzhR27TBF7k434CUwFBTv+LhBN6353AoGAOioKoFsMDGPmcEqi2nQJcrjdKyfAj8ej8ZUiovL2IZ0sCA2sNcAikEqljdU5ntFSR8Rm6+Oh6XxrFMiYgpZ3eQCP4pEVch8bnFbDt+q1mSw8TxAVhEHBBYdva82JIAOA9/w8NJ6cMhHkA5JgRrzRjz2FN06oCFd9DImRnCdh5hc=";
//    public static final String APP_ID = "appoyinqfone202211090002";
//    public static final String PLFM_BSN_ID = "************";
//    public static final String SFTP_HOST = "open.test.bankcomm.com";
//    public static final String APIGW_URL_ADDRESS = "https://***************:9443";//外网uat
    // public static final String APIGW_URL_ADDRESS = "http://**************:8090";//内网uat
    // public static final String APIGW_URL_ADDRESS = "http://127.0.0.1:9501";

//    生产
    public static final String MY_PRIVATE_KEY = "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDJ551fB3mClpqGyvn0RdMB8/NqXixnskocETsZRAqk5KSmyx8OXGXpu4eQT+P3Vj7MqmQ8RlwbqX2QXBNpMxzDKET+sD8XxUm6C6/rSos63OMhCz8xOUjzL5eGMP8Tf58n7zHUknaGOEhdkcb4c1keqek5hEoQO+zoPP/72wpMUEJ3luUj1w4dgr4lG/79isrDyko9l+kfpViRyfOEQYVhfkivWLdmHRcHIzCrALPHPtc2WrIuMzw/6ljxmc7VdTZkutl23YEpJ34cqAQrPVIFiw9pjLEkNWOwNXMLoTkcGbQTH6tChwA0tDmIaJW+d+yUrHjQA3Sf4sEPUQJTq4XdAgMBAAECggEAZ4Rh0jQh34tZxNxSlSQrkGfEGHgaKjxl9Mf81fJlxq8XoBCqZpGHCvRECAB97/T/E64U4DZuyRv4LciXhytWhtShszcn0gcrysBtPKSmh9JyfSGoD5PATDbNaFa8oQGg1zezA7roG97Fc3vnWMDDI46X7g5ebr5as6N612UxfXgLhSb8wBVp4rv9BITv6n73CjWswSqMKUK6RAQCjyvQADTqIxtcXfoEkERlOp+OMCU3VmiVW+lfUNqXK387NNnWdXCw0jnuoEUsWTwehi8AHVuF/MNX+0XGK2gHB5742giiBcf31/W81VYgmkGKElJt+RRX8NAB045ccqNrd6xGYQKBgQDuoIFO1yH81Epkhjpo/hA1vJRH8A0n4eoiUFTZE2T0H66XKyoQCrYIFGpfkqo+gogFP9X1bVK5tryA0tB8donuoy2c7zQEPux+zbjKQD5c3cTZCVPXym8ejGwPLBb7hx61189bg2CLQqUbubsYvseH3MgHz2GTknwqjaI6fTOHpQKBgQDYmq+h/nX0FPqUHTObWccriWF1m3rxfCc0aAGub2F2ELtDxlFDLqWNLLGg4g4mN8JSJwkneV5owIXdQTKT+c8x6SkrOmyhQ0fgQi9DpwonsDMbB5zXIvOJqX7YIaf3MQ5QYd1yBjjGJ2vr+D6xYRWPvtx07MsXkX6Sak8n7ulv2QKBgDlOrJ89hTkN1qvbnI14bnYpSscX/GDWnX8OIcJcKQOD1E8Tm69skxjPplg7hc0rVfVExmKBlbtqCk05074ATdHUuHCW9bwLTIqLZ1STJybwT4T7+3m4cQU6Hf1qzj4rzhFHudG1dGsdXz+QVEF2F78mI5fUkw94v38MsqgvRz/NAoGAZuS0YeOa7KO6lPl6NmXjcw8DuPZu5J/xj7eIdPB7/7Xa/0sJ9zwFcJa2mpJH3mfGa3gXpSTGcERkUsJR4HFet3rnD53lVIW9cw59nUmQTWIqJMDtBFWaEt7tXoY+X/JxtkWHCE9kUN0RKzr1rRVNRMeJuhFyOHPpCGxvxax8P6ECgYAfsvAdoxZ3L6TVHzzBFKNpWGmrPyJv9c1P26G/qgwiSb8+3W+6luu99G1M/9vOEqvOev1FgfyD/Gncujrd0/eykcwglsGO7Q77loaPrRLfOwiu7QnbZUPZhEZuKdupmP+/OBajXqeEWmsnVfM6BFj488eodmqJUAVAISF6RMFHVQ==";
    public static final String APIGW_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAihC4Qj8mlFxlO54qhUOmwWTKqhTCrfzZIeWFqS+QH9BxiChja6Za8oYVhyP+kiRiWxffBTzT25DPUfaDiQVCnlhdqlfOHHZ2Gp291pKrmF/E4WxLk50zYE1d69osw20GY0EVxhpml5bOxumxughpKwPKCrcYtN7MXAeWUEpl7AzqPNUgV+KlmE7TxB9tWcP6jeSn4/PQ47BfYmi2LI25UXfaFrUSNITkePoIYVZnP6FVpsC2grTdnPeUgfaCB3f/fPjEwRPrCHXCMopEWQQGIvqZuaZkFaQAd5XYfQnRytnF8nPofuRCDOHZYV2ldb5fVfsne/PuWmKBnBghebcw+QIDAQAB";
    public static final String MY_SFTP_PRIVATE_KEY = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDA7ZUXpaQZ/VKyAoC3tVNmE2wkh44fSsTu/NzYW3wWZmdyhGU163m+C/wWTkglNQbeWdHyouCn9FLgECiaN9L/IfLmSd1DTIRYJYgTv7nYGxFi2kphh8ECyg/Ozayo40rUJUd3pWyU4izMM+rnP1i1pp9m+O0H9VBfbg5Wye44vkQBF1Re5LkfroL6EHexeHefGZx8kp2rwMSZh5JbGQJ1Kw44i/ZS3s8usaxlG3bS/3k7K6hNGi/bX7fXNLwOdsL7LZoJSJWFnx8EAJJVirEFwbpbj8FWvR5A7bJlDC/L8Y2hNrt4cJO/vm85uR9BdZuDSq/jI70XK4iQ2MEz7OSXAgMBAAECggEBALHWjvdURbd7Ts4uBm7RI4ZVYvstRTHNH0lZJ038zp5rWPIz3oPcgsVPkNkS4egCQEmYLDJoN4Q6cGaAHp1B1io+JgRnlCEkGF6UgiIKOopdMSKZK7VJiP5EOlwcNQsdMW3qyz0b7MI01qyJMwBk4SXY6Njnnr8WPzcoYOlZjFEaXZi+l5lKVPzwIwTMSdgGwyhlzk0zqjpqf9QjswPVYnbqecpTORDxcSXWcuHu98ampInArkorVtgGSMYw+3ZCtNMTiFG44/2/JEqi9/WBLDprqEC6EpO03GrQrWgbab2dLVWabuhYKqVmAEfNcH7+qsCImDVJ+wU0QSYUmab1YuECgYEA+ICTAMtEtU36fc++oetiPRI5+75XfwPPwTCGF80ibp1UKRcfQhmo3MrF1AvLyv+iEi5hqa6nn9A4uyenWUtHZdpBKGK9nSjPnJPBhsoilJgyJcl6+zqoaXQoxhCILcTmYCAhulaR5DjscsN/y11Si8GGqoqyZrF3LRp5vcG/VYcCgYEAxr/BH1b8F+idly1737sb2Nellylo0Q5YXB0s29k1kZmiqK52L6k0g54ESxGQe52auYABWceXoiuvIWpkDJFd2sRdFCZ0gOAYp75LQ5BrVBuoANUK/frK2REkpFrRWpxqopDRQDo6PWirw5fxqv+LmdbqDxwHt57i1HiSUMFPvHECgYBbxM+EuYwUEEny8CcMEKrktrhm+THCnBu60jqSy5RtmPgdfIZ0mJk9gDxyRDDsNF8bq7kxOmh4oiJxKwGn/h7NxD3PtjcjtJCG2CHdrwP6MAJE5hTedhj/aIAuNKi1jkMtOHB8KcASsdrPvtaQuVPkfIFItZOQTPUf2OkgZE5OPQKBgQCmycjx2feYwNdvRPZNnSITaXVHU7LordctBjbdEH1KANpmIHKCMMcnJPaJYOYVNv/faDK5+yufJnXD3xqzCVLwAhXykQr4FBfcvW8/9fR8EvF8MU0WMt8EJsTPQH+sQNOkNU/FTJgPbMWoUNy4Gdg3dAncQDOhvq6wi7aOGowloQKBgDAFoQKAM1D7DJjquUuW1yq8jbdLPVoKLHS0fzqun6r8opR20vRZVCrlgnPtTadK9j8UHgVkNKk4RLKEsMdVqdNvJ56X36XefgcelO9Ie4XzMPr9/PiB4ZHahZBHEtn3p0PKfgBOSHhDy+wV0SyOS10aiOjBl0HBRSNwwGD9w2lY";
    public static final String APP_ID = "appoZGJZS202301180001";
    public static final String PLFM_BSN_ID = "**********";
    public static final String SFTP_HOST = "open.bankcomm.cn";
    public static final String APIGW_URL_ADDRESS = "https://open.bankcomm.com";//生产地址


    /**
     * 签约申请
     */
    public static void addAgree(String accountNo, String accountName) {
        try {
            //DefaultBocomClient client = new DefaultBocomClient(APP_ID, new InfoSecRSASignatureSPI(), MY_PRIVATE_KEY, APIGW_PUBLIC_KEY);
            //DefaultBocomClient client = new DefaultBocomClient(APP_ID, new InfoSecRSASignatureSPI(), APIGW_PUBLIC_KEY);
            DefaultBocomClient client = new DefaultBocomClient(APP_ID, MY_PRIVATE_KEY, APIGW_PUBLIC_KEY);
            // 测试环境可以忽略SSL证书告警，生产环境不可忽略
            client.ignoreSSLHostnameVerifier();

            PPAS03001RequestV1 request = new PPAS03001RequestV1();
            request.setServiceUrl(APIGW_URL_ADDRESS + "/api/ppas/PPAS03001/v1");

            PPAS03001RequestV1.PPAS03001RequestV1Biz bizContent = new PPAS03001RequestV1.PPAS03001RequestV1Biz();
            PPAS03001RequestV1.PPAS03001RequestV1Biz.PpasHead PPAS_HEAD = new PPAS03001RequestV1.PPAS03001RequestV1Biz.PpasHead();
            bizContent.setPpasHead(PPAS_HEAD);
            PPAS_HEAD.setStruct("");
            PPAS_HEAD.setCountry("");
            PPAS_HEAD.setSystemId("");
            PPAS_HEAD.setSendDate("");
            PPAS_HEAD.setTimeZone("");
            PPAS_HEAD.setLanguage("");
            PPAS_HEAD.setPriority("");
            PPAS_HEAD.setEtellerNo("");
            PPAS_HEAD.setOrgId("");
            PPAS_HEAD.setSendTime("");
            PPAS_HEAD.setDuration("");
            PPAS_HEAD.setChannelId("");
            PPAS03001RequestV1.PPAS03001RequestV1Biz.PpasBody PPAS_BODY = new PPAS03001RequestV1.PPAS03001RequestV1Biz.PpasBody();
            bizContent.setPpasBody(PPAS_BODY);
            PPAS_BODY.setPlfmBsnId(PLFM_BSN_ID);
            PPAS_BODY.setPlfmSerNo(createSerNo(accountNo));
            PPAS_BODY.setOpType("0");
            PPAS_BODY.setSignAcctNo(accountNo);
            PPAS_BODY.setSignAcctNme(accountName);
            PPAS_BODY.setOpenMode("**********");
//            PPAS_BODY.setParCpmyIbkCustId("1");
            request.setBizContent(bizContent);
            PPAS03001ResponseV1 response = client.execute(request, UUID.randomUUID().toString().replace("-", ""));
            logger.info(response.toString());
//            if (response.isSuccess()) {
//                System.out.println("success");
//                System.out.println(response.getPpasHead().getReturnMsg());
//            } else {
//                System.out.println(response.getRspCode());
//                System.out.println(response.getRspMsg());
//            }
        } catch (Exception xcp) {
            xcp.printStackTrace();
        }
    }

    /**
     * 签约查询
     */
    public static boolean getAgree(String accountNo, String accountName) {
        try {
            //DefaultBocomClient client = new DefaultBocomClient(APP_ID, new InfoSecRSASignatureSPI(), MY_PRIVATE_KEY, APIGW_PUBLIC_KEY);
            //DefaultBocomClient client = new DefaultBocomClient(APP_ID, new InfoSecRSASignatureSPI(), APIGW_PUBLIC_KEY);
            DefaultBocomClient client = new DefaultBocomClient(APP_ID, MY_PRIVATE_KEY, APIGW_PUBLIC_KEY);
            /**
             * 测试环境可以忽略SSL证书告警，生产环境不可忽略
             */
            client.ignoreSSLHostnameVerifier();

            PPAS03002RequestV1 request = new PPAS03002RequestV1();
            request.setServiceUrl(APIGW_URL_ADDRESS + "/api/ppas/PPAS03002/v1");

            PPAS03002RequestV1.PPAS03002RequestV1Biz bizContent = new PPAS03002RequestV1.PPAS03002RequestV1Biz();
            PPAS03002RequestV1.PPAS03002RequestV1Biz.PpasHead PPAS_HEAD = new PPAS03002RequestV1.PPAS03002RequestV1Biz.PpasHead();
            bizContent.setPpasHead(PPAS_HEAD);
            PPAS_HEAD.setStruct("");
            PPAS_HEAD.setCountry("");
            PPAS_HEAD.setSystemId("");
            PPAS_HEAD.setSendDate("");
            PPAS_HEAD.setTimeZone("");
            PPAS_HEAD.setLanguage("");
            PPAS_HEAD.setPriority("");
            PPAS_HEAD.setEtellerNo("");
            PPAS_HEAD.setOrgId("");
            PPAS_HEAD.setSendTime("");
            PPAS_HEAD.setDuration("");
            PPAS_HEAD.setChannelId("");
            PPAS03002RequestV1.PPAS03002RequestV1Biz.PpasBody PPAS_BODY = new PPAS03002RequestV1.PPAS03002RequestV1Biz.PpasBody();
            bizContent.setPpasBody(PPAS_BODY);
            PPAS_BODY.setPlfmBsnId(PLFM_BSN_ID);
            PPAS_BODY.setSignAcctNo(accountNo);
            PPAS_BODY.setSignAcctNme(accountName);
            request.setBizContent(bizContent);
            PPAS03002ResponseV1 response = client.execute(request, UUID.randomUUID().toString().replace("-", ""));
            logger.info(response.toString());
            if (response.isSuccess() && StringUtils.equals(response.getPpasBody().getPlfmMshpAgmStt(), "0")) {
                return true;
            }
        } catch (Exception xcp) {
            xcp.printStackTrace();
        }
        return false;
    }

    public static String createSerNo(String acctNo) {
        Date now = Calendar.getInstance().getTime();
        String fmtNow = new SimpleDateFormat("yyyyMMddHHmmss").format(now);
        return acctNo + "hf" + fmtNow;
    }

    /**
     * 按页面获取流水
     */
    public static B2BYQFYQFU1016ResponseV1 getJournals(String accountNo, String startDate, String endDate, String bsnAcptNo, String offset) {
        if (StringUtils.isBlank(bsnAcptNo)) {
            bsnAcptNo = "0";
        }
        if (StringUtils.isBlank(offset)) {
            offset = "0";
        }
        try {
            //DefaultBocomClient client = new DefaultBocomClient(APP_ID, new InfoSecRSASignatureSPI(), MY_PRIVATE_KEY, APIGW_PUBLIC_KEY);
            //DefaultBocomClient client = new DefaultBocomClient(APP_ID, new InfoSecRSASignatureSPI(), APIGW_PUBLIC_KEY);
            DefaultBocomClient client = new DefaultBocomClient(APP_ID, MY_PRIVATE_KEY, APIGW_PUBLIC_KEY);

            // 测试环境可以忽略SSL证书告警，生产环境不可忽略
            client.ignoreSSLHostnameVerifier();

            B2BYQFYQFU1016RequestV1 request = new B2BYQFYQFU1016RequestV1();
            request.setServiceUrl(APIGW_URL_ADDRESS + "/api/pptm/B2BYQFYQFU1016/v1");

            B2BYQFYQFU1016RequestV1.B2BYQFYQFU1016RequestV1Biz bizContent = new B2BYQFYQFU1016RequestV1.B2BYQFYQFU1016RequestV1Biz();
            B2BYQFYQFU1016RequestV1.B2BYQFYQFU1016RequestV1Biz.PptmHead PPTM_HEAD = new B2BYQFYQFU1016RequestV1.B2BYQFYQFU1016RequestV1Biz.PptmHead();
            bizContent.setPptmHead(PPTM_HEAD);
            PPTM_HEAD.setStruct("");
            PPTM_HEAD.setCountry("");
            PPTM_HEAD.setSystemId("");
            PPTM_HEAD.setMsgType("");
            PPTM_HEAD.setSendDate("");
            PPTM_HEAD.setTxOrgNo("");
            PPTM_HEAD.setTimeZone("");
            PPTM_HEAD.setLanguage("");
            PPTM_HEAD.setTransNo("");
            PPTM_HEAD.setPriority("");
            PPTM_HEAD.setEtellerNo("");
            PPTM_HEAD.setSendTime("");
            PPTM_HEAD.setDuration("");
            PPTM_HEAD.setTxBranchNo("");
            PPTM_HEAD.setReserve("");
            PPTM_HEAD.setChannelId("");
            B2BYQFYQFU1016RequestV1.B2BYQFYQFU1016RequestV1Biz.PptmBody PPTM_BODY = new B2BYQFYQFU1016RequestV1.B2BYQFYQFU1016RequestV1Biz.PptmBody();
            bizContent.setPptmBody(PPTM_BODY);
            PPTM_BODY.setPlfmBsnId(PLFM_BSN_ID);
            PPTM_BODY.setAcctNo(accountNo);
            PPTM_BODY.setBgnDt(startDate);
            PPTM_BODY.setEndDt(endDate);
            PPTM_BODY.setOprTp("1");
            PPTM_BODY.setBsnAcptNo(bsnAcptNo);
            PPTM_BODY.setOffset(offset);
            request.setBizContent(bizContent);
            B2BYQFYQFU1016ResponseV1 response = client.execute(request, UUID.randomUUID().toString().replace("-", ""));
            logger.info(response.toString());
            return response;
        } catch (Exception xcp) {
            throw new ServiceException(xcp.getMessage(), ErrorCode.BAD_REQUEST);
        }
    }

    /**
     * 获取流水数据
     */
    public static List<B2BYQFYQFU1016ResponseV1.PptmBody.TxnDetl> getJournals(String accountNo, String startDate, String endDate) {
        List<B2BYQFYQFU1016ResponseV1.PptmBody.TxnDetl> result = new ArrayList<>();
        String bsnAcptNo = null;
        String offset = null;
        while (true) {
            B2BYQFYQFU1016ResponseV1 responseV1 = getJournals(accountNo, startDate, endDate, bsnAcptNo, offset);
            if (!responseV1.isSuccess()) {
                throw new ServiceException(responseV1.getRspMsg(), ErrorCode.BAD_REQUEST);
            }
            if (StringUtils.isNotBlank(responseV1.getPptmHead().getReturnMsg())) {
                throw new ServiceException(responseV1.getPptmHead().getReturnMsg(), ErrorCode.BAD_REQUEST);
            }
            List<B2BYQFYQFU1016ResponseV1.PptmBody.TxnDetl> temp = responseV1.getPptmBody().getTxnDetlList();
            if (temp != null && !temp.isEmpty()) {
                result.addAll(temp);
            }
            if (StringUtils.equals("0", responseV1.getPptmBody().getEndFlg())) {
                break;
            }
            bsnAcptNo = responseV1.getPptmBody().getBsnAcptNo();
            offset = responseV1.getPptmBody().getOffset();
        }
        return result;
    }

    /**
     * 设置SFTP公私密钥
     */
    public static void replaceAuthV1() {
        try {
            DefaultBocomClient client = new DefaultBocomClient(APP_ID, MY_PRIVATE_KEY, APIGW_PUBLIC_KEY);
            /*
              测试环境可以忽略SSL证书告警，生产环境不可忽略
             */
            client.ignoreSSLHostnameVerifier();

            //生成2048位RSA公私钥
            KeyPair keyPair = RSAKeyGen.generateKeyPair(2048);

            //公钥
            String pubKey = com.bocom.api.utils.Base64.encode(keyPair.getPublic().getEncoded());
            //私钥    接口返回成功后将私钥复制到SftpTest中
            String priKey = com.bocom.api.utils.Base64.encode(keyPair.getPrivate().getEncoded());

            System.out.println("pubKey:" + pubKey);
            System.out.println("priKey:" + priKey);

            //公钥转ssh格式
            RSAPublicKey rpks;
            KeyFactory keyFactory = KeyFactory.getInstance(BocomConstants.ENCRYPT_TYPE_RSA);
            X509EncodedKeySpec bob = new X509EncodedKeySpec(com.bocom.api.utils.Base64.decode(pubKey));
            rpks = (RSAPublicKey) keyFactory.generatePublic(bob);
            String result = Base64.encode(EncryptUtils.encode(rpks));

            ReplaceAuthRequestV1 request = new ReplaceAuthRequestV1();
            request.setServiceUrl(APIGW_URL_ADDRESS + "/api/chnfobp/replaceAuth/v1");

            ReplaceAuthRequestV1.ReplaceAuthRequestV1Biz bizContent = new ReplaceAuthRequestV1.ReplaceAuthRequestV1Biz();
            bizContent.setPublicKey(result);
            request.setBizContent(bizContent);
            ReplaceAuthResponseV1 response = client.execute(request, UUID.randomUUID().toString().replace("-", ""));
            if (response.isSuccess()) {
                System.out.println("success");
                System.out.println(response);
            } else {
                System.out.println(response.getRspCode());
                System.out.println(response.getRspMsg());
            }
        } catch (Exception xcp) {
            xcp.printStackTrace();
        }
    }

    /**
     * 下载回单数据
     */
    public static List<BankAccountReceiptBcm> sftpDownload(String queryDate) {
        SftpBocomClient client = null;
        List<BankAccountReceiptBcm> result = new ArrayList<>();
        Map<String, String> jrnlSeqNoMap = new HashMap<>();
        try {
            long start = System.currentTimeMillis();
            client = new SftpBocomClient(APP_ID, MY_PRIVATE_KEY, APIGW_PUBLIC_KEY, SFTP_HOST, SFTP_PORT, MY_SFTP_PRIVATE_KEY);
            client.login();
            //文件下载
            byte[] ftpData = client.download("",queryDate,null);
            ZipInputStream zipInputStream = new ZipInputStream(new ByteArrayInputStream(ftpData));
            try {
                ZipEntry entry;
                while ((entry = zipInputStream.getNextEntry()) != null) {
                    if (!entry.isDirectory()) {
                        String fileName = entry.getName();
                        if (StringUtils.endsWith(fileName, ".zip")) {
                            logger.error("交通银行压缩包处理#"+fileName);
                            byte[] data = getByte(zipInputStream);
                            ZipInputStream zipInputStream2 = new ZipInputStream(new ByteArrayInputStream(data));
                            ZipEntry entry2;
                            while ((entry2 = zipInputStream2.getNextEntry()) != null) {
                                if (!entry2.isDirectory()) {
                                    String fileName2 = entry2.getName();
                                    if (StringUtils.endsWith(fileName2, ".pdf")) {
                                        byte[] data2 = getByte(zipInputStream2);
                                        String objectName = OSSUtils.uploadIcbcPdf(fileName2, new ByteArrayInputStream(data2));
                                        BankAccountReceiptBcm bankAccountReceiptBcm = new BankAccountReceiptBcm();
                                        bankAccountReceiptBcm.parseAccount(fileName);
                                        bankAccountReceiptBcm.parseJrnlNo(fileName2);
                                        bankAccountReceiptBcm.receiptUrl = BASE_FILE_URL + objectName;
                                        result.add(bankAccountReceiptBcm);
                                    }
                                    else if (StringUtils.endsWith(fileName2, ".txt") && !StringUtils.endsWith(fileName2, "md5.txt")) {
                                        byte[] data2 = getByte(zipInputStream2);
                                        String[] rows = StringUtils.split(new String(data2), "\r\n");
                                        for (String row : rows) {
                                            String[] cells = StringUtils.split(row, "|!");
                                            if (StringUtils.equals("回单日期", cells[0])) {
                                                continue;
                                            }
                                            jrnlSeqNoMap.put(cells[1], cells[3]);
                                        }
                                    }
                                }
                            }
                            zipInputStream2.closeEntry();
                            zipInputStream2.close();
                        }
                    }
                }
                zipInputStream.closeEntry();
                zipInputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            long end = System.currentTimeMillis();
            System.out.println("sftp cost time: " + (end - start) + "ms");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (client != null) {
                client.logout();
            }
        }
        for (BankAccountReceiptBcm bankAccountReceiptBcm : result) {
            bankAccountReceiptBcm.jrnlSeqNo = jrnlSeqNoMap.get(bankAccountReceiptBcm.receiptNo);
        }
        return result;
    }

    public static void test() {
        try {
            //DefaultBocomClient client = new DefaultBocomClient(APP_ID, new OpenBankSM2SignatureSPI(), MY_PRIVATE_KEY, APIGW_PUBLIC_KEY);
            //DefaultBocomClient client = new DefaultBocomClient(APP_ID, new OpenBankRSAEMSignatureSPI(), APIGW_PUBLIC_KEY);
            DefaultBocomClient client = new DefaultBocomClient(APP_ID, MY_PRIVATE_KEY, APIGW_PUBLIC_KEY);
            /**
             * 测试环境可以忽略SSL证书告警，生产环境不可忽略
             */
            client.ignoreSSLHostnameVerifier();

            BocomCorpActRegisterUrlv1RequestV1 request = new BocomCorpActRegisterUrlv1RequestV1();
            request.setServiceUrl(APIGW_URL_ADDRESS + "/api/ecrm/BocomCorpActRegisterUrlv1/v1");

            BocomCorpActRegisterUrlv1RequestV1.BocomCorpActRegisterUrlv1RequestV1Biz bizContent = new BocomCorpActRegisterUrlv1RequestV1.BocomCorpActRegisterUrlv1RequestV1Biz();
            bizContent.setOpenwebSourcePlatform("0001");
            bizContent.setOpenwebBranchCode("***********");
            bizContent.setOpenwebProductType("0001");
            bizContent.setOpenwebCorpName("企业名称");
            bizContent.setOpenwebCreditNo("统一社会信用代码");
            bizContent.setOpenwebAddr("联系地址");
            bizContent.setOpenwebUserName("联系人");
            bizContent.setOpenwebMobilePhoneNum("联系人手机号");
            bizContent.setOpenwebTokenId("token(合肥高新园区特色)");
            bizContent.setOpenwebTerminalType("1");
            bizContent.setOpenwebTraceNo("流水号");
            bizContent.setOpenwebExt1("备用字段");
            bizContent.setOpenwebExt2("备用字段");
            bizContent.setOpenwebExt3("备用字段");
            bizContent.setOpenwebExt4("备用字段");
            bizContent.setOpenwebExt5("备用字段");
            request.setBizContent(bizContent);
            BocomCorpActRegisterUrlv1ResponseV1 response = client.execute(request,UUID.randomUUID().toString().replace("-", ""));
            if (response.isSuccess()) {
                System.out.println("success");
                System.out.println(response);
            } else {
                System.out.println(response.getRspCode());
                System.out.println(response.getRspMsg());
            }
        } catch (Exception xcp) {
            xcp.printStackTrace();
        }
    }

    public static void main(String[] args) {
//        String accountNo = "322000622013000683682";
//        String accountName = "上海卓顾信息科技有限公司第二分公司";

//        String accountNo = "310069037013008995296";
//        String accountName = "优壹银企付需求九";

//        addAgree(accountNo, accountName);
//        boolean isAgree = getAgree(accountNo, accountName);
//        System.out.println(isAgree);
//        List<B2BYQFYQFU1016ResponseV1.PptmBody.TxnDetl> result = getJournals(accountNo, "********", "********");
//        System.out.println(result);
//        replaceAuthV1();
//        List<BankAccountReceiptBcm> list = sftpDownload("********");
//        System.out.println(list);
//        test();
    }
}
