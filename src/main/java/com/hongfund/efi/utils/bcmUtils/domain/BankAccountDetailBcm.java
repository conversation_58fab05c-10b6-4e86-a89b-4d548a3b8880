package com.hongfund.efi.utils.bcmUtils.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.util.Date;

@EntityListeners(AuditingEntityListener.class)
@Entity
public class BankAccountDetailBcm {
    // JPA 主键标识, 策略为由数据库生成主键
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;
    @CreatedDate
    public Date gmtCreate;
    @LastModifiedDate
    public Date gmtModified;

    /**
     * 账套id
     */
    public Long accountBookId;

    /**
     * 期间
     */
    public String period;
    /**
     * 回单地址
     */
    public String receiptUrl;

    /**
     * 抹账标志
     */
    public String wipedSts;
    /**
     * 交易日期
     */
    public String txnDt;
    /**
     * 交易时间
     */
    public String txnTme;
    /**
     * 业务类型
     */
    public String busTp;
    /**
     * 流水号
     */
    public String jrnlNo;
    /**
     * 流水序号
     */
    public String jrnlSeqNo;
    /**
     * 账号
     */
    public String acctNo;
    /**
     * 户名
     */
    public String acctNme;
    /**
     * 收支标志
     */
    public String incmExpdtFlg;
    /**
     * 币种
     */
    public String ccy;
    /**
     * 交易金额
     */
    public String txnAmt;
    /**
     * 账户余额
     */
    public String acctBal;
    /**
     * 可用余额
     */
    public String avlBal;
    /**
     * 对方账号
     */
    public String cntrpAcctNo;
    /**
     * 对方账户名称
     */
    public String cntrpAcctNme;
    /**
     * 对方地址
     */
    public String cntrpAddr;
    /**
     * 对方开户行行号
     */
    public String cntrpBankNo;
    /**
     * 对方账户开户行行名
     */
    public String cntrpBankNme;
    /**
     * 凭证类型
     */
    public String bvCode;
    /**
     * 凭证号码
     */
    public String bvNo;
    /**
     * 凭证名称
     */
    public String bvNme;
    /**
     * 凭证签发日期
     */
    public String bvSignDt;
    /**
     * 附言
     */
    public String pscpt;
    /**
     * 备注
     */
    public String remrk;
    /**
     * 核心交易码
     */
    public String coreTxnCd;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
