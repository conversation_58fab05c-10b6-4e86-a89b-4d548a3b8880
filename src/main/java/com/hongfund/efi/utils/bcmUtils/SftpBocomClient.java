package com.hongfund.efi.utils.bcmUtils;

import com.bocom.api.BocomApiException;
import com.bocom.api.DefaultBocomClient;
import com.bocom.api.security.crypt.impl.RSACryptor;
import com.bocom.api.security.digest.ApiDigest;
import com.bocom.api.security.digest.impl.DefaultDigest;
import com.bocom.api.security.keygen.RSAKeyGen;
import com.bocom.api.utils.*;
import com.bocom.api.utils.enums.EncryptType;
import com.hongfund.efi.utils.bcmUtils.request.ReplaceAuthRequestV1;
import com.hongfund.efi.utils.bcmUtils.response.ReplaceAuthResponseV1;
import com.jcraft.jsch.ChannelSftp.LsEntry;
import com.jcraft.jsch.SftpException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.Vector;

public class SftpBocomClient extends DefaultBocomClient {
    private static ApiDigest apiDigest = new DefaultDigest();

    private Logger log = LoggerFactory.getLogger(this.getClass());

    private String privateKey;

    private EncryptType encryptType = EncryptType.RSA_AND_AES;

    protected EncryptUtils encryptUtils = new EncryptUtils();

    protected SftpUtil sftpUtil;

    public SftpBocomClient(String appId, String privateKey, String bocomPublicKey, EncryptType encryptType) {
        super(appId, privateKey, bocomPublicKey, encryptType);
        this.encryptType = encryptType;
        this.privateKey = privateKey;
    }

    /*public SftpBocomClient(String appId, String privateKey, String bocomPublicKey, String serviceUrl, String sftpServiceUrl) throws BocomApiException {
        super(appId, privateKey, bocomPublicKey);
        initSftpUtil(serviceUrl, sftpServiceUrl);
    }*/

    public SftpBocomClient(String appId, String privateKey, String bocomPublicKey, String sftpHost, int sftpPort, String sftpPrivateKey) throws BocomApiException {
        super(appId, privateKey, bocomPublicKey);
        initSftpUtil(sftpHost, sftpPort, sftpPrivateKey);
        this.privateKey = privateKey;
    }

    public void initSftpUtil(String host, int port, String privateKey) throws BocomApiException {
        try {
            this.sftpUtil = new SftpUtil(this.appId, host, port, this.encryptUtils.formatPkcs8ToPkcs1(privateKey));
        } catch (IOException e) {
            throw new BocomApiException(e);
        }
    }

    protected void initSftpUtil(String serviceUrl, String sftpServiceUrl) throws BocomApiException {
        // generate random sftp public/private keys
        String sshPublicKey;
        String pkcs1PrivateKey;
        try {
            KeyFactory keyFactory = KeyFactory.getInstance(BocomConstants.ENCRYPT_TYPE_RSA);
            KeyPair keyPair = RSAKeyGen.generateKeyPair(2048);
            String pubKey = Base64.encode(keyPair.getPublic().getEncoded());
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(Base64.decode(pubKey));
            RSAPublicKey rsaPublicKey = (RSAPublicKey)keyFactory.generatePublic(keySpec);
            sshPublicKey = Base64.encode(EncryptUtils.encode(rsaPublicKey));
            String priKey = Base64.encode(keyPair.getPrivate().getEncoded());
            pkcs1PrivateKey = this.encryptUtils.formatPkcs8ToPkcs1(priKey);
        } catch (Exception e) {
            throw new BocomApiException(e);
        }

        // update ssh public key to sftp server
        this.ignoreSSLHostnameVerifier();
        ReplaceAuthRequestV1 request = new ReplaceAuthRequestV1();
        request.setServiceUrl(serviceUrl + "/api/chnfobp/replaceAuth/v1");
        ReplaceAuthRequestV1.ReplaceAuthRequestV1Biz bizContent = new ReplaceAuthRequestV1.ReplaceAuthRequestV1Biz();
        bizContent.setPublicKey(sshPublicKey);
        request.setBizContent(bizContent);
        ReplaceAuthResponseV1 response = this.execute(request, UUID.randomUUID().toString().replace("-", ""));
        if (!response.isSuccess()) {
            throw new BocomApiException("update ssh public key to sftp server error, rspCode: " + response.getRspCode() + " rspMsg: " + response.getRspMsg() + "sshPublicKey: " + sshPublicKey);
        }

        try {
            String[] hostPart = sftpServiceUrl.split("//")[1].split(":");
            String sftpHost = hostPart[0];
            int sftpPort = Integer.valueOf(hostPart[1]);
            this.sftpUtil = new SftpUtil(this.appId, sftpHost, sftpPort, pkcs1PrivateKey);
        } catch (Exception e) {
            throw new BocomApiException(e);
        }
    }

    public SftpUtil getSftpUtil() {
        return sftpUtil;
    }

    public void setSftpUtil(SftpUtil sftpUtil) {
        this.sftpUtil = sftpUtil;
    }

    /**
     * 连接sftp服务器
     *
     * @throws Exception
     */
    public void login() throws BocomApiException {
        try {
            this.sftpUtil.login(0);
        } catch (Exception e) {
            throw new BocomApiException(e);
        }
    }

    /**
     * 连接sftp服务器
     *
     * @throws Exception
     */
    public void login(int timeout) throws BocomApiException {
        try {
            this.sftpUtil.login(timeout);
        } catch (Exception e) {
            throw new BocomApiException(e);
        }
    }

    /**
     * 关闭连接 server
     */
    public void logout() {
        if(this.sftpUtil!=null) {
            this.sftpUtil.logout();
        }
    }

    /**
     * 文件上传
     *
     * @param uploadFile
     * @param date
     * @throws BocomApiException
     */
    public void upload(String uploadFile, String date) throws BocomApiException {
        String directory = BocomConstants.FILE_PATH_UPLOAD + date;
        RSACryptor rsaCryptor = new RSACryptor();
        String aesKey = encryptUtils.getKeyByEncryptType(this.encryptType);
        // 文件Hash
        String fileHashcode = "";
        // 加密后密钥
        String encryptKey = "";

        File inFile = new File(uploadFile);

        String fileName = inFile.getName();
        long fileSize = inFile.length();
        String filePath = inFile.getPath();

        File outFile = new File(filePath + BocomConstants.FILE_SUFFIX_TEM);

        FileOutputStream fos = null;

        FileInputStream in = null;
        FileInputStream inDigest = null;
        FileInputStream fis = null;

        try {
            in = new FileInputStream(uploadFile);
            fos = new FileOutputStream(outFile);

            int bufSize = 1024;
            byte[] buffer = new byte[bufSize];
            int len;
            while (-1 != (len = in.read(buffer, 0, bufSize))) {

                byte[] temp = null;

                if (len < bufSize) {
                    // 最后一次读取时，不需要读取1024长度
                    byte[] tem = new byte[len];
                    System.arraycopy(buffer, 0, tem, 0, tem.length);
                    // temp = aesCryptor.encrypt(tem, aesKey, "");
                    temp = BocomEncrypt.symEncryptContent(tem, this.encryptType, aesKey, "UTF-8");
                    fos.write(temp, 0, temp.length);

                } else {
                    // temp = aesCryptor.encrypt(buffer, aesKey, "");
                    temp = BocomEncrypt.symEncryptContent(buffer, this.encryptType, aesKey, "UTF-8");
                    fos.write(temp, 0, temp.length);
                }
            }

            fis = new FileInputStream(outFile);
            // 加密后文件上传
            this.sftpUtil.upload(directory, fileName+BocomConstants.FILE_SUFFIX_ENCODE, fis);

            // 处理附属文件
            inDigest = new FileInputStream(inFile);
            fileHashcode = BocomDigest.fileDigest(inDigest);
            encryptKey = rsaCryptor.encrypt(aesKey, bocomPublicKey, BocomConstants.CHARSET_UTF8);
            StringBuilder sb = new StringBuilder();
            sb.append(BocomConstants.FILE_HASH_CODE + BocomConstants.COLON + fileHashcode + BocomConstants.LINE_FEED);
            sb.append(BocomConstants.FILE_SIZE + BocomConstants.COLON + fileSize + BocomConstants.LINE_FEED);
            sb.append(BocomConstants.ENCRYPT_TYPE + BocomConstants.COLON + this.encryptType + BocomConstants.LINE_FEED);
            sb.append(BocomConstants.ENCRYPT_KEY + BocomConstants.COLON + encryptKey);

            ByteArrayOutputStream baotem = new ByteArrayOutputStream((sb.toString().length()));
            baotem.write(sb.toString().getBytes(BocomConstants.CHARSET_UTF8));

            // 上传处理文件
            this.sftpUtil.upload(directory, fileName + BocomConstants.FILE_SUFFIX_ATTACH, baotem.toByteArray());

        } catch (FileNotFoundException e) {
            throw new BocomApiException(e.getCause());
        } catch (IOException ex) {
            throw new BocomApiException(ex.getCause());
        } catch (SftpException sex) {
            throw new BocomApiException(sex.getCause());
        } finally {
            try {
                if (fis != null) {
                    fis.close();
                }
                if (in != null) {
                    in.close();
                }
                if (inDigest != null) {
                    inDigest.close();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                throw new BocomApiException(e.getCause());
            }
            outFile.delete();
        }
    }

    public byte[] download(String filePath, String date,String downloadFileName) throws BocomApiException {
        String directory = BocomConstants.FILE_PATH_DOWNLOAD + date;
        RSACryptor rsaCryptor = new RSACryptor();
        ByteArrayOutputStream outputStream = null;
        FileInputStream fis = null;
        File fileOutTem = null;

        try {
            Vector<?> files = this.sftpUtil.listFiles(directory);

            for (int i = 0; i < files.size(); i++) {
                // 新增指定文件名称下载
                if (!StringUtils.isEmpty(downloadFileName)){
                    LsEntry entry = (LsEntry) files.get(i);
                    String filename = entry.getFilename();

                    boolean eqFileName = false;
                    if (filename.endsWith(BocomConstants.FILE_SUFFIX_ATTACH) || filename.endsWith(BocomConstants.FILE_SUFFIX_ENCODE)) {
                        if (!StringUtils.isEmpty(filename)){
                            eqFileName = downloadFileName.equals(filename.substring(0,filename.lastIndexOf(".")));
                        }
                    }

                    if (!eqFileName){ continue;}
                }

                // 只下载有附属文件的文件
                if (((LsEntry)files.get(i)).getFilename().endsWith(BocomConstants.FILE_SUFFIX_ATTACH)) {
                    try {
                        byte[] dataByte = this.sftpUtil.download(directory, ((LsEntry)files.get(i)).getFilename());

                        String[] dataStr = new String(dataByte).split(BocomConstants.LINE_FEED);
                        Map<String, String> dataMap = new HashMap<String, String>();

                        for (int j = 0; j < dataStr.length; j++) {
                            String[] temp = dataStr[j].split(BocomConstants.COLON);
                            dataMap.put(temp[0], temp[1]);
                        }
                        String fileName = ((LsEntry)files.get(i)).getFilename().replace(BocomConstants.FILE_SUFFIX_ATTACH,
                            BocomConstants.FILE_SUFFIX_ENCODE);

                        // 临时文件落地
//                        inputStreamTemp = sftpUtil.downloadInputStream(directory, fileName);
                        outputStream = new ByteArrayOutputStream();
                        sftpUtil.download(directory, fileName, filePath + fileName + BocomConstants.FILE_SUFFIX_TEM);

                        fileOutTem = new File(filePath + fileName + BocomConstants.FILE_SUFFIX_TEM);
                        fis = new FileInputStream(fileOutTem);

                        // 获取encryptKey
                        String encryptKey = dataMap.get(BocomConstants.ENCRYPT_KEY);
                        // 获取encryptType
                        String fileEncryptType = dataMap.get(BocomConstants.ENCRYPT_TYPE);

                        encryptKey = rsaCryptor.decrypt(encryptKey, privateKey, BocomConstants.CHARSET_UTF8);

                        EncryptType encryptType = EncryptType.RSA_AND_AES;
                        if (EncryptType.SM2_AND_SM4.toString().equalsIgnoreCase(fileEncryptType)) {
                            encryptType = EncryptType.SM2_AND_SM4;
                        }
                        // 1024字节对称加密后为1040长度
                        int bufSize = 1040;
                        byte[] buffer = new byte[bufSize];

                        int len;
                        while (-1 != (len = fis.read(buffer, 0, bufSize))) {
                            if (len < bufSize) {
                                // 最后一次读取时，不需要读取1040长度
                                byte[] tem = new byte[len];
                                System.arraycopy(buffer, 0, tem, 0, tem.length);

                                byte[] temp = BocomEncrypt.symDecryptContent(tem,encryptType, encryptKey, "UTF-8");
                                if (temp != null) {
                                    outputStream.write(temp, 0, temp.length);
                                }
                            } else {
                                byte[] temp = BocomEncrypt.symDecryptContent(buffer,encryptType, encryptKey, "UTF-8");
                                if (temp != null) {
                                    outputStream.write(temp, 0, temp.length);
                                }
                            }
                        }
                        byte[] result = outputStream.toByteArray();
                        String fileHashcode = apiDigest.digest(result, "SHA-256");
                        if (fileHashcode != null && fileHashcode.equals(dataMap.get(BocomConstants.FILE_HASH_CODE))) {

                        } else {
                            log.info(fileName + "文件fileHashcode校验失败");
                            break;
                        }
                        long fileSize = result.length;

                        if (fileSize == Long.parseLong(dataMap.get(BocomConstants.FILE_SIZE))) {

                        } else {
                            log.info(fileName + "文件大小校验失败");
                            break;
                        }
                        return result;
                    } catch (Exception e) {
                        throw new BocomApiException(e.getCause());
                    } finally {
                        try {
                            if (fis != null) {
                                fis.close();
                            }
                            if (outputStream != null) {
                                outputStream.close();
                            }
                            if (fileOutTem != null) {
                                fileOutTem.delete();
                            }
                        } catch (IOException e) {
                            throw new BocomApiException(e.getCause());
                        }
                    }
                }
            }

        } catch (SftpException e) {
            throw new BocomApiException(e);
        }
        return null;
    }

}
