package com.hongfund.efi.utils.bcmUtils.response.ppas;

import com.bocom.api.BocomResponse;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;

public class PPAS03002ResponseV1 extends BocomResponse {
    /**
     * ""
     */
    @JsonProperty("ppas_head")
    private PpasHead ppasHead;

    public static class PpasHead {
        /**
         * 格式类型
         */
        @JsonProperty("struct")
        private String struct;

        /**
         * 返回码
         */
        @JsonProperty("return_code")
        private String returnCode;

        /**
         * 错误信息
         */
        @JsonProperty("return_msg")
        private String returnMsg;

        /**
         * 报文发送日期
         */
        @JsonProperty("send_date")
        private String sendDate;

        /**
         * 预留
         */
        @JsonProperty("reserve")
        private String reserve;

        /**
         * 版本号
         */
        @JsonProperty("version")
        private String version;

        /**
         * 报文发送时间
         */
        @JsonProperty("send_time")
        private String sendTime;

        public String getStruct() {
            return struct;
        }

        public void setStruct(String struct) {
            this.struct = struct;
        }

        public String getReturnCode() {
            return returnCode;
        }

        public void setReturnCode(String returnCode) {
            this.returnCode = returnCode;
        }

        public String getReturnMsg() {
            return returnMsg;
        }

        public void setReturnMsg(String returnMsg) {
            this.returnMsg = returnMsg;
        }

        public String getSendDate() {
            return sendDate;
        }

        public void setSendDate(String sendDate) {
            this.sendDate = sendDate;
        }

        public String getReserve() {
            return reserve;
        }

        public void setReserve(String reserve) {
            this.reserve = reserve;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getSendTime() {
            return sendTime;
        }

        public void setSendTime(String sendTime) {
            this.sendTime = sendTime;
        }

        @Override
        public String toString() {
            return ToStringBuilder.reflectionToString(this);
        }
    }

    /**
     * ""
     */
    @JsonProperty("ppas_body")
    private PpasBody ppasBody;

    public static class PpasBody {
        /**
         * 平台会员协议编号
         */
        @JsonProperty("plfm_mshp_agm_id")
        private String plfmMshpAgmId;

        /**
         * 平台会员协议状态
         */
        @JsonProperty("plfm_mshp_agm_stt")
        private String plfmMshpAgmStt;

        /**
         * 开通网银功能
         */
        @JsonProperty("open_mode")
        private String openMode;

        /**
         * 电子渠道开通标志
         */
        @JsonProperty("open_flag")
        private String openFlag;

        public String getPlfmMshpAgmId() {
            return plfmMshpAgmId;
        }

        public void setPlfmMshpAgmId(String plfmMshpAgmId) {
            this.plfmMshpAgmId = plfmMshpAgmId;
        }

        public String getPlfmMshpAgmStt() {
            return plfmMshpAgmStt;
        }

        public void setPlfmMshpAgmStt(String plfmMshpAgmStt) {
            this.plfmMshpAgmStt = plfmMshpAgmStt;
        }

        public String getOpenMode() {
            return openMode;
        }

        public void setOpenMode(String openMode) {
            this.openMode = openMode;
        }

        public String getOpenFlag() {
            return openFlag;
        }

        public void setOpenFlag(String openFlag) {
            this.openFlag = openFlag;
        }

        @Override
        public String toString() {
            return ToStringBuilder.reflectionToString(this);
        }
    }

    public PpasHead getPpasHead() {
        return ppasHead;
    }

    public void setPpasHead(PpasHead ppasHead) {
        this.ppasHead = ppasHead;
    }

    public PpasBody getPpasBody() {
        return ppasBody;
    }

    public void setPpasBody(PpasBody ppasBody) {
        this.ppasBody = ppasBody;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
