package com.hongfund.efi.utils.bcmUtils.request.ecrm;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.hongfund.efi.utils.bcmUtils.response.ecrm.BocomCorpActRegisterUrlv1ResponseV1;


public class BocomCorpActRegisterUrlv1RequestV1 extends AbstractBocomRequest<BocomCorpActRegisterUrlv1ResponseV1> {

    @Override
    public Class<BocomCorpActRegisterUrlv1ResponseV1> getResponseClass() {
        return BocomCorpActRegisterUrlv1ResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return BocomCorpActRegisterUrlv1RequestV1Biz.class;
    }


    public static class BocomCorpActRegisterUrlv1RequestV1Biz implements BizContent {

        /**
         * 来源平台枚举
         */
        @JsonProperty("openweb_source_platform")
        private String openwebSourcePlatform;

        /**
         * 所属分行号
         */
        @JsonProperty("openweb_branch_code")
        private String openwebBranchCode;

        /**
         * 产品枚举
         */
        @JsonProperty("openweb_product_type")
        private String openwebProductType;

        /**
         * 企业名称
         */
        @JsonProperty("openweb_corp_name")
        private String openwebCorpName;

        /**
         * 统一社会信用代码
         */
        @JsonProperty("openweb_credit_no")
        private String openwebCreditNo;

        /**
         * 联系地址
         */
        @JsonProperty("openweb_addr")
        private String openwebAddr;

        /**
         * 联系人
         */
        @JsonProperty("openweb_user_name")
        private String openwebUserName;

        /**
         * 联系人手机号
         */
        @JsonProperty("openweb_mobile_phone_num")
        private String openwebMobilePhoneNum;

        /**
         * token(合肥高新园区特色)
         */
        @JsonProperty("openweb_token_id")
        private String openwebTokenId;

        /**
         * 终端类型枚举
         */
        @JsonProperty("openweb_terminal_type")
        private String openwebTerminalType;

        /**
         * 流水号
         */
        @JsonProperty("openweb_trace_no")
        private String openwebTraceNo;

        /**
         * 备用字段1
         */
        @JsonProperty("openweb_ext1")
        private String openwebExt1;

        /**
         * 备用字段2
         */
        @JsonProperty("openweb_ext2")
        private String openwebExt2;

        /**
         * 备用字段3
         */
        @JsonProperty("openweb_ext3")
        private String openwebExt3;

        /**
         * 备用字段4
         */
        @JsonProperty("openweb_ext4")
        private String openwebExt4;

        /**
         * 备用字段5
         */
        @JsonProperty("openweb_ext5")
        private String openwebExt5;

        public String getOpenwebSourcePlatform() {
            return openwebSourcePlatform;
        }

        public void setOpenwebSourcePlatform(String openwebSourcePlatform) {
            this.openwebSourcePlatform = openwebSourcePlatform;
        }

        public String getOpenwebBranchCode() {
            return openwebBranchCode;
        }

        public void setOpenwebBranchCode(String openwebBranchCode) {
            this.openwebBranchCode = openwebBranchCode;
        }

        public String getOpenwebProductType() {
            return openwebProductType;
        }

        public void setOpenwebProductType(String openwebProductType) {
            this.openwebProductType = openwebProductType;
        }

        public String getOpenwebCorpName() {
            return openwebCorpName;
        }

        public void setOpenwebCorpName(String openwebCorpName) {
            this.openwebCorpName = openwebCorpName;
        }

        public String getOpenwebCreditNo() {
            return openwebCreditNo;
        }

        public void setOpenwebCreditNo(String openwebCreditNo) {
            this.openwebCreditNo = openwebCreditNo;
        }

        public String getOpenwebAddr() {
            return openwebAddr;
        }

        public void setOpenwebAddr(String openwebAddr) {
            this.openwebAddr = openwebAddr;
        }

        public String getOpenwebUserName() {
            return openwebUserName;
        }

        public void setOpenwebUserName(String openwebUserName) {
            this.openwebUserName = openwebUserName;
        }

        public String getOpenwebMobilePhoneNum() {
            return openwebMobilePhoneNum;
        }

        public void setOpenwebMobilePhoneNum(String openwebMobilePhoneNum) {
            this.openwebMobilePhoneNum = openwebMobilePhoneNum;
        }

        public String getOpenwebTokenId() {
            return openwebTokenId;
        }

        public void setOpenwebTokenId(String openwebTokenId) {
            this.openwebTokenId = openwebTokenId;
        }

        public String getOpenwebTerminalType() {
            return openwebTerminalType;
        }

        public void setOpenwebTerminalType(String openwebTerminalType) {
            this.openwebTerminalType = openwebTerminalType;
        }

        public String getOpenwebTraceNo() {
            return openwebTraceNo;
        }

        public void setOpenwebTraceNo(String openwebTraceNo) {
            this.openwebTraceNo = openwebTraceNo;
        }

        public String getOpenwebExt1() {
            return openwebExt1;
        }

        public void setOpenwebExt1(String openwebExt1) {
            this.openwebExt1 = openwebExt1;
        }

        public String getOpenwebExt2() {
            return openwebExt2;
        }

        public void setOpenwebExt2(String openwebExt2) {
            this.openwebExt2 = openwebExt2;
        }

        public String getOpenwebExt3() {
            return openwebExt3;
        }

        public void setOpenwebExt3(String openwebExt3) {
            this.openwebExt3 = openwebExt3;
        }

        public String getOpenwebExt4() {
            return openwebExt4;
        }

        public void setOpenwebExt4(String openwebExt4) {
            this.openwebExt4 = openwebExt4;
        }

        public String getOpenwebExt5() {
            return openwebExt5;
        }

        public void setOpenwebExt5(String openwebExt5) {
            this.openwebExt5 = openwebExt5;
        }
    }
}
