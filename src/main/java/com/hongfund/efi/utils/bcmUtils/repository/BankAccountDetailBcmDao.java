package com.hongfund.efi.utils.bcmUtils.repository;

import com.hongfund.efi.utils.bcmUtils.domain.BankAccountDetailBcm;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface BankAccountDetailBcmDao extends JpaRepository<BankAccountDetailBcm, Long> {
    List<BankAccountDetailBcm> findByAccountBookIdAndPeriod(Long accountBookId, String period);
    void deleteByAccountBookIdAndPeriod(long accountBookId, String period);
}
