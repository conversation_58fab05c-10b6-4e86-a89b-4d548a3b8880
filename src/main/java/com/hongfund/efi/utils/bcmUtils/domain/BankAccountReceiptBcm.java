package com.hongfund.efi.utils.bcmUtils.domain;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.util.Date;

@EntityListeners(AuditingEntityListener.class)
@Entity
public class BankAccountReceiptBcm {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;
    @CreatedDate
    public Date gmtCreate;
    @LastModifiedDate
    public Date gmtModified;

    /**
     * 账号
     */
    public String account;
    /**
     * 会计流水号
     */
    public String jrnlNo;
    /**
     * 流水序号
     */
    public String jrnlSeqNo;
    /**
     * 回单日期
     */
    public String receiptDate;
    /**
     * 回单编号
     */
    public String receiptNo;
    /**
     * 回单地址
     */
    public String receiptUrl;

    /**
     * 文件名解析
     *  eg: 310069037013008329880_20301231.zip
     */
    public void parseAccount(String fileName) {
        String[] temp = StringUtils.split(fileName, "_");
        this.account = temp[0];
    }

    /**
     * 文件名解析
     *  eg: 20301231_036518465620_EAA0000000613306.pdf
     */
    public void parseJrnlNo(String fileName) {
        String[] temp = StringUtils.split(fileName, "_");
        this.receiptDate = temp[0];
        this.receiptNo = temp[1];
        this.jrnlNo = StringUtils.removeEnd(temp[2], ".pdf");
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
