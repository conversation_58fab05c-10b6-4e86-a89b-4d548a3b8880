package com.hongfund.efi.utils.bcmUtils.response.ecrm;

import com.bocom.api.BocomResponse;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;

public class BocomCorpActRegisterUrlv1ResponseV1 extends BocomResponse {
    @JsonProperty("url")
    private String url;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
