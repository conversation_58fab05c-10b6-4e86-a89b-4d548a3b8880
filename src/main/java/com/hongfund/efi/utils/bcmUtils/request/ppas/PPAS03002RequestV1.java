package com.hongfund.efi.utils.bcmUtils.request.ppas;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.hongfund.efi.utils.bcmUtils.response.ppas.PPAS03002ResponseV1;


public class PPAS03002RequestV1 extends AbstractBocomRequest<PPAS03002ResponseV1> {

  @Override
  public Class<PPAS03002ResponseV1> getResponseClass() {
    return PPAS03002ResponseV1.class;
  }

  @Override
  public boolean isNeedEncrypt() {
    return false;
  }

  @Override
  public String getMethod() {
    return "POST";
  }

  @Override
  public Class<? extends BizContent> getBizContentClass() {
    return PPAS03002RequestV1Biz.class;
  }

  public static class PPAS03002RequestV1Biz implements BizContent {

	/** ""*/
	@JsonProperty("ppas_head")
	private PpasHead ppasHead;

	public static class PpasHead {
     /** 报文格式*/
     @JsonProperty("struct")
     private String struct;

     /** 国家*/
     @JsonProperty("country")
     private String country;

     /** 系统号*/
     @JsonProperty("system_id")
     private String systemId;

     /** 发送日期*/
     @JsonProperty("send_date")
     private String sendDate;

     /** 时区*/
     @JsonProperty("time_zone")
     private String timeZone;

     /** 语言*/
     @JsonProperty("language")
     private String language;

     /** 优先级*/
     @JsonProperty("priority")
     private String priority;

     /** 签约柜员号*/
     @JsonProperty("eteller_no")
     private String etellerNo;

     /** 机构ID*/
     @JsonProperty("org_id")
     private String orgId;

     /** 发送时间*/
     @JsonProperty("send_time")
     private String sendTime;

     /** 报文有效时间*/
     @JsonProperty("duration")
     private String duration;

     /** 渠道ID*/
     @JsonProperty("channel_id")
     private String channelId;

	public String getStruct() {
		return struct;
	}

	public void setStruct(String struct) {
		this.struct = struct;
	}
	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}
	public String getSystemId() {
		return systemId;
	}

	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}
	public String getSendDate() {
		return sendDate;
	}

	public void setSendDate(String sendDate) {
		this.sendDate = sendDate;
	}
	public String getTimeZone() {
		return timeZone;
	}

	public void setTimeZone(String timeZone) {
		this.timeZone = timeZone;
	}
	public String getLanguage() {
		return language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}
	public String getPriority() {
		return priority;
	}

	public void setPriority(String priority) {
		this.priority = priority;
	}
	public String getEtellerNo() {
		return etellerNo;
	}

	public void setEtellerNo(String etellerNo) {
		this.etellerNo = etellerNo;
	}
	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}
	public String getSendTime() {
		return sendTime;
	}

	public void setSendTime(String sendTime) {
		this.sendTime = sendTime;
	}
	public String getDuration() {
		return duration;
	}

	public void setDuration(String duration) {
		this.duration = duration;
	}
	public String getChannelId() {
		return channelId;
	}

	public void setChannelId(String channelId) {
		this.channelId = channelId;
	}
}	/** ""*/
	@JsonProperty("ppas_body")
	private PpasBody ppasBody;

	public static class PpasBody {
     /** 平台业务编号*/
     @JsonProperty("plfm_bsn_id")
     private String plfmBsnId;

     /** 签约账号*/
     @JsonProperty("sign_acct_no")
     private String signAcctNo;

     /** 签约账户名称*/
     @JsonProperty("sign_acct_nme")
     private String signAcctNme;

	public String getPlfmBsnId() {
		return plfmBsnId;
	}

	public void setPlfmBsnId(String plfmBsnId) {
		this.plfmBsnId = plfmBsnId;
	}
	public String getSignAcctNo() {
		return signAcctNo;
	}

	public void setSignAcctNo(String signAcctNo) {
		this.signAcctNo = signAcctNo;
	}
	public String getSignAcctNme() {
		return signAcctNme;
	}

	public void setSignAcctNme(String signAcctNme) {
		this.signAcctNme = signAcctNme;
	}
}	public PpasHead getPpasHead() {
		return ppasHead;
	}

	public void setPpasHead(PpasHead ppasHead) {
		this.ppasHead = ppasHead;
	}
	public PpasBody getPpasBody() {
		return ppasBody;
	}

	public void setPpasBody(PpasBody ppasBody) {
		this.ppasBody = ppasBody;
	}
}
}
