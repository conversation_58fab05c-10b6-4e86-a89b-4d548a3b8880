package com.hongfund.efi.utils.bcmUtils.response.pptm;

import com.bocom.api.BocomResponse;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.List;

public class B2BYQFYQFU1016ResponseV1 extends BocomResponse {
    /**
     * ""
     */
    @JsonProperty("pptm_head")
    private PptmHead pptmHead;

    public static class PptmHead {
        /**
         * 格式类型
         */
        @JsonProperty("struct")
        private String struct;

        /**
         * 返回码
         */
        @JsonProperty("return_code")
        private String returnCode;

        /**
         * 错误信息
         */
        @JsonProperty("return_msg")
        private String returnMsg;

        /**
         * 报文发送日期
         */
        @JsonProperty("send_date")
        private String sendDate;

        /**
         * 交易状态
         */
        @JsonProperty("tran_state")
        private String tranState;

        /**
         * 预留
         */
        @JsonProperty("reserve")
        private String reserve;

        /**
         * 交易流水号
         */
        @JsonProperty("trans_no")
        private String transNo;

        /**
         * 版本号
         */
        @JsonProperty("version")
        private String version;

        /**
         * 报文发送时间
         */
        @JsonProperty("send_time")
        private String sendTime;

        public String getStruct() {
            return struct;
        }

        public void setStruct(String struct) {
            this.struct = struct;
        }

        public String getReturnCode() {
            return returnCode;
        }

        public void setReturnCode(String returnCode) {
            this.returnCode = returnCode;
        }

        public String getReturnMsg() {
            return returnMsg;
        }

        public void setReturnMsg(String returnMsg) {
            this.returnMsg = returnMsg;
        }

        public String getSendDate() {
            return sendDate;
        }

        public void setSendDate(String sendDate) {
            this.sendDate = sendDate;
        }

        public String getTranState() {
            return tranState;
        }

        public void setTranState(String tranState) {
            this.tranState = tranState;
        }

        public String getReserve() {
            return reserve;
        }

        public void setReserve(String reserve) {
            this.reserve = reserve;
        }

        public String getTransNo() {
            return transNo;
        }

        public void setTransNo(String transNo) {
            this.transNo = transNo;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getSendTime() {
            return sendTime;
        }

        public void setSendTime(String sendTime) {
            this.sendTime = sendTime;
        }

        @Override
        public String toString() {
            return ToStringBuilder.reflectionToString(this);
        }
    }

    /**
     * ""
     */
    @JsonProperty("pptm_body")
    private PptmBody pptmBody;

    public static class PptmBody {
        /**
         * "交易明细列表"
         */
        @JsonProperty("txn_detl_list")
        private List<TxnDetl> txnDetlList;

        public static class TxnDetl {
            /**
             * 抹账标志
             */
            @JsonProperty("wiped_sts")
            private String wipedSts;

            /**
             * 交易日期
             */
            @JsonProperty("txn_dt")
            private String txnDt;

            /**
             * 交易时间
             */
            @JsonProperty("txn_tme")
            private String txnTme;

            /**
             * 业务类型
             */
            @JsonProperty("bus_tp")
            private String busTp;

            /**
             * 流水号
             */
            @JsonProperty("jrnl_no")
            private String jrnlNo;

            /**
             * 流水序号
             */
            @JsonProperty("jrnl_seq_no")
            private String jrnlSeqNo;

            /**
             * 账号
             */
            @JsonProperty("acct_no")
            private String acctNo;

            /**
             * 户名
             */
            @JsonProperty("acct_nme")
            private String acctNme;

            /**
             * 收支标志
             */
            @JsonProperty("incm_expdt_flg")
            private String incmExpdtFlg;

            /**
             * 币种
             */
            @JsonProperty("ccy")
            private String ccy;

            /**
             * 交易金额
             */
            @JsonProperty("txn_amt")
            private String txnAmt;

            /**
             * 账户余额
             */
            @JsonProperty("acct_bal")
            private String acctBal;

            /**
             * 可用余额
             */
            @JsonProperty("avl_bal")
            private String avlBal;

            /**
             * 对方账号
             */
            @JsonProperty("cntrp_acct_no")
            private String cntrpAcctNo;

            /**
             * 对方账户名称
             */
            @JsonProperty("cntrp_acct_nme")
            private String cntrpAcctNme;

            /**
             * 对方地址
             */
            @JsonProperty("cntrp_addr")
            private String cntrpAddr;

            /**
             * 对方开户行行号
             */
            @JsonProperty("cntrp_bank_no")
            private String cntrpBankNo;

            /**
             * 对方账户开户行行名
             */
            @JsonProperty("cntrp_bank_nme")
            private String cntrpBankNme;

            /**
             * 凭证类型
             */
            @JsonProperty("bv_code")
            private String bvCode;

            /**
             * 凭证号码
             */
            @JsonProperty("bv_no")
            private String bvNo;

            /**
             * 凭证名称
             */
            @JsonProperty("bv_nme")
            private String bvNme;

            /**
             * 凭证签发日期
             */
            @JsonProperty("bv_sign_dt")
            private String bvSignDt;

            /**
             * 附言
             */
            @JsonProperty("pscpt")
            private String pscpt;

            /**
             * 备注
             */
            @JsonProperty("remrk")
            private String remrk;

            /**
             * 核心交易码
             */
            @JsonProperty("core_txn_cd")
            private String coreTxnCd;

            public String getWipedSts() {
                return wipedSts;
            }

            public void setWipedSts(String wipedSts) {
                this.wipedSts = wipedSts;
            }

            public String getTxnDt() {
                return txnDt;
            }

            public void setTxnDt(String txnDt) {
                this.txnDt = txnDt;
            }

            public String getTxnTme() {
                return txnTme;
            }

            public void setTxnTme(String txnTme) {
                this.txnTme = txnTme;
            }

            public String getBusTp() {
                return busTp;
            }

            public void setBusTp(String busTp) {
                this.busTp = busTp;
            }

            public String getJrnlNo() {
                return jrnlNo;
            }

            public void setJrnlNo(String jrnlNo) {
                this.jrnlNo = jrnlNo;
            }

            public String getJrnlSeqNo() {
                return jrnlSeqNo;
            }

            public void setJrnlSeqNo(String jrnlSeqNo) {
                this.jrnlSeqNo = jrnlSeqNo;
            }

            public String getAcctNo() {
                return acctNo;
            }

            public void setAcctNo(String acctNo) {
                this.acctNo = acctNo;
            }

            public String getAcctNme() {
                return acctNme;
            }

            public void setAcctNme(String acctNme) {
                this.acctNme = acctNme;
            }

            public String getIncmExpdtFlg() {
                return incmExpdtFlg;
            }

            public void setIncmExpdtFlg(String incmExpdtFlg) {
                this.incmExpdtFlg = incmExpdtFlg;
            }

            public String getCcy() {
                return ccy;
            }

            public void setCcy(String ccy) {
                this.ccy = ccy;
            }

            public String getTxnAmt() {
                return txnAmt;
            }

            public void setTxnAmt(String txnAmt) {
                this.txnAmt = txnAmt;
            }

            public String getAcctBal() {
                return acctBal;
            }

            public void setAcctBal(String acctBal) {
                this.acctBal = acctBal;
            }

            public String getAvlBal() {
                return avlBal;
            }

            public void setAvlBal(String avlBal) {
                this.avlBal = avlBal;
            }

            public String getCntrpAcctNo() {
                return cntrpAcctNo;
            }

            public void setCntrpAcctNo(String cntrpAcctNo) {
                this.cntrpAcctNo = cntrpAcctNo;
            }

            public String getCntrpAcctNme() {
                return cntrpAcctNme;
            }

            public void setCntrpAcctNme(String cntrpAcctNme) {
                this.cntrpAcctNme = cntrpAcctNme;
            }

            public String getCntrpAddr() {
                return cntrpAddr;
            }

            public void setCntrpAddr(String cntrpAddr) {
                this.cntrpAddr = cntrpAddr;
            }

            public String getCntrpBankNo() {
                return cntrpBankNo;
            }

            public void setCntrpBankNo(String cntrpBankNo) {
                this.cntrpBankNo = cntrpBankNo;
            }

            public String getCntrpBankNme() {
                return cntrpBankNme;
            }

            public void setCntrpBankNme(String cntrpBankNme) {
                this.cntrpBankNme = cntrpBankNme;
            }

            public String getBvCode() {
                return bvCode;
            }

            public void setBvCode(String bvCode) {
                this.bvCode = bvCode;
            }

            public String getBvNo() {
                return bvNo;
            }

            public void setBvNo(String bvNo) {
                this.bvNo = bvNo;
            }

            public String getBvNme() {
                return bvNme;
            }

            public void setBvNme(String bvNme) {
                this.bvNme = bvNme;
            }

            public String getBvSignDt() {
                return bvSignDt;
            }

            public void setBvSignDt(String bvSignDt) {
                this.bvSignDt = bvSignDt;
            }

            public String getPscpt() {
                return pscpt;
            }

            public void setPscpt(String pscpt) {
                this.pscpt = pscpt;
            }

            public String getRemrk() {
                return remrk;
            }

            public void setRemrk(String remrk) {
                this.remrk = remrk;
            }

            public String getCoreTxnCd() {
                return coreTxnCd;
            }

            public void setCoreTxnCd(String coreTxnCd) {
                this.coreTxnCd = coreTxnCd;
            }

            @Override
            public String toString() {
                return ToStringBuilder.reflectionToString(this);
            }
        }

        /**
         * 查询条数
         */
        @JsonProperty("enqre_rec_cnt")
        private String enqreRecCnt;

        /**
         * 业务受理号
         */
        @JsonProperty("bsn_acpt_no")
        private String bsnAcptNo;

        /**
         * 偏移量
         */
        @JsonProperty("offset")
        private String offset;

        /**
         * 结束标志
         */
        @JsonProperty("end_flg")
        private String endFlg;

        public List<TxnDetl> getTxnDetlList() {
            return txnDetlList;
        }

        public void setTxnDetlList(List<TxnDetl> txnDetlList) {
            this.txnDetlList = txnDetlList;
        }

        public String getEnqreRecCnt() {
            return enqreRecCnt;
        }

        public void setEnqreRecCnt(String enqreRecCnt) {
            this.enqreRecCnt = enqreRecCnt;
        }

        public String getBsnAcptNo() {
            return bsnAcptNo;
        }

        public void setBsnAcptNo(String bsnAcptNo) {
            this.bsnAcptNo = bsnAcptNo;
        }

        public String getOffset() {
            return offset;
        }

        public void setOffset(String offset) {
            this.offset = offset;
        }

        public String getEndFlg() {
            return endFlg;
        }

        public void setEndFlg(String endFlg) {
            this.endFlg = endFlg;
        }

        @Override
        public String toString() {
            return ToStringBuilder.reflectionToString(this);
        }
    }

    public PptmHead getPptmHead() {
        return pptmHead;
    }

    public void setPptmHead(PptmHead pptmHead) {
        this.pptmHead = pptmHead;
    }

    public PptmBody getPptmBody() {
        return pptmBody;
    }

    public void setPptmBody(PptmBody pptmBody) {
        this.pptmBody = pptmBody;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
