package com.hongfund.efi.utils.wxUtitls.response;

import com.hongfund.efi.utils.wxUtitls.WxAppPrepay;

/**
 * 微信小程序创建预支付订单接口响应Response
 */
public class PrepayResponse {
    /**
     * 
     */
    public String appid;
    /**
     * 商户号
     */
    public String partnerId;
    /**
     * 预支付交易会话ID
     */
    public String prepayId; 
    /**
     * 扩展字段
     */
    public WxAppPrepay.DirectAPIv3AppPrepayResponse packageValue;
    /**
     * 随机字符串
     */
    public String nonceStr;
    /**
     * 时间戳
     */
    public String timestamp;
    /**
     * 订单号
     */
    public String orderId;

    /**
     * 签名值
     */
    public String paySign;

}
