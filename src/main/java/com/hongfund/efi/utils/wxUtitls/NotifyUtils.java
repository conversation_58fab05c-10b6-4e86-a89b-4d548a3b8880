package com.hongfund.efi.utils.wxUtitls;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class NotifyUtils {

    public static class ResourceInfo {
        /** 原始类型 */
        @SerializedName("original_type")
        public String originalType;

        /** 加密算法 */
        @SerializedName("algorithm")
        public String algorithm;

        /** 密文 */
        @SerializedName("ciphertext")
        public String ciphertext;

        /** 附加数据 */
        @SerializedName("associated_data")
        public String associatedData;

        /** 随机串 */
        @SerializedName("nonce")
        public String nonce;
    }

    public static class NotifyRequest {
        /** 通知ID */
        @SerializedName("id")
        public String id;

        /** 创建时间 */
        @SerializedName("create_time")
        public String createTime;

        /** 资源类型 */
        @SerializedName("resource_type")
        public String resourceType;

        /** 事件类型 */
        @SerializedName("event_type")
        public String eventType;

        /** 摘要 */
        @SerializedName("summary")
        public String summary;

        /** 资源数据 */
        @SerializedName("resource")
        public ResourceInfo resource;
    }

    public static class TransactionInfo {
        /** 微信支付订单号 */
        @SerializedName("transaction_id")
        public String transactionId;

        /** 订单金额信息 */
        @SerializedName("amount")
        public AmountInfo amount;

        /** 商户号 */
        @SerializedName("mchid")
        public String mchid;

        /**
         * 【交易状态】 交易状态，详细业务流转状态处理请参考开发指引-订单状态流转图。枚举值：
         * SUCCESS：支付成功
         * REFUND：转入退款
         * NOTPAY：未支付
         * CLOSED：已关闭
         * REVOKED：已撤销（仅付款码支付会返回）
         * USERPAYING：用户支付中（仅付款码支付会返回）
         * PAYERROR：支付失败（仅付款码支付会返回）
         *  */
        @SerializedName("trade_state")
        public String tradeState;

        /** 付款银行 */
        @SerializedName("bank_type")
        public String bankType;

        /** 优惠详情 */
        @SerializedName("promotion_detail")
        public List<PromotionDetail> promotionDetail;

        /** 支付完成时间 */
        @SerializedName("success_time")
        public String successTime;

        /** 支付者信息 */
        @SerializedName("payer")
        public PayerInfo payer;

        /** 商户订单号 */
        @SerializedName("out_trade_no")
        public String outTradeNo;

        /** 应用ID */
        @SerializedName("appid")
        public String appid;

        /** 交易状态描述 */
        @SerializedName("trade_state_desc")
        public String tradeStateDesc;

        /**
         * 【交易类型】 返回当前订单的交易类型，枚举值：
         * JSAPI：公众号支付、小程序支付
         * NATIVE：Native支付
         * APP：APP支付
         * MICROPAY：付款码支付
         * MWEB：H5支付
         * FACEPAY：刷脸支付
         * */
        @SerializedName("trade_type")
        public String tradeType;

        /** 附加数据 */
        @SerializedName("attach")
        public String attach;

        /** 场景信息 */
        @SerializedName("scene_info")
        public SceneInfo sceneInfo;
    }

    public static class AmountInfo {
        /** 用户支付金额 */
        @SerializedName("payer_total")
        public int payerTotal;

        /** 总金额 */
        @SerializedName("total")
        public int total;

        /** 货币类型 */
        @SerializedName("currency")
        public String currency;

        /** 用户支付币种 */
        @SerializedName("payer_currency")
        public String payerCurrency;
    }

    public static class PromotionDetail {
        /** 优惠券金额 */
        @SerializedName("amount")
        public int amount;

        /** 微信出资 */
        @SerializedName("wechatpay_contribute")
        public int wechatpayContribute;

        /** 优惠券ID */
        @SerializedName("coupon_id")
        public String couponId;

        /** 优惠范围 */
        @SerializedName("scope")
        public String scope;

        /** 商户出资 */
        @SerializedName("merchant_contribute")
        public int merchantContribute;

        /** 优惠名称 */
        @SerializedName("name")
        public String name;

        /** 其他出资 */
        @SerializedName("other_contribute")
        public int otherContribute;

        /** 货币类型 */
        @SerializedName("currency")
        public String currency;

        /** 批次ID */
        @SerializedName("stock_id")
        public String stockId;

        /** 商品详情 */
        @SerializedName("goods_detail")
        public List<GoodsDetail> goodsDetail;
    }

    public static class GoodsDetail {
        /** 商品备注 */
        @SerializedName("goods_remark")
        public String goodsRemark;

        /** 商品数量 */
        @SerializedName("quantity")
        public int quantity;

        /** 优惠金额 */
        @SerializedName("discount_amount")
        public int discountAmount;

        /** 商品ID */
        @SerializedName("goods_id")
        public String goodsId;

        /** 商品单价 */
        @SerializedName("unit_price")
        public int unitPrice;
    }

    public static class PayerInfo {
        /** 用户标识 */
        @SerializedName("openid")
        public String openid;
    }

    public static class SceneInfo {
        /** 设备ID */
        @SerializedName("device_id")
        public String deviceId;
    }
}
