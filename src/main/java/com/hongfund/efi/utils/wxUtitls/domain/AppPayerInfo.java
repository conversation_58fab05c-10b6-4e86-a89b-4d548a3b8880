package com.hongfund.efi.utils.wxUtitls.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.util.Date;

/**
 * 支付者信息表
 * 用于存储微信小程序用户的支付相关信息
 */
@EntityListeners(AuditingEntityListener.class)
@Entity
public class AppPayerInfo {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;
    @CreatedDate
    public Date gmtCreate;
    @LastModifiedDate
    public Date gmtModified;
    
    /**
     * 用户ID
     */
    public Long userId;
    /**
     * 微信openid
     */
    public String openId;
    /**
     * 微信unionid
     */
    public String unionId;
    /**
     * 会话密钥
     */
    public String sessionKey;
    /**
     * 错误码
     */
    public Integer errCode;
    /**
     * 错误信息
     */
    public String errMsg;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
