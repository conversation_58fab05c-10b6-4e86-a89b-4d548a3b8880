package com.hongfund.efi.utils.wxUtitls;

import com.google.gson.annotations.SerializedName;
import okhttp3.*;

import java.io.IOException;
import java.io.UncheckedIOException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.List;

import static com.hongfund.efi.utils.wxUtitls.PaymentHelper.TRADE_TYPE_JSAPI;

/**
 * App下单
 */
public class WxAppPrepay {
  private static String HOST = "https://api.mch.weixin.qq.com";
  private static String METHOD = "POST";
  private static String PATH = "/v3/pay/transactions/app";
  private static String MINI_PATH = "/v3/pay/transactions/jsapi";

  /**
   * 统一下单接口，支持APP支付和小程序支付
   * 根据request.tradeType自动选择对应的API路径
   */
  public DirectAPIv3AppPrepayResponse run(String tradeType, CommonPrepayRequest request) {
    String uri = PATH;
    if (TRADE_TYPE_JSAPI.equals(tradeType)) {
      uri = MINI_PATH;
    }
    String reqBody = WXPayUtility.toJson(request);

    Request.Builder reqBuilder = new Request.Builder().url(HOST + uri);
    reqBuilder.addHeader("Accept", "application/json");
    reqBuilder.addHeader("Wechatpay-Serial", wechatPayPublicKeyId);
    reqBuilder.addHeader("Authorization", WXPayUtility.buildAuthorization(mchid, certificateSerialNo, privateKey, METHOD, uri, reqBody));
    reqBuilder.addHeader("Content-Type", "application/json");
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), reqBody);
    reqBuilder.method(METHOD, requestBody);
    Request httpRequest = reqBuilder.build();

    // 发送HTTP请求
    OkHttpClient client = new OkHttpClient.Builder().build();
    try (Response httpResponse = client.newCall(httpRequest).execute()) {
      String respBody = WXPayUtility.extractBody(httpResponse);
      if (httpResponse.code() >= 200 && httpResponse.code() < 300) {
        // 2XX 成功，验证应答签名
        WXPayUtility.validateResponse(this.wechatPayPublicKeyId, this.wechatPayPublicKey,
                                      httpResponse.headers(), respBody);

        // 从HTTP应答报文构建返回数据
        return WXPayUtility.fromJson(respBody, DirectAPIv3AppPrepayResponse.class);
      } else {
        throw new WXPayUtility.ApiException(httpResponse.code(), respBody, httpResponse.headers());
      }
    } catch (IOException e) {
      throw new UncheckedIOException("Sending request to " + uri + " failed.", e);
    }
  }

  /**
   * 创建支付者信息
   * @param openid 用户openid
   * @return 支付者信息对象
   */
  public static Payer createPayer(String openid) {
    if (openid == null || openid.trim().isEmpty()) {
      return null;
    }
    Payer payer = new Payer();
    payer.openid = openid.trim();
    return payer;
  }

  /** 商户号 */
  private final String mchid;
  /** 证书序列号 */
  private final String certificateSerialNo;
  /** 私钥 */
  private final PrivateKey privateKey;
  /** 微信支付公钥ID */
  private final String wechatPayPublicKeyId;
  /** 微信支付公钥 */
  private final PublicKey wechatPayPublicKey;

  public WxAppPrepay(String mchid, String certificateSerialNo, String privateKey, String wechatPayPublicKeyId, String wechatPayPublicKey) {
    this.mchid = mchid;
    this.certificateSerialNo = certificateSerialNo;
    this.privateKey = WXPayUtility.loadPrivateKeyFromString(privateKey);
    this.wechatPayPublicKeyId = wechatPayPublicKeyId;
    this.wechatPayPublicKey = WXPayUtility.loadPublicKeyFromString(wechatPayPublicKey);
  }

  public static class SettleInfo {
    /** 是否分账 */
    @SerializedName("profit_sharing")
    public Boolean profitSharing;
  }

  public static class CommonAmountInfo {
    /** 总金额 */
    @SerializedName("total")
    public Long total;

    /** 货币类型 */
    @SerializedName("currency")
    public String currency;
  }

  public static class CommonSceneInfo {
    /** 用户端IP */
    @SerializedName("payer_client_ip")
    public String payerClientIp;

    /** 设备ID */
    @SerializedName("device_id")
    public String deviceId;

    /** 门店信息 */
    @SerializedName("store_info")
    public StoreInfo storeInfo;
  }

  public static class DirectAPIv3AppPrepayResponse {
    /** 预支付交易会话标识 */
    @SerializedName("prepay_id")
    public String prepayId;
  }

  public static class GoodsDetail {
    /** 商户商品ID */
    @SerializedName("merchant_goods_id")
    public String merchantGoodsId;

    /** 微信支付商品ID */
    @SerializedName("wechatpay_goods_id")
    public String wechatpayGoodsId;

    /** 商品名称 */
    @SerializedName("goods_name")
    public String goodsName;

    /** 商品数量 */
    @SerializedName("quantity")
    public Long quantity;

    /** 商品单价 */
    @SerializedName("unit_price")
    public Long unitPrice;
  }

  public static class CouponInfo {
    /** 订单原价 */
    @SerializedName("cost_price")
    public Long costPrice;

    /** 发票ID */
    @SerializedName("invoice_id")
    public String invoiceId;

    /** 商品详情 */
    @SerializedName("goods_detail")
    public List<GoodsDetail> goodsDetail;
  }

  public static class StoreInfo {
    /** 门店ID */
    @SerializedName("id")
    public String id;

    /** 门店名称 */
    @SerializedName("name")
    public String name;

    /** 地区编码 */
    @SerializedName("area_code")
    public String areaCode;

    /** 详细地址 */
    @SerializedName("address")
    public String address;
  }

  /**
   * 支付者信息
   * 用于小程序支付和公众号支付
   */
  public static class Payer {
    /** 用户标识 */
    @SerializedName("openid")
    public String openid;

    /** 用户子标识 */
    @SerializedName("sub_openid")
    public String subOpenid;
  }

  public static class CommonPrepayRequest {
    /** 应用ID */
    @SerializedName("appid")
    public String appid;

    /** 商户号 */
    @SerializedName("mchid")
    public String mchid;

    /**【支付者信息】支付者信息*/
    @SerializedName("payer")
    public Payer payer;

    /** 商品描述 */
    @SerializedName("description")
    public String description;

    /** 商户订单号 */
    @SerializedName("out_trade_no")
    public String outTradeNo;

    /** 订单失效时间 */
    @SerializedName("time_expire")
    public String timeExpire;

    /** 附加数据 */
    @SerializedName("attach")
    public String attach;

    /** 通知地址 */
    @SerializedName("notify_url")
    public String notifyUrl;

    /** 订单优惠标记 */
    @SerializedName("goods_tag")
    public String goodsTag;

    /** 是否支持开票 */
    @SerializedName("support_fapiao")
    public Boolean supportFapiao;

    /** 订单金额 */
    @SerializedName("amount")
    public CommonAmountInfo amount;

    /** 优惠功能 */
    @SerializedName("detail")
    public CouponInfo detail;

    /** 场景信息 */
    @SerializedName("scene_info")
    public CommonSceneInfo sceneInfo;

    /** 结算信息 */
    @SerializedName("settle_info")
    public SettleInfo settleInfo;
  }
}