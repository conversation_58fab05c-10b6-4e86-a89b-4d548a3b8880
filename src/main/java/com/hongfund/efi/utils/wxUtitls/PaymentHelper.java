package com.hongfund.efi.utils.wxUtitls;

import org.apache.commons.lang3.StringUtils;

/**
 * 微信支付辅助工具类
 * 提供支付相关的便捷方法
 * 
 * <AUTHOR>
 */
public class PaymentHelper {

    /**
     * 支付类型常量
     */
    public static final String TRADE_TYPE_APP = "APP";           // APP支付
    public static final String TRADE_TYPE_JSAPI = "JSAPI";      // 小程序支付/公众号支付
    public static final String TRADE_TYPE_NATIVE = "NATIVE";    // Native支付
    public static final String TRADE_TYPE_MWEB = "MWEB";        // H5支付

    /**
     * 创建支付者信息
     * @param openid 用户openid
     * @return 支付者信息对象，如果openid无效则返回null
     */
    public static WxAppPrepay.Payer createPayerInfo(String openid) {
        if (!isValidOpenid(openid)) {
            return null;
        }
        return WxAppPrepay.createPayer(openid);
    }

    /**
     * 验证openid格式是否有效
     * @param openid 用户openid
     * @return 是否有效
     */
    public static boolean isValidOpenid(String openid) {
        if (StringUtils.isBlank(openid)) {
            return false;
        }
        // 微信openid通常长度在20-32位之间，包含字母数字和部分特殊字符
        return openid.matches("^[a-zA-Z0-9_-]{20,32}$");
    }

    /**
     * 检查支付类型是否需要支付者信息
     * @param tradeType 交易类型
     * @return 是否需要支付者信息
     */
    public static boolean requiresPayerInfo(String tradeType) {
        return TRADE_TYPE_JSAPI.equals(tradeType);
    }

    /**
     * 验证支付参数
     * @param tradeType 交易类型
     * @param payerOpenid 支付者openid
     * @return 验证结果，null表示验证通过，否则返回错误信息
     */
    public static String validatePaymentParams(String tradeType, String payerOpenid) {
        if (StringUtils.isBlank(tradeType)) {
            return "交易类型不能为空";
        }

        // 检查是否为支持的交易类型
        if (!isSupportedTradeType(tradeType)) {
            return "不支持的交易类型：" + tradeType;
        }

        // 如果是小程序支付或公众号支付，必须提供openid
        if (requiresPayerInfo(tradeType)) {
            if (StringUtils.isBlank(payerOpenid)) {
                return "小程序支付或公众号支付时，支付者openid不能为空";
            }
            if (!isValidOpenid(payerOpenid)) {
                return "支付者openid格式不正确";
            }
        }

        return null; // 验证通过
    }

    /**
     * 检查是否为支持的交易类型
     * @param tradeType 交易类型
     * @return 是否支持
     */
    public static boolean isSupportedTradeType(String tradeType) {
        return TRADE_TYPE_APP.equals(tradeType) ||
               TRADE_TYPE_JSAPI.equals(tradeType) ||
               TRADE_TYPE_NATIVE.equals(tradeType) ||
               TRADE_TYPE_MWEB.equals(tradeType);
    }

    /**
     * 构建支付请求的通用方法
     * @param appid 应用ID
     * @param mchid 商户号
     * @param description 商品描述
     * @param outTradeNo 商户订单号
     * @param notifyUrl 通知地址
     * @param tradeType 交易类型
     * @param payerOpenid 支付者openid（小程序支付时必填）
     * @param totalAmount 支付金额（分）
     * @return 支付请求对象
     */
    public static WxAppPrepay.CommonPrepayRequest buildPaymentRequest(
            String appid, String mchid, String description, String outTradeNo,
            String notifyUrl, String tradeType, String payerOpenid, Long totalAmount) {

        WxAppPrepay.CommonPrepayRequest request = new WxAppPrepay.CommonPrepayRequest();
        request.appid = appid;
        request.mchid = mchid;
        request.description = description;
        request.outTradeNo = outTradeNo;
        request.notifyUrl = notifyUrl;
        tradeType = tradeType != null ? tradeType : TRADE_TYPE_APP;

        // 如果是小程序支付，设置支付者信息
        if (requiresPayerInfo(tradeType)) {
            request.payer = createPayerInfo(payerOpenid);
        }

        // 设置金额信息
        request.amount = new WxAppPrepay.CommonAmountInfo();
        request.amount.total = totalAmount;
        request.amount.currency = "CNY";

        return request;
    }

    /**
     * 从多个可能的openid中选择有效的支付者信息
     * @param openids 可能的openid列表
     * @return 第一个有效的支付者信息，如果都无效则返回null
     */
    public static WxAppPrepay.Payer createPayerInfoFromCandidates(String... openids) {
        if (openids == null) {
            return null;
        }

        for (String openid : openids) {
            if (isValidOpenid(openid)) {
                return createPayerInfo(openid);
            }
        }

        return null;
    }

    /**
     * 批量验证openid
     * @param openids openid数组
     * @return 验证结果数组，对应每个openid的验证结果
     */
    public static boolean[] validateOpenids(String... openids) {
        if (openids == null) {
            return new boolean[0];
        }

        boolean[] results = new boolean[openids.length];
        for (int i = 0; i < openids.length; i++) {
            results[i] = isValidOpenid(openids[i]);
        }

        return results;
    }

    /**
     * 获取有效的openid列表
     * @param openids 待验证的openid数组
     * @return 有效的openid列表
     */
    public static java.util.List<String> getValidOpenids(String... openids) {
        java.util.List<String> validOpenids = new java.util.ArrayList<>();

        if (openids != null) {
            for (String openid : openids) {
                if (isValidOpenid(openid)) {
                    validOpenids.add(openid);
                }
            }
        }

        return validOpenids;
    }

    /**
     * 创建支付者信息的建造者模式
     */
    public static class PayerBuilder {
        private String openid;
        private String subOpenid;

        public PayerBuilder openid(String openid) {
            this.openid = openid;
            return this;
        }

        public PayerBuilder subOpenid(String subOpenid) {
            this.subOpenid = subOpenid;
            return this;
        }

        public WxAppPrepay.Payer build() {
            if (!isValidOpenid(openid)) {
                return null;
            }

            WxAppPrepay.Payer payer = new WxAppPrepay.Payer();
            payer.openid = openid;
            payer.subOpenid = subOpenid;
            return payer;
        }
    }

    /**
     * 创建支付者信息建造者
     * @return PayerBuilder实例
     */
    public static PayerBuilder payerBuilder() {
        return new PayerBuilder();
    }
}
