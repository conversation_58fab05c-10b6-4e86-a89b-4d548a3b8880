package com.hongfund.efi.domain;

import com.hongfund.efi.dto.AccountBookDto;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.util.Date;

/**
 * 风险报告
 */
@EntityListeners(AuditingEntityListener.class)
@Entity
public class TaxReport {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	public Long id;
	@CreatedDate
	public Date gmtCreate;
	@LastModifiedDate
	public Date gmtModified;
	@Version
	public Integer version;

	/**
	 * 订单编号
	 */
	public String orderId;
	/**
	 * 报告版本
	 */
	public String gId;
	/**
	 * 环境
	 */
	public String env;
	/**
	 * ip地址
	 */
	public String ip;
	/**
	 * 验证码
	 */
	public String smsCode;
	/**
	 * 异步任务id
	 */
	public String taskId;
	/**
	 * 账套id
	 */
	public Long accountBookId;
	/**
	 * 账套名称
	 */
	public String accountBookName;
	/**
	 * 税号
	 */
	public String taxCode;

	/**
	 * 电子税务局登录名
	 */
	public String nationalName;
	/**
	 * 电子税局密码
	 */
	public String nationalPwd;
	/**
	 * 办税人手机号
	 */
	public String taxPayerPhone;
	/**
	 * 主管税务机关
	 */
	public String taxAuthorities;
	/**
	 * 验证：方式 0：网页 1：APP
	 */
	public Integer typeAuth = 0;

	/**
	 * 所属省份
	 */
	public String province;
	/**
	 * 所属城市
	 */
	public String city;
	/**
	 * 电子税务局登录方式 1:旧版登录；2：新版登录；3：代理登录；4：代理登录验证
	 */
	public Integer loginWay;
	/**
	 * 代理：社会信用代码
	 */
	public String socialCreditCodeAgent;

	/**
	 * 开始运行时间
	 */
	public Date startTime;
	/**
	 * 区域编号
	 */
	public Integer areaId;
	/**
	 * 操作编号
	 */
	public Integer actionId;
	/**
	 * 优先级
	 */
	public Integer orderLevel = 0;
	/**
	 * 状态
	 */
	public Integer state;
	/**
	 * 结果信息
	 */
	public String resultMessage;
	/**
	 * 结果下载地址
	 */
	public String resultUrl;
	/**
	 * 结果图片地址
	 */
	public String resultImageUrl;
	/**
	 * 结果数据json
	 */
	public String resultJsonUrl;
	/**
	 * 结果数据json
	 */
	public String resultPdfUrl;
	/**
	 * 结果数据json2
	 */
	public String resultJsonUrl2;
	/**
	 * 结果数据json2
	 */
	public String resultPdfUrl2;
	/**
	 * 结果数据json3
	 */
	public String resultJsonUrl3;
	/**
	 * 结果数据json3
	 */
	public String resultPdfUrl3;
	/**
	 * 结果数据json4
	 */
	public String resultJsonUrl4;
	/**
	 * 结果数据json4
	 */
	public String resultPdfUrl4;
	/**
	 * 结果数据json5
	 */
	public String resultJsonUrl5;
	/**
	 * 结果数据json5
	 */
	public String resultPdfUrl5;
	/**
	 * 来源 0：财务软件 1：小程序 2：app
	 */
	public Integer source = 0;

	public TaxReport() {
	}

	public AccountBookDto createBook() {
		AccountBookDto accountBookDto = new AccountBookDto();
		accountBookDto.bookName = this.accountBookName;
		accountBookDto.accountBookInfo = new AccountBookInfo();
		accountBookDto.accountBookInfo.accountBookName = this.accountBookName;
		accountBookDto.accountBookInfo.socialCreditCode = this.taxCode;
		accountBookDto.accountBookInfo.province = this.province;
		accountBookDto.accountBookInfo.city = this.city;
		accountBookDto.accountBookInfo.taxAuthorities = this.taxAuthorities;
		accountBookDto.accountBookInfo.loginWay = this.loginWay;
		accountBookDto.accountBookInfo.socialCreditCodeAgent = this.socialCreditCodeAgent;
		if (this.loginWay == 3) {
			accountBookDto.accountBookInfo.nationalNameAgent = this.nationalName;
			accountBookDto.accountBookInfo.nationalPwdAgent = this.nationalPwd;
			accountBookDto.accountBookInfo.taxPayerPhoneAgent = this.taxPayerPhone;
		}
		else {
			accountBookDto.accountBookInfo.nationalNameNew = this.nationalName;
			accountBookDto.accountBookInfo.nationalPwdNew = this.nationalPwd;
			accountBookDto.accountBookInfo.taxPayerPhoneNew = this.taxPayerPhone;
		}
		return accountBookDto;
	}

	public void parseData(AccountBookInfo accountBookInfo, AccountBook accountBook) {
		this.accountBookId = accountBook.id;
		this.accountBookName = accountBook.bookName;
		this.taxCode = accountBookInfo.socialCreditCode;
		this.taxAuthorities = accountBookInfo.taxAuthorities;
		this.province = accountBookInfo.province;
		this.city = accountBookInfo.city;
		this.loginWay = accountBookInfo.loginWay;
		this.socialCreditCodeAgent = accountBookInfo.socialCreditCodeAgent;
		if (accountBookInfo.loginWay == 3) {
			this.nationalName = accountBookInfo.nationalNameAgent;
			this.nationalPwd = accountBookInfo.nationalPwdAgent;
			this.taxPayerPhone = accountBookInfo.taxPayerPhoneAgent;
		}
		else if (accountBookInfo.loginWay == 1) {
			this.nationalName = accountBookInfo.nationalName;
			this.nationalPwd = accountBookInfo.nationalPwd;
			this.taxPayerPhone = accountBookInfo.taxPayerPhone;
		}
		else {
			this.nationalName = accountBookInfo.nationalNameNew;
			this.nationalPwd = accountBookInfo.nationalPwdNew;
			this.taxPayerPhone = accountBookInfo.taxPayerPhoneNew;
		}
	}

	public void parseData(RpaCookieTask rpaCookieTask) {
		this.accountBookId = rpaCookieTask.accountBookId;
		this.accountBookName = rpaCookieTask.accountBookName;
		this.taxCode = rpaCookieTask.taxCode;
		this.taxAuthorities = rpaCookieTask.taxAuthorities;
		this.nationalName = rpaCookieTask.nationalName;
		this.nationalPwd = rpaCookieTask.nationalPwd;
		this.taxPayerPhone = rpaCookieTask.taxPayerPhone;
		this.province = rpaCookieTask.province;
		this.city = rpaCookieTask.city;
		this.loginWay = rpaCookieTask.loginWay;
		this.socialCreditCodeAgent = rpaCookieTask.socialCreditCodeAgent;
		this.typeAuth = rpaCookieTask.typeAuth;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
