package com.hongfund.efi.domain;

import java.util.Date;

import javax.persistence.*;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@EntityListeners(AuditingEntityListener.class)
@Entity
public class AppUser {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;
    @CreatedDate
    public Date gmtCreate;
    @LastModifiedDate
    public Date gmtModified;
    public String phone;
    public String reference;// 推荐人
    public String referenceCompany;// 推荐人公司
    public String registerTime;// 注册时间
    public Long companyId;// 公司id
    public Date  aiEndTime;//ai到期时间
    public Date manualConsultEndTime;//人工税务咨询到期时间
}