package com.hongfund.efi.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单表
 */
@Entity
@EntityListeners(AuditingEntityListener.class)
public class AppOrders {

    // JPA 主键标识, 策略为由数据库生成主键
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;
    @CreatedDate
    public Date gmtCreate;
    @LastModifiedDate
    public Date gmtModified;
    /**
     * 订单编号
     */
    public String orderNo;
    /**
     * 用户ID
     */
    public Long userId;
    /**
     * 公司ID
     */
    public Long companyId;
    /**
     * 服务ID
     */
    public Long serviceId;
    /**
     * 服务名称
     */
    public String serviceName;
    /**
     * 订单金额
     */
    public BigDecimal amount;
    /**
     * 订单状态：1-待支付，2-已支付，3-已取消
     */
    public Integer orderStatus = 1;
    /**
     * 交易流水号
     */
    public String transactionId;
    /**
     * 支付时间
     */
    public Date paymentTime;
    /**
     * 取消时间
     */
    public Date cancelTime;
    /**
     * 交易类型
     * JSAPI：公众号支付、小程序支付
     * NATIVE：Native支付
     * APP：APP支付
     * MICROPAY：付款码支付
     * MWEB：H5支付
     * FACEPAY：刷脸支付
     */
    public String tradeType;
    /**
     * 报告状态
     */
    public Integer reportStatus;
    /**
     * 报告下载地址
     */
    public String reportUrl;
    /**
     * 结果信息
     */
    public String resultMsg;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
