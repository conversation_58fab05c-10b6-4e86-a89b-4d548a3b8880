package com.hongfund.efi.service;

import com.csvreader.CsvReader;
import com.hongfund.efi.domain.*;
import com.hongfund.efi.dto.*;
import com.hongfund.efi.profile.TaxRecordArea;
import com.hongfund.efi.repository.*;
import com.hongfund.efi.service.exception.ErrorCode;
import com.hongfund.efi.service.exception.ServiceException;
import com.hongfund.efi.utils.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.hongfund.efi.profile.TaxRecordAction.TAX_REPORT;
import static com.hongfund.efi.profile.TaxRecordArea.getByProvinceAndBookName;
import static com.hongfund.efi.profile.TaxRecordState.*;
import static com.hongfund.efi.service.AiHelperService.matchRpaCookieState;
import static com.hongfund.efi.utils.PeriodUtils.getDateStr;
import static java.util.Calendar.DAY_OF_MONTH;

@Service
@Transactional(rollbackFor = Exception.class)
public class TaxReportService {

    @Value("${spring.profiles.active}")
    private String active;
    @Autowired
    private AccountBookDao accountBookDao;
    @Autowired
    private AccountBookInfoDao accountBookInfoDao;
    @Autowired
    private AccountDao accountDao;
    @Autowired
    private TaxReportDao taxReportDao;
    @Autowired
    private TaxReportDataDao taxReportDataDao;
    @Autowired
    private UibotSettingDao uibotSettingDao;
    @Autowired
    private RpaCookieTaskDao rpaCookieTaskDao;
    @Autowired
    private RpaCookieStateDao rpaCookieStateDao;

    @Autowired
    private TaxNationalNameService taxNationalNameService;
    @Autowired
    private QxyOrderService qxyOrderService;
    @Autowired
    private AccountBookService accountBookService;
    @Autowired
    private RestTemplate restTemplate;


    /**
     * 查询汇算清缴所需信息
     */
    public ResponseDto info(String areaIds, String bookName, Integer actionId, HttpServletRequest request) {
        String[] areaIdArray = StringUtils.split(areaIds, ",");
        List<Integer> areaIdList = new ArrayList<>();
        for (String areaId : areaIdArray) {
            areaIdList.add(Integer.valueOf(areaId));
        }

        TaxReport taxReport;
        if (StringUtils.isNotBlank(bookName)) {
            AccountBook accountBook = accountBookDao.findByBookName(bookName);
            if (accountBook == null) {
                return new ResponseDto(1, "找不到该账套", null);
            }
            taxReport = taxReportDao.findFirstByStateAndAreaIdInAndAccountBookIdAndActionIdOrderByGmtModifiedAsc(WAITING, areaIdList, accountBook.id, actionId);
        } else {
            List<String> names = taxNationalNameService.findLongNames();
            List<TaxReport> temp = taxReportDao.findCompanyWaiting(areaIdList, actionId, names, PageRequest.of(0, 1));
            if (temp.isEmpty()) {
                return new ResponseDto(1, "没有等待任务", null);
            }
            taxReport = temp.get(0);
        }

        if (taxReport == null) {
            return new ResponseDto(1, "没有等待任务", null);
        }
        AccountBook accountBook = accountBookDao.findAccountBookById(taxReport.accountBookId);
        if (accountBook == null) {
            taxReportDao.delete(taxReport);
            return new ResponseDto(1, "该账套已删除", null);
        }
        if (StringUtils.isNotBlank(taxReport.taxPayerPhone)) {
            taxNationalNameService.lockTaxNationalName(taxReport.taxPayerPhone, 360);
        }
        taxReport.state = RUNNING;
        taxReport.ip = IpUtils.getIp(request);
        taxReport.startTime = new Date();
        taxReportDao.save(taxReport);
        TaxReportDto result = BeanMapper.map(taxReport, TaxReportDto.class);
        result.accountBookDto = BeanMapper.map(accountBook, AccountBookDto.class);
        result.accountBookDto.accountBookInfo = accountBookInfoDao.findByAccountBookId(accountBook.id);
        return new ResponseDto(200, "获取成功", result);
    }

    /**
     * 设置汇算清缴报税结果
     */
    public ResponseDto setResult(TaxReportDto taxReportDto, HttpServletRequest request) {
        TaxReport taxReport = taxReportDao.findTaxReportById(taxReportDto.id);
        if (taxReport == null) {
            throw new ServiceException("找不到报税记录，无法设置", ErrorCode.BAD_REQUEST);
        }
        if (taxReportDto.state == null) {
            taxReportDto.state = FINISHED;
        }

        taxReport.state = taxReportDto.state;
        taxReport.resultUrl = taxReportDto.resultUrl;
        taxReport.resultImageUrl = taxReportDto.resultImageUrl;
        taxReport.resultMessage = taxReportDto.resultMessage;
        taxReport.resultJsonUrl = taxReportDto.resultJsonUrl;
        taxReport.resultPdfUrl = taxReportDto.resultPdfUrl;
        taxReport.resultJsonUrl2 = taxReportDto.resultJsonUrl2;
        taxReport.resultPdfUrl2 = taxReportDto.resultPdfUrl2;
        taxReport.resultJsonUrl3 = taxReportDto.resultJsonUrl3;
        taxReport.resultPdfUrl3 = taxReportDto.resultPdfUrl3;
        taxReport.resultJsonUrl4 = taxReportDto.resultJsonUrl4;
        taxReport.resultPdfUrl4 = taxReportDto.resultPdfUrl4;
        taxReport.resultJsonUrl5 = taxReportDto.resultJsonUrl5;
        taxReport.resultPdfUrl5 = taxReportDto.resultPdfUrl5;
        taxReport.ip = IpUtils.getIp(request);
        taxReportDao.save(taxReport);
        postOrder(taxReport);
        return new ResponseDto(200, "设置成功", null);
    }

    /**
     * 推送报告
     */
    public void postOrder(TaxReport taxReport) {
        String stateStr = "";
        if (taxReport.state == FINISHED) {
            stateStr = "succeed";
        }
        if (taxReport.state == ERROR) {
            stateStr = "fail";
        }
        if (StringUtils.isNotBlank(stateStr) && StringUtils.isNotBlank(taxReport.orderId)) {
            // 使用Map作为请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("orderId", taxReport.orderId);
            requestBody.put("reportUrl", taxReport.resultPdfUrl2);
            requestBody.put("state", stateStr);
            requestBody.put("remark", taxReport.resultMessage);
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            // 创建请求实体
            HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(requestBody, headers);
            String url;
            if (StringUtils.startsWith(active, "jzs")) {
                url = "https://apis.jinzhangshi.com/openApi/upJzjOrderReportUrl";
            }
            else {
                url = "https://api.hongfund.com/notify/service_mall_report.php";
            }
            ResponseDto2 responseDto2 = restTemplate.postForObject(url, httpEntity, ResponseDto2.class);
            System.out.println(responseDto2);
        }
    }

    public ResponseDto retryTaxReports(TaxReportDto TaxReportDto) {
        TaxReport taxReport = taxReportDao.findTaxReportById(TaxReportDto.id);
        if (taxReport != null) {
            AccountBook accountBook = accountBookDao.findAccountBookById(taxReport.accountBookId);
            AccountBookInfo accountBookInfo = accountBookInfoDao.findByAccountBookId(accountBook.id);
            TaxRecordArea taxRecordArea = getByProvinceAndBookName(accountBookInfo.province, accountBook.bookName);
            if (taxRecordArea == null) {
                return new ResponseDto(20000, "该账套不在支持区域内", null);
            }

            UibotSetting uibotSetting = uibotSettingDao.findByAreaIdAndActionId(taxRecordArea.areaId, taxReport.actionId);
            if (uibotSetting == null || uibotSetting.isOnline == null || uibotSetting.isOnline != 1) {
                return new ResponseDto(20000, "配置未上线", null);
            }
            taxReport.resultMessage = null;
            taxReport.state = WAITING;
            taxReport.orderLevel = 0;
            taxReport.gmtCreate = new Date();
            taxReport.startTime = null;
            taxReport.resultUrl = null;
            taxReport.resultImageUrl = null;
            taxReport.resultJsonUrl = null;
            taxReport.resultPdfUrl = null;
            taxReport.resultJsonUrl2 = null;
            taxReport.resultPdfUrl2 = null;
            taxReport.resultJsonUrl3 = null;
            taxReport.resultPdfUrl3 = null;
            taxReport.resultJsonUrl4 = null;
            taxReport.resultPdfUrl4 = null;
            taxReport.resultJsonUrl5 = null;
            taxReport.resultPdfUrl5 = null;
            taxReportDao.save(taxReport);
            return new ResponseDto(20000, "成功", null);
        } else {
            return new ResponseDto(1, "找不到记录", null);
        }
    }

    /**
     * 一键申报
     */
    public ResponseDto saveTaxReport(TaxReportDto taxReportDto) {
        TaxReport taxReport = new TaxReport();
        taxReport.actionId = TAX_REPORT.actionId;
        taxReport.state = WAITING;
        taxReport.gId = taxReportDto.gId;
        taxReport.orderId = taxReportDto.orderId;
        taxReport.source = taxReportDto.source;

        AccountBook accountBook;
        if (taxReportDto.accountBookId != null) {
            accountBook = accountBookDao.findAccountBookById(taxReportDto.accountBookId);
        }
        else {
            accountBook = accountBookService.getAccountBook(taxReportDto.bookName, taxReportDto.partyId);
        }
        if (accountBook != null) {
            AccountBookInfo accountBookInfo = accountBookInfoDao.findByAccountBookId(accountBook.id);
            String managerNo = StringUtils.substring(accountBook.accounterNo, 0, 6);
            Account adminAccount = accountDao.findByNo(managerNo);
            taxReport.typeAuth = adminAccount.typeAuth;
            if (qxyOrderService.isOpen(accountBook.accounterNo, accountBookInfo.socialCreditCode)) {
                taxReport.typeAuth = 1;
            }
            taxReport.parseData(accountBookInfo, accountBook);
        }
        if (taxReportDto.source != 0) {
            List<RpaCookieTask> oldList = rpaCookieTaskDao.findOld(taxReportDto.bookName, taxReportDto.source, PageRequest.of(0,1));
            if (!oldList.isEmpty()) {
                RpaCookieTask old = oldList.get(0);
                taxReport.parseData(old);
            }
        }
        TaxRecordArea taxRecordArea = getByProvinceAndBookName(taxReport.province, taxReport.accountBookName);
        if (taxRecordArea == null) {
            return new ResponseDto(1, "该账套不在支持区域内：" + taxReport.accountBookName, null);
        }
        taxReport.areaId = taxRecordArea.areaId;
        UibotSetting uibotSetting = uibotSettingDao.findByAreaIdAndActionId(taxRecordArea.areaId, TAX_REPORT.actionId);
        if (uibotSetting == null || uibotSetting.isOnline == null || uibotSetting.isOnline != 1) {
            return new ResponseDto(1, "配置未上线", null);
        }
        taxReportDao.save(taxReport);
        return new ResponseDto(200, "成功", null);
    }

    public ResponseDto deleteTaxReports(TaxReportDto taxReportDto) {
        TaxReport taxReport = taxReportDao.findTaxReportById(taxReportDto.id);
        if (taxReport != null) {
            taxReport.state = UN_START;
            taxReportDao.delete(taxReport);
            return new ResponseDto(20000, "成功", null);
        } else {
            return new ResponseDto(1, "找不到记录", null);
        }
    }

    /**
     * 检测报告账套列表
     */
    public Page<TaxReportBookDto> listTaxReports(Long accountId, String accounterNo, String query, String addtax, Integer loginWay,
                                                 Integer authState, String startPeriod, String endPeriod, Integer taxReportState, Boolean isSortByCode, Pageable pageable) {
        Account account = accountDao.findOne(accountId);

        // 参数处理
        if (StringUtils.isBlank(query)) {
            query = "";
        }
        if (StringUtils.isBlank(addtax)) {
            addtax = "";
        }
        if (StringUtils.isBlank(accounterNo)) {
            accounterNo = account.no + "%";
        }

        // 处理日期筛选
        Date startDate = null;
        Date endDate = null;
        if (StringUtils.isNotBlank(startPeriod)) {
            Date date = FormatUtils.parse(startPeriod);
            startDate = DateUtils.truncate(date, DAY_OF_MONTH);
        }
        if (StringUtils.isNotBlank(endPeriod)) {
            Date date = FormatUtils.parse(endPeriod);
            endDate = DateUtils.ceiling(date, DAY_OF_MONTH);
        }

        Sort sort;
        if (StringUtils.startsWith(active, "jzs")) {
            if (isSortByCode) {
                sort = Sort.by("bookCode").ascending()
                        .and(Sort.by("id").ascending());
            } else {
                sort = Sort.by("id").ascending();
            }
        } else {
            sort = Sort.by("accountStatus").ascending()
                    .and(Sort.by("id").ascending());
        }

        Page<TaxReportBookDto> result;
        // 根据用户级别进行不同的查询
        if (account.level == 1) {
            // 管理员级别，直接查询
            result = accountBookDao.findTaxReportBooks(accounterNo, query, addtax, loginWay, startDate, endDate, PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), sort));
            for (TaxReportBookDto taxReportBook : result.getContent()) {
                if (taxReportBook.taxReportState == null) {
                    taxReportBook.taxReportState = 1;
                }
            }
        } else {
            // 非管理员级别，需要进行额外的权限和状态过滤
            result = accountBookDao.findTaxReportBooks(accounterNo, query, addtax, loginWay, startDate, endDate, PageRequest.of(0, 10000, sort));

            Set<String> nameSet = new HashSet<>();
            for (TaxReportBookDto taxReportBook : result.getContent()) {
                nameSet.add(taxReportBook.nationalNameFinal);
            }

            // 查询RPA Cookie状态
            List<RpaCookieState> rpaCookieStateList = rpaCookieStateDao.findBySocialCreditCodeIn(nameSet);

            // 设置验证状态
            for (TaxReportBookDto taxReportBook : result.getContent()) {
                // 匹配RPA Cookie状态
                RpaCookieState rpaCookieState = matchRpaCookieState(rpaCookieStateList, taxReportBook.nationalNameFinal, taxReportBook.loginWay);
                if (rpaCookieState != null) {
                    taxReportBook.authState = rpaCookieState.state;
                }
            }
            // 根据权限状态进行过滤
            List<TaxReportBookDto> filteredData = result.getContent().stream()
                    .filter(taxReportBook -> authState == null || (taxReportBook.authState != null && taxReportBook.authState.equals(authState)))
                    .collect(Collectors.toList());

            List<Long> ids = new ArrayList<>();
            for (TaxReportBookDto taxReportBook : result) {
                ids.add(taxReportBook.accountBookId);
            }
            List<TaxReport> taxReports = taxReportDao.findByAccountBookIdIn(ids);
            Map<Long, List<TaxReport>> taxReportMap = new HashMap<>();
            for (TaxReport taxReport : taxReports) {
                if (taxReportMap.containsKey(taxReport.accountBookId)) {
                    taxReportMap.get(taxReport.accountBookId).add(taxReport);
                } else {
                    List<TaxReport> temp = new ArrayList<>();
                    temp.add(taxReport);
                    taxReportMap.put(taxReport.accountBookId, temp);
                }
            }

            Date now = new Date();
            Date twoMonthsAgo = DateUtils.addMonths(now, -2);
            // 根据任务日期，判断生成状态
            for (TaxReportBookDto taxReportBook : filteredData) {
                List<TaxReport> list = taxReportMap.getOrDefault(taxReportBook.accountBookId, new ArrayList<>());
                taxReportBook.reports.addAll(BeanMapper.mapList(list, TaxReportDto.class));
                // 是否2个月内有任务
                TaxReport latestTask = getLatestTask(list, twoMonthsAgo);
                if (latestTask != null) {
                    taxReportBook.taxReportState = latestTask.state;
                    if (StringUtils.isNotBlank(latestTask.gId) && StringUtils.equals(latestTask.gId, "b2")) {
                        taxReportBook.taxReportState = 1;
                    }
                } else {
                    taxReportBook.taxReportState = 1;
                }
            }

            // 根据生成状态进行过滤
            filteredData = filteredData.stream()
                    .filter(taxReportBook -> taxReportState == null || (taxReportBook.taxReportState != null && taxReportBook.taxReportState.equals(taxReportState)))
                    .collect(Collectors.toList());

            // 使用内存分页器
            RamPager<TaxReportBookDto> pager = new RamPager<>(filteredData, pageable);
            result = pager.pageResult();
        }

        return result;
    }

    // 根据时间获取最新的任务
    public static TaxReport getLatestTask(List<TaxReport> taxReports, Date date) {
        TaxReport result = null;
        for (TaxReport taxReport : taxReports) {
            if (taxReport.gmtCreate.compareTo(date) >= 0 && (result == null || result.gmtCreate.getTime() < taxReport.gmtCreate.getTime())) {
                result = taxReport;
            }
        }
		return result;
	}

    /**
     * 保存风险报告数据
     */
    public ResponseDto saveTaxReportData(List<TaxReportDataDto> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return new ResponseDto(1, "没有数据", null);
        }
        String taxCode = dataList.get(0).taxCode;
        List<TaxReportData> list = taxReportDataDao.findByTaxCode(taxCode);
        List<TaxReportData> saveList = new ArrayList<>();
        for (TaxReportDataDto taxReportDataDto : dataList) {
            TaxReportData taxReportData = findByType(taxReportDataDto.type, list);
            if (taxReportData == null) {
                taxReportData = new TaxReportData();
                taxReportData.taxCode = taxCode;
                taxReportData.type = taxReportDataDto.type;
            }
            taxReportData.dataUrl = taxReportDataDto.dataUrl;
            saveList.add(taxReportData);
        }
        taxReportDataDao.saveAll(saveList);
        return new ResponseDto(200, "保存成功", null);
    }

    /**
     * 查询风险报告数据
     */
    public ResponseDto findTaxReportData(String taxCode, String type, String startPeriod, String endPeriod) {
        ArrayList<String[]> dataList = findTaxReportData(taxCode, type);
        ArrayList<String[]> result = new ArrayList<>();
        if (dataList != null && !dataList.isEmpty()) {
            String[] firstRow = dataList.get(0);
            result.add(firstRow);
            int startPeriodIndex = findIndex(firstRow, new String[]{"开票日期", "所属期起"});
            int endPeriodIndex = findIndex(firstRow, new String[]{"开票日期", "所属期止"});
            for (String[] row : dataList) {
                if (row.length <= startPeriodIndex || row.length <= endPeriodIndex) {
                    continue;
                }
                String rowStartPeriod = StringUtils.substring(getDateStr(row[startPeriodIndex]), 0, 6);
                String rowEndPeriod = StringUtils.substring(getDateStr(row[endPeriodIndex]), 0, 6);
                if (StringUtils.isBlank(rowStartPeriod) || StringUtils.isBlank(rowEndPeriod)) {
                    continue;
                }
                if (StringUtils.isNotBlank(endPeriod) && StringUtils.compare(rowStartPeriod, endPeriod) > 0) {
                    continue;
                }
                if (StringUtils.isNotBlank(startPeriod) && StringUtils.compare(rowEndPeriod, startPeriod) < 0) {
                    continue;
                }
                result.add(row);
            }
        }
        return new ResponseDto(200, "查询成功", result);
    }

    /**
     * 查询首页年度数据
     * 取利润表本年累计
     */
    public ResponseDto findHomeYear(String taxCode, String year) {
        ArrayList<String[]> dataList = findTaxReportData(taxCode, "利润表");
        // 本年累计盈利
        BigDecimal yl = BigDecimal.ZERO;
        String ylPeriod = null;
        // 总收入
        BigDecimal sr = BigDecimal.ZERO;
        String srPeriod = null;
        // 总支出
        BigDecimal zc = BigDecimal.ZERO;
        String zcPeriod = null;
        if (dataList != null && !dataList.isEmpty()) {
            String[] firstRow = dataList.get(0);
            int startPeriodIndex = findIndex(firstRow, new String[]{"所属期起"});
            int endPeriodIndex = findIndex(firstRow, new String[]{"所属期止"});
            int itemIndex = findIndex(firstRow, new String[]{"项目"});
            int fileNameIndex = findIndex(firstRow, new String[]{"文件名"});
            int monthIndex = findIndex(firstRow, new String[]{"本月金额"});
            int yearIndex = findIndex(firstRow, new String[]{"本年累计金额"});
            for (String[] row : dataList) {
                if (row.length <= startPeriodIndex || row.length <= endPeriodIndex) {
                    continue;
                }
                String rowStartPeriod = StringUtils.substring(getDateStr(row[startPeriodIndex]), 0, 6);
                String rowEndPeriod = StringUtils.substring(getDateStr(row[endPeriodIndex]), 0, 6);
                if (StringUtils.isBlank(rowStartPeriod) || StringUtils.isBlank(rowEndPeriod)) {
                    continue;
                }
                if (!StringUtils.startsWith(rowEndPeriod, year)) {
                    continue;
                }
                // 去掉年报
                if (PeriodUtils.getDifference(rowStartPeriod, rowEndPeriod) == 12) {
                    continue;
                }
                String item = row[itemIndex];
                if (StringUtils.contains(item, "三、利润总额")) {
                    if (ylPeriod == null || StringUtils.compare(ylPeriod, rowEndPeriod) < 0) {
                        ylPeriod = rowEndPeriod;
                    }
                    yl = countBalance(row, fileNameIndex, monthIndex, yearIndex);
                }
                if (StringUtils.contains(item, "一、营业收入")) {
                    if (srPeriod == null || StringUtils.compare(srPeriod, rowEndPeriod) < 0) {
                        srPeriod = rowEndPeriod;
                    }
                    sr = countBalance(row, fileNameIndex, monthIndex, yearIndex);
                }
                if (StringUtils.contains(item, "减：营业成本")) {
                    if (zcPeriod == null || StringUtils.compare(zcPeriod, rowEndPeriod) < 0) {
                        zcPeriod = rowEndPeriod;
                    }
                    zc = countBalance(row, fileNameIndex, monthIndex, yearIndex);
                }
            }
        }
        Map<String, Object> result = new HashMap<>();
        result.put("累计盈利", yl);
        result.put("总收入", sr);
        result.put("总支出", zc);
        return new ResponseDto(200, "查询成功", result);
    }

    /**
     * 查询首页季度数据
     * 取资产负债表和发票数据
     */
    public ResponseDto findHomeQuarter(String taxCode, String startPeriod, String endPeriod) {
        ArrayList<String[]> invoiceList = findTaxReportData(taxCode, "发票标准数据");
        // 应收账款
        BigDecimal yszk = BigDecimal.ZERO;
        // 应付账款
        BigDecimal yfzk = BigDecimal.ZERO;
        // 销项金额
        BigDecimal xxje = BigDecimal.ZERO;
        // 销项税额
        BigDecimal xxse = BigDecimal.ZERO;
        // 销项数量
        int xxsl = 0;
        // 进项金额
        BigDecimal jxje = BigDecimal.ZERO;
        // 进项税额
        BigDecimal jxse = BigDecimal.ZERO;
        // 进项数量
        int jxsl = 0;
        // 实缴税金
        BigDecimal sjsj = BigDecimal.ZERO;
        if (invoiceList != null && !invoiceList.isEmpty()) {
            String[] firstRow = invoiceList.get(0);
            int periodIndex = findIndex(firstRow, new String[]{"开票日期"});
            int dirIndex = findIndex(firstRow, new String[]{"发票方向"});
            int stateIndex = findIndex(firstRow, new String[]{"发票状态"});
            int jeIndex = findIndex(firstRow, new String[]{"金额"});
            int seIndex = findIndex(firstRow, new String[]{"税额"});
            int gxStateIndex = findIndex(firstRow, new String[]{"勾选状态"});
            int fphmIndex = findIndex(firstRow, new String[]{"发票号码"});
            for (String[] row : invoiceList) {
                String period = StringUtils.substring(getDateStr(row[periodIndex]), 0, 6);
                if (StringUtils.isBlank(period)) {
                    continue;
                }
                if (StringUtils.compare(period, startPeriod) < 0 || StringUtils.compare(period, endPeriod) > 0) {
                    continue;
                }
                String state = row[stateIndex];
                if (!StringUtils.equalsAny(state, "正常", "已红冲-全额", "已红冲-部分")) {
                    continue;
                }
                String dir = row[dirIndex];
                if (StringUtils.equals(dir, "进项")) {
                    String gxState = row[gxStateIndex];
                    if (!StringUtils.equals(gxState, "已勾选")) {
                        continue;
                    }
                    BigDecimal rowJxje = BigDecimalUtils.getDecimalValue(row[jeIndex]);
                    BigDecimal rowJxse = BigDecimalUtils.getDecimalValue(row[seIndex]);
                    jxje = jxje.add(rowJxje);
                    jxse = jxse.add(rowJxse);
                    jxsl++;
                }
                else {
                    BigDecimal rowXxje = BigDecimalUtils.getDecimalValue(row[jeIndex]);
                    BigDecimal rowXxse = BigDecimalUtils.getDecimalValue(row[seIndex]);
                    xxje = xxje.add(rowXxje);
                    xxse = xxse.add(rowXxse);
                    String fphm = row[fphmIndex];
                    if (!StringUtils.contains(fphm, "-")) {
                        xxsl++;
                    }
                }
            }
        }

        ArrayList<String[]> balanceSheetList = findTaxReportData(taxCode, "资产负债表");
        if (balanceSheetList != null && !balanceSheetList.isEmpty()) {
            String[] firstRow = balanceSheetList.get(0);
            int itemIndex = findIndex(firstRow, new String[]{"项目"});
            int balanceIndex = findIndex(firstRow, new String[]{"期末余额"});
            int startPeriodIndex = findIndex(firstRow, new String[]{"所属期起"});
            int endPeriodIndex = findIndex(firstRow, new String[]{"所属期止"});
            for (String[] row : balanceSheetList) {
                String rowStartPeriod = StringUtils.substring(getDateStr(row[startPeriodIndex]), 0, 6);
                String rowEndPeriod = StringUtils.substring(getDateStr(row[endPeriodIndex]), 0, 6);
                if (StringUtils.isBlank(rowStartPeriod) || StringUtils.isBlank(rowEndPeriod)) {
                    continue;
                }
                if (!StringUtils.equals(rowStartPeriod, startPeriod) || !StringUtils.equals(rowEndPeriod, endPeriod)) {
                    continue;
                }
                String item = row[itemIndex];
                if (StringUtils.equals(item, "应收账款")) {
                    yszk = BigDecimalUtils.getDecimalValue(row[balanceIndex]);
                }
                if (StringUtils.equals(item, "应付账款")) {
                    yfzk = BigDecimalUtils.getDecimalValue(row[balanceIndex]);
                }
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("应收账款", yszk);
        result.put("应付账款", yfzk);
        result.put("销项金额", xxje);
        result.put("销项税额", xxse);
        result.put("销项数量", xxsl);
        result.put("进项金额", jxje);
        result.put("进项税额", jxse);
        result.put("进项数量", jxsl);
        result.put("实缴税金", sjsj);
        return new ResponseDto(200, "查询成功", result);
    }

    private BigDecimal countBalance(String[] row, int fileNameIndex, int monthIndex, int yearIndex) {
        String fileName = row[fileNameIndex];
        if (StringUtils.contains(fileName, "小企业会计准则")) {
            return BigDecimalUtils.getDecimalValue(row[yearIndex]);
        }
        else {
            return BigDecimalUtils.getDecimalValue(row[monthIndex]);
        }
    }

    private int findIndex(String[] row, String[] nameArray) {
        for (int i = 0; i < row.length; i++) {
            for (String name : nameArray) {
                if (StringUtils.equals(name, row[i])) {
                    return i;
                }
            }
        }
        return -1;
    }

    private TaxReportData findByType(String type, List<TaxReportData> list) {
        for (TaxReportData taxReportData : list) {
            if (StringUtils.equals(type, taxReportData.type)) {
                return taxReportData;
            }
        }
        return null;
    }

    private ArrayList<String[]> findTaxReportData(String taxCode, String type) {
        TaxReportData taxReportData = taxReportDataDao.findByTaxCodeAndType(taxCode, type);
        if (taxReportData == null || StringUtils.isBlank(taxReportData.dataUrl)) {
            return null;
        }
        ArrayList<String[]> dataList = new ArrayList<>();
        ResponseEntity<byte[]> data = restTemplate.getForEntity(taxReportData.dataUrl, byte[].class);
        CsvReader reader = new CsvReader(new ByteArrayInputStream(Objects.requireNonNull(data.getBody())), ',', StandardCharsets.UTF_8);
        try {
            while (reader.readRecord()) {
                dataList.add(reader.getValues());
            }
            reader.close();
        }
        catch (Exception e) {
            return null;
        }
        if (!dataList.isEmpty()) {
            String[] firstRow = dataList.get(0);
            if (firstRow.length > 0) {
                firstRow[0] = StringUtils.removeStart(firstRow[0], "\uFEFF");
            }
        }
        return dataList;
    }

    /**
     * 查询利润表
     */
    public ResponseDto findIncome(String taxCode, String startPeriod, String endPeriod) {
        List<TaxReportDataIncomeDto> result = new ArrayList<>();
        ArrayList<String[]> dataList = findTaxReportData(taxCode, "利润表");
        if (dataList != null && !dataList.isEmpty()) {
            String[] firstRow = dataList.get(0);
            int startPeriodIndex = findIndex(firstRow, new String[]{"所属期起"});
            int endPeriodIndex = findIndex(firstRow, new String[]{"所属期止"});
            int itemIndex = findIndex(firstRow, new String[]{"项目"});
            int fileNameIndex = findIndex(firstRow, new String[]{"文件名"});
            int monthIndex = findIndex(firstRow, new String[]{"本月金额"});
            int yearIndex = findIndex(firstRow, new String[]{"本年累计金额"});
            for (String[] row : dataList) {
                if (row.length <= startPeriodIndex || row.length <= endPeriodIndex) {
                    continue;
                }
                String rowStartPeriod = StringUtils.substring(getDateStr(row[startPeriodIndex]), 0, 6);
                String rowEndPeriod = StringUtils.substring(getDateStr(row[endPeriodIndex]), 0, 6);
                if (StringUtils.isBlank(rowStartPeriod) || StringUtils.isBlank(rowEndPeriod)) {
                    continue;
                }
                if (!StringUtils.equals(rowStartPeriod, startPeriod) || !StringUtils.equals(rowEndPeriod, endPeriod)) {
                    continue;
                }
                TaxReportDataIncomeDto data = new TaxReportDataIncomeDto();
                data.name = row[itemIndex];
                data.monthBalance = BigDecimalUtils.getDecimalValue(row[monthIndex]);
                data.yearBalance = BigDecimalUtils.getDecimalValue(row[yearIndex]);
                String fileName = row[fileNameIndex];
                if (StringUtils.contains(fileName, "小企业会计准则")) {
                    data.monthName = "本月金额";
                    data.yearName = "本年累计金额";
                }
                else {
                    data.monthName = "本期金额";
                    data.yearName = "上期金额";
                }
                result.add(data);
            }
        }
        return new ResponseDto(200, "查询成功", result);
    }

    /**
     * 查询资产负债表
     */
    public ResponseDto findBalanceSheet(String taxCode, String startPeriod, String endPeriod, String type) {
        List<TaxReportDataIncomeDto> result = new ArrayList<>();
        ArrayList<String[]> dataList = findTaxReportData(taxCode, "资产负债表");
        if (dataList != null && !dataList.isEmpty()) {
            String[] firstRow = dataList.get(0);
            int startPeriodIndex = findIndex(firstRow, new String[]{"所属期起"});
            int endPeriodIndex = findIndex(firstRow, new String[]{"所属期止"});
            int itemIndex = findIndex(firstRow, new String[]{"项目"});
            int typeIndex = findIndex(firstRow, new String[]{"来源"});
            int monthIndex = findIndex(firstRow, new String[]{"期末余额"});
            int yearIndex = findIndex(firstRow, new String[]{"年初余额"});
            for (String[] row : dataList) {
                if (row.length <= startPeriodIndex || row.length <= endPeriodIndex) {
                    continue;
                }
                String rowStartPeriod = StringUtils.substring(getDateStr(row[startPeriodIndex]), 0, 6);
                String rowEndPeriod = StringUtils.substring(getDateStr(row[endPeriodIndex]), 0, 6);
                if (StringUtils.isBlank(rowStartPeriod) || StringUtils.isBlank(rowEndPeriod)) {
                    continue;
                }
                if (!StringUtils.equals(rowStartPeriod, startPeriod) || !StringUtils.equals(rowEndPeriod, endPeriod)) {
                    continue;
                }
                String rowType = row[typeIndex];
                if (!StringUtils.contains(rowType, type)) {
                    continue;
                }
                TaxReportDataIncomeDto data = new TaxReportDataIncomeDto();
                data.name = row[itemIndex];
                data.monthBalance = BigDecimalUtils.getDecimalValue(row[monthIndex]);
                data.yearBalance = BigDecimalUtils.getDecimalValue(row[yearIndex]);
                data.monthName = "期末余额";
                data.yearName = "年初余额";
                result.add(data);
            }
        }
        return new ResponseDto(200, "查询成功", result);
    }

    /**
     * 利润趋势图
     * 纳税分析图
     */
    public ResponseDto findEcharts(String taxCode, String year) {
        ArrayList<String[]> dataList = findTaxReportData(taxCode, "利润表");
        String startPeriod1 = year + "01";
        String endPeriod1 = year + "03";
        String startPeriod2 = year + "04";
        String endPeriod2 = year + "06";
        String startPeriod3 = year + "07";
        String endPeriod3 = year + "09";
        String startPeriod4 = year + "10";
        String endPeriod4 = year + "12";
        BigDecimal yl1 = BigDecimal.ZERO;
        BigDecimal yl2 = BigDecimal.ZERO;
        BigDecimal yl3 = BigDecimal.ZERO;
        BigDecimal yl4 = BigDecimal.ZERO;
        BigDecimal ns1 = BigDecimal.ZERO;
        BigDecimal ns2 = BigDecimal.ZERO;
        BigDecimal ns3 = BigDecimal.ZERO;
        BigDecimal ns4 = BigDecimal.ZERO;
        if (dataList != null && !dataList.isEmpty()) {
            String[] firstRow = dataList.get(0);
            int startPeriodIndex = findIndex(firstRow, new String[]{"所属期起"});
            int endPeriodIndex = findIndex(firstRow, new String[]{"所属期止"});
            int itemIndex = findIndex(firstRow, new String[]{"项目"});
            int fileNameIndex = findIndex(firstRow, new String[]{"文件名"});
            int monthIndex = findIndex(firstRow, new String[]{"本月金额"});
            int yearIndex = findIndex(firstRow, new String[]{"本年累计金额"});
            for (String[] row : dataList) {
                if (row.length <= startPeriodIndex || row.length <= endPeriodIndex) {
                    continue;
                }
                String rowStartPeriod = StringUtils.substring(getDateStr(row[startPeriodIndex]), 0, 6);
                String rowEndPeriod = StringUtils.substring(getDateStr(row[endPeriodIndex]), 0, 6);
                if (StringUtils.isBlank(rowStartPeriod) || StringUtils.isBlank(rowEndPeriod)) {
                    continue;
                }
                if (!StringUtils.startsWith(rowEndPeriod, year)) {
                    continue;
                }
                // 去掉年报
                if (PeriodUtils.getDifference(rowStartPeriod, rowEndPeriod) == 12) {
                    continue;
                }
                String item = row[itemIndex];
                if (StringUtils.contains(item, "三、利润总额")) {
                    BigDecimal balance = countBalance(row, fileNameIndex, monthIndex, yearIndex);
                    if (StringUtils.equals(startPeriod1, rowStartPeriod) && StringUtils.equals(endPeriod1, rowEndPeriod)) {
                        yl1 = balance;
                    }
                    if (StringUtils.equals(startPeriod2, rowStartPeriod) && StringUtils.equals(endPeriod2, rowEndPeriod)) {
                        yl2 = balance;
                    }
                    if (StringUtils.equals(startPeriod3, rowStartPeriod) && StringUtils.equals(endPeriod3, rowEndPeriod)) {
                        yl3 = balance;
                    }
                    if (StringUtils.equals(startPeriod4, rowStartPeriod) && StringUtils.equals(endPeriod4, rowEndPeriod)) {
                        yl4 = balance;
                    }
                }
            }
        }

        List<TaxReport> taxReportList = taxReportDao.findData(taxCode, PageRequest.of(0, 1));
        if (!taxReportList.isEmpty()) {
            TaxReport taxReport = taxReportList.get(0);
            if (StringUtils.isNotBlank(taxReport.resultJsonUrl)) {
                TaxReportDataJsonDto jsonData = restTemplate.getForEntity(taxReport.resultJsonUrl, TaxReportDataJsonDto.class).getBody();
                for (TaxReportDataJsonPayDto pay : Objects.requireNonNull(jsonData).payData) {
                    if (StringUtils.equals(pay.zsxmMc, "增值税")) {
                        String payStartPeriod = StringUtils.substring(getDateStr(pay.skssqq), 0, 6);
                        String payEndPeriod = StringUtils.substring(getDateStr(pay.skssqz), 0, 6);
                        if (StringUtils.compare(payStartPeriod, endPeriod1) <= 0 && StringUtils.compare(payEndPeriod, startPeriod1) >= 0) {
                            ns1 = ns1.add(pay.sjje);
                        }
                        if (StringUtils.compare(payStartPeriod, endPeriod2) <= 0 && StringUtils.compare(payEndPeriod, startPeriod2) >= 0) {
                            ns2 = ns2.add(pay.sjje);
                        }
                        if (StringUtils.compare(payStartPeriod, endPeriod3) <= 0 && StringUtils.compare(payEndPeriod, startPeriod3) >= 0) {
                            ns3 = ns3.add(pay.sjje);
                        }
                        if (StringUtils.compare(payStartPeriod, endPeriod4) <= 0 && StringUtils.compare(payEndPeriod, startPeriod4) >= 0) {
                            ns4 = ns4.add(pay.sjje);
                        }
                    }
                }
            }
        }
        Map<String, Object> result = new HashMap<>();
        List<BigDecimal> profitTrend = new ArrayList<>();
        profitTrend.add(yl1);
        profitTrend.add(yl2);
        profitTrend.add(yl3);
        profitTrend.add(yl4);
        List<BigDecimal> taxAnalysis = new ArrayList<>();
        taxAnalysis.add(ns1);
        taxAnalysis.add(ns2);
        taxAnalysis.add(ns3);
        taxAnalysis.add(ns4);
        result.put("利润趋势图", profitTrend);
        result.put("纳税分析图", taxAnalysis);
        return new ResponseDto(200, "查询成功", result);
    }

    /**
     * 税额
     */
    public ResponseDto findTax(String taxCode, String startPeriod, String endPeriod) {
        Map<String, Object> result = new HashMap<>();
        BigDecimal zzs = BigDecimal.ZERO;
        BigDecimal yhs = BigDecimal.ZERO;
        BigDecimal cjs = BigDecimal.ZERO;
        BigDecimal jyffj = BigDecimal.ZERO;
        BigDecimal dfjyffj = BigDecimal.ZERO;
        BigDecimal qysds = BigDecimal.ZERO;
        List<TaxReport> taxReportList = taxReportDao.findData(taxCode, PageRequest.of(0, 1));
        if (!taxReportList.isEmpty()) {
            TaxReport taxReport = taxReportList.get(0);
            if (StringUtils.isNotBlank(taxReport.resultJsonUrl)) {
                TaxReportDataJsonDto jsonData = restTemplate.getForEntity(taxReport.resultJsonUrl, TaxReportDataJsonDto.class).getBody();
                for (TaxReportDataJsonPayDto pay : Objects.requireNonNull(jsonData).payData) {
                    String payStartPeriod = StringUtils.substring(getDateStr(pay.skssqq), 0, 6);
                    String payEndPeriod = StringUtils.substring(getDateStr(pay.skssqz), 0, 6);
                    if (StringUtils.compare(payStartPeriod, endPeriod) <= 0 && StringUtils.compare(payEndPeriod, startPeriod) >= 0) {
                        if (StringUtils.equals(pay.zsxmMc, "增值税")) {
                            zzs = zzs.add(pay.sjje);
                        }
                        if (StringUtils.equals(pay.zsxmMc, "印花税")) {
                            yhs = yhs.add(pay.sjje);
                        }
                        if (StringUtils.equals(pay.zsxmMc, "城市维护建设税")) {
                            cjs = cjs.add(pay.sjje);
                        }
                        if (StringUtils.equals(pay.zsxmMc, "教育费附加")) {
                            jyffj = jyffj.add(pay.sjje);
                        }
                        if (StringUtils.equals(pay.zsxmMc, "地方教育附加")) {
                            dfjyffj = dfjyffj.add(pay.sjje);
                        }
                        if (StringUtils.equals(pay.zsxmMc, "企业所得税")) {
                            qysds = qysds.add(pay.sjje);
                        }
                    }
                }
            }
        }
        result.put("增值税", zzs);
        result.put("印花税", yhs);
        result.put("城建税", cjs);
        result.put("教育费附加", jyffj);
        result.put("地方教育费附加", dfjyffj);
        result.put("企业所得税", qysds);
        return new ResponseDto(200, "查询成功", result);
    }
}
