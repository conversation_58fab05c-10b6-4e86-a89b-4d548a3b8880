package com.hongfund.efi.service;

import com.hongfund.efi.domain.*;
import com.hongfund.efi.dto.ResponseDto;
import com.hongfund.efi.dto.RpaCookieTaskDto;
import com.hongfund.efi.dto.TaxAuthDto;
import com.hongfund.efi.dto.TextMessageDto;
import com.hongfund.efi.profile.TaxRecordArea;
import com.hongfund.efi.repository.*;
import com.hongfund.efi.utils.RamPager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

import static com.hongfund.efi.profile.TaxRecordAction.RPA_COOKIE_TASK;
import static com.hongfund.efi.profile.TaxRecordArea.getByProvinceAndBookName;
import static com.hongfund.efi.profile.TaxRecordState.*;
import static com.hongfund.efi.service.AiHelperService.matchRpaCookieState;

@Service
public class RpaCookieTaskService {

    @Value("${spring.profiles.active}")
    private String active;
    @Autowired
    @Qualifier("restTemplateWithLongTimeout")
    private RestTemplate restTemplate;
    @Autowired
    private AccountBookDao accountBookDao;
    @Autowired
    private AccountBookInfoDao accountBookInfoDao;
    @Autowired
    private RpaCookieTaskDao rpaCookieTaskDao;
    @Autowired
    private RpaCookieStateDao rpaCookieStateDao;
    @Autowired
    private AccountDao accountDao;
    @Autowired
    private QxyOrderLogDao qxyOrderLogDao;
    @Autowired
    private QxyCookieService qxyCookieService;
    @Autowired
    private QxyOrderService qxyOrderService;
    @Autowired
    private AccountBookService accountBookService;

    @Transactional(readOnly = true)
    public ResponseDto infoRpaCookieTask(Long accountBookId, String bookName, Long partyId, Integer source) {
        if (source == null) {
            source = 0;
        }
        AccountBook accountBook;
        if (accountBookId == null) {
            accountBook = accountBookService.getAccountBook(bookName, partyId);
        }
        else {
            accountBook = accountBookDao.findAccountBookById(accountBookId);
        }
        RpaCookieTask rpaCookieTask = new RpaCookieTask();
        if (accountBook != null) {
            AccountBookInfo accountBookInfo = accountBookInfoDao.findByAccountBookId(accountBook.id);
            rpaCookieTask.parseData(accountBookInfo, accountBook);
            if (StringUtils.startsWith(active, "jzs")) {
                String managerNo = StringUtils.substring(accountBook.accounterNo, 0, 6);
                Account adminAccount = accountDao.findByNo(managerNo);
                rpaCookieTask.typeAuth = adminAccount.typeAuth;
                if (qxyOrderService.isOpen(accountBook.accounterNo, accountBookInfo.socialCreditCode)) {
                    rpaCookieTask.typeAuth = 1;
                }
                if (source == 0 && adminAccount.countIsOpen() && rpaCookieTask.typeAuth != 1) {
                    return new ResponseDto(4, "未开通该功能，请开通升级后使用！", null);
                }
            }
        }
        if (source != 0) {
            List<RpaCookieTask> oldList = rpaCookieTaskDao.findOld(bookName, source, PageRequest.of(0,1));
            if (!oldList.isEmpty()) {
                RpaCookieTask old = oldList.get(0);
                rpaCookieTask.parseData(old);
            }
        }
        rpaCookieTask.env = active;
        rpaCookieTask.source = source;
        rpaCookieTask.actionId = RPA_COOKIE_TASK.actionId;
        return new ResponseDto(200, "查询成功", rpaCookieTask);
    }

    @Transactional
    public ResponseDto saveRpaCookieTask(RpaCookieTaskDto rpaCookieTaskDto, HttpServletRequest request2) {
        ResponseDto responseDto = infoRpaCookieTask(rpaCookieTaskDto.accountBookId, null, null, rpaCookieTaskDto.source);
        if (!responseDto.isSuccess()) {
            return responseDto;
        }
        RpaCookieTask rpaCookieTask = (RpaCookieTask) responseDto.data;
        if (rpaCookieTaskDto.source != 0) {
            rpaCookieTask.taxPayerPhone = rpaCookieTaskDto.taxPayerPhone;
            rpaCookieTask.taxAuthorities = rpaCookieTaskDto.taxAuthorities;
            rpaCookieTask.province = rpaCookieTaskDto.province;
            rpaCookieTask.city = rpaCookieTaskDto.city;
            rpaCookieTask.loginWay = rpaCookieTaskDto.loginWay;
            rpaCookieTask.socialCreditCodeAgent = rpaCookieTaskDto.socialCreditCodeAgent;
        }
        TaxRecordArea taxRecordArea = getByProvinceAndBookName(rpaCookieTask.province, rpaCookieTask.accountBookName);
        if (taxRecordArea == null) {
            return new ResponseDto(3, "找不到区域", null);
        }
        rpaCookieTask.areaId = taxRecordArea.areaId;
        rpaCookieTask.nationalName = rpaCookieTaskDto.nationalName;
        rpaCookieTask.nationalPwd = rpaCookieTaskDto.nationalPwd;
        RpaCookieState rpaCookieState = rpaCookieStateDao.findBySocialCreditCodeAndLoginWayAndOpenState(rpaCookieTask.taxPayerPhone, rpaCookieTask.loginWay, rpaCookieTask.typeAuth);
        if (rpaCookieState != null && rpaCookieState.state == 1 && rpaCookieTaskDto.source == 0) {
            return new ResponseDto(4, "验证成功", rpaCookieTask);
        }
        if (rpaCookieState == null) {
            rpaCookieState = new RpaCookieState();
            rpaCookieState.socialCreditCode = rpaCookieTask.taxPayerPhone;
            rpaCookieState.loginWay = rpaCookieTask.loginWay;
            rpaCookieState.openState = rpaCookieTask.typeAuth;
        }
        rpaCookieState.authTime = new Date();
        rpaCookieTask.state = RUNNING;
        int code = 200;
        // 企享云
        if (rpaCookieTask.typeAuth == 1) {
            ResponseDto checkResult = qxyCookieService.check(rpaCookieTaskDto.accountBookId, null, rpaCookieTask, request2);
            if (!checkResult.isSuccess()) {
                rpaCookieTask.state = ERROR;
                rpaCookieTask.resultMessage = checkResult.message;
            }
            else {
                //可以快速登录
                if ((boolean) checkResult.data) {
                    rpaCookieTask.state = FINISHED;
                    rpaCookieTask.resultMessage = "登录成功, 无需验证码";
                    code = 201;
                }
                else {
                    ResponseDto loginResult = qxyCookieService.login(rpaCookieTaskDto.accountBookId, null, rpaCookieTask, request2);
                    rpaCookieTask.resultMessage = loginResult.message;
                    if (!loginResult.isSuccess()) {
                        rpaCookieTask.state = ERROR;
                    }
                    else {
                        if (StringUtils.contains(loginResult.message, "登录成功")) {
                            rpaCookieTask.state = FINISHED;
                            rpaCookieTask.resultMessage = "登录成功, 无需验证码";
                            code = 201;
                        }
                        else {
                            Map<String, Object> temp = (Map<String, Object>) loginResult.data;
                            rpaCookieTask.taskId = String.valueOf(temp.get("taskId"));
                            rpaCookieTask.resultMessage = "短信发送成功";
                        }
                    }
                }
            }
            if (code == 201) {
                if (rpaCookieState.state == 0) {
                    rpaCookieState.gmtCreate = new Date();
                }
                rpaCookieState.state = 1;
                rpaCookieState.gmtModified = new Date();
            }
        }
        else {
            ResponseDto responseDto1 = restTemplate.getForObject("http://fc.hongfund.com:8801/tax/server/free", ResponseDto.class);
            if (responseDto1 == null || responseDto1.code != 20000) {
                return new ResponseDto(5, "服务器繁忙，请稍后再试", null);
            }
            String serverIp = (String) responseDto1.data;
            rpaCookieTask.ip = serverIp;
            String baseUrl = createBaseUrl(serverIp);
            rpaCookieTaskDao.save(rpaCookieTask);
            rpaCookieTaskDao.flush();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<RpaCookieTask> request = new HttpEntity<>(rpaCookieTask, headers);
            ResponseDto responseDto2 = null;
            try {
                responseDto2 = restTemplate.postForObject(baseUrl+"/rpa/cookie/jump", request, ResponseDto.class);
            }
            catch (Exception e) {
                rpaCookieTask.state = ERROR;
                rpaCookieTask.resultMessage = e.getMessage();
            }
            if (responseDto2 != null) {
                code = responseDto2.code;
                rpaCookieTask.resultMessage = responseDto2.message;
            }
            if (responseDto2 == null || (responseDto2.code != 200 && responseDto2.code != 201)) {
                rpaCookieTask.state = ERROR;
            }
            else {
                rpaCookieTask.state = FINISHED;
            }
        }
        rpaCookieStateDao.save(rpaCookieState);
        rpaCookieTaskDao.save(rpaCookieTask);
        if (rpaCookieTask.state == ERROR) {
            return new ResponseDto(6, rpaCookieTask.resultMessage, rpaCookieTask);
        }
        else {
            return new ResponseDto(code, rpaCookieTask.resultMessage, rpaCookieTask);
        }
    }

    /**
     * 接收短信验证码
     */
    @Transactional
    public ResponseDto sendSmsCode(RpaCookieTaskDto rpaCookieTaskDto, HttpServletRequest httpServletRequest) {
        RpaCookieTask rpaCookieTask = rpaCookieTaskDao.findRpaCookieTaskById(rpaCookieTaskDto.id);
        if (StringUtils.isNotBlank(rpaCookieTask.smsCode)) {
            return new ResponseDto(1, "请勿重复提交", rpaCookieTask);
        }
        rpaCookieTask.smsCode = rpaCookieTaskDto.smsCode;
        rpaCookieTaskDao.flush();
        if (rpaCookieTask.typeAuth == 1) {
            ResponseDto smsResult = qxyCookieService.pushSms(rpaCookieTask.accountBookId, rpaCookieTask.smsCode, rpaCookieTask.taskId, rpaCookieTask, httpServletRequest);
            rpaCookieTask.resultMessage = smsResult.message;
            if (!smsResult.isSuccess()) {
                rpaCookieTask.state = ERROR;
            }
            else {
                rpaCookieTask.state = FINISHED;
                RpaCookieState rpaCookieState = rpaCookieStateDao.findBySocialCreditCodeAndLoginWayAndOpenState(rpaCookieTask.taxPayerPhone, rpaCookieTask.loginWay, rpaCookieTask.typeAuth);
                if (rpaCookieState == null) {
                    rpaCookieState = new RpaCookieState();
                    rpaCookieState.socialCreditCode = rpaCookieTask.taxPayerPhone;
                    rpaCookieState.loginWay = rpaCookieTask.loginWay;
                    rpaCookieState.openState = rpaCookieTask.typeAuth;
                }
                if (rpaCookieState.state == 0) {
                    rpaCookieState.gmtCreate = new Date();
                }
                rpaCookieState.state = 1;
                rpaCookieState.gmtModified = new Date();
                rpaCookieStateDao.save(rpaCookieState);
            }
        }
        else {
            // 保存短信
            TextMessageDto textMessageDto = new TextMessageDto();
            textMessageDto.phone = rpaCookieTask.taxPayerPhone;
            textMessageDto.content = "【金账云登录验证】您的验证码是："+rpaCookieTask.smsCode+"，请不要把验证码泄露给其他人。";
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<TextMessageDto> request = new HttpEntity<>(textMessageDto, headers);
            restTemplate.postForObject("http://fc.hongfund.com:8801/text/message/save", request, ResponseDto.class);

            String baseUrl = createBaseUrl(rpaCookieTask.ip);
            HttpHeaders headers2 = new HttpHeaders();
            headers2.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<RpaCookieTask> request2 = new HttpEntity<>(rpaCookieTask, headers2);
            ResponseDto responseDto2 = null;
            try {
                responseDto2 = restTemplate.postForObject(baseUrl+"/rpa/cookie/continue", request2, ResponseDto.class);
            }
            catch (Exception e) {
                rpaCookieTask.state = ERROR;
                rpaCookieTask.resultMessage = e.getMessage();
            }
            if (responseDto2 != null) {
                rpaCookieTask.resultMessage = responseDto2.message;
            }
            if (responseDto2 == null || (responseDto2.code != 200 && responseDto2.code != 201)) {
                rpaCookieTask.state = ERROR;
            }
            else {
                rpaCookieTask.state = FINISHED;
            }
        }
        rpaCookieTaskDao.save(rpaCookieTask);
        if (rpaCookieTask.state == ERROR) {
            return new ResponseDto(1, rpaCookieTask.resultMessage, rpaCookieTask);
        }
        else {
            return new ResponseDto(200, rpaCookieTask.resultMessage, rpaCookieTask);
        }
    }

    @Transactional
    public ResponseDto setResult(RpaCookieTaskDto rpaCookieTaskDto) {
        RpaCookieTask rpaCookieTask = rpaCookieTaskDao.findRpaCookieTaskById(rpaCookieTaskDto.id);
        if (rpaCookieTask == null) {
            return new ResponseDto(1, "找不到记录", null);
        }
        rpaCookieTask.state = rpaCookieTaskDto.state;
        rpaCookieTask.resultMessage = rpaCookieTaskDto.resultMessage;
        rpaCookieTaskDao.save(rpaCookieTask);
        return new ResponseDto(200, "保存成功", rpaCookieTask);
    }

    /**
     * 验证列表
     */
    public Page<TaxAuthDto> listRpaCookieTask(Long accountId, String query, String accounterNo, Integer state, Integer loginWay, Integer openState, Pageable pageable) {
        if(StringUtils.isBlank(query)){
            query = "";
        }
        Account account = accountDao.findOne(accountId);
        if (StringUtils.isBlank(accounterNo)) {
            accounterNo = account.no + "%";
        }

        if (account.level == 1) {
            return rpaCookieStateDao.findTaxAuthList(accounterNo, query, loginWay, pageable);
        }

        List<TaxAuthDto> data = rpaCookieStateDao.findTaxAuthList2(accounterNo, query, loginWay);
        List<QxyOrderLog> qxyOrderLogs = qxyOrderLogDao.findByAccounterNo(StringUtils.substring(accounterNo, 0, 6));
        Set<String> nameSet = new HashSet<>();
        for (TaxAuthDto taxAuthDto : data) {
            taxAuthDto.openState = QxyOrderService.isOpen(qxyOrderLogs, taxAuthDto.socialCreditCode) ? 1 : 0;
            nameSet.add(taxAuthDto.nationalNameFinal);
        }
        List<RpaCookieState> rpaCookieStateList = rpaCookieStateDao.findBySocialCreditCodeIn(nameSet);
        for (TaxAuthDto taxAuthDto : data) {
            RpaCookieState rpaCookieState = matchRpaCookieState(rpaCookieStateList, taxAuthDto.nationalNameFinal, taxAuthDto.loginWay, taxAuthDto.openState);
            if (rpaCookieState != null) {
                taxAuthDto.state = rpaCookieState.state;
                if (rpaCookieState.authTime != null) {
                    taxAuthDto.gmtModified = rpaCookieState.authTime;
                }
                else {
                    taxAuthDto.gmtModified = rpaCookieState.gmtModified;
                }
            }
        }
        List<TaxAuthDto> filteredData = data.stream()
                .filter(taxAuthDto -> state == null || (taxAuthDto.state != null && taxAuthDto.state.equals(state)))
                .filter(taxAuthDto -> openState == null || (taxAuthDto.openState != null && taxAuthDto.openState.equals(openState)))
                .collect(Collectors.toList());
        RamPager<TaxAuthDto> pager = new RamPager<>(filteredData, pageable);
        return pager.pageResult();
    }

    public static String createBaseUrl(String ip) {
        return "http://"+ip+":3060";
    }
}
