package com.hongfund.efi.service;

import com.hongfund.efi.domain.*;
import com.hongfund.efi.dto.*;
import com.hongfund.efi.profile.SubjectLongTextType;
import com.hongfund.efi.repository.*;
import com.hongfund.efi.service.exception.ErrorCode;
import com.hongfund.efi.service.exception.ServiceException;
import com.hongfund.efi.utils.*;
import com.itextpdf.text.pdf.PdfPTable;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

import static com.hongfund.efi.profile.AccountingSystem.AS_CJT;
import static com.hongfund.efi.profile.AccountingSystem.AS_NPO;
import static com.hongfund.efi.profile.AssetType.ASSET_TYPE_1;
import static com.hongfund.efi.profile.AssetType.ASSET_TYPE_2;
import static com.hongfund.efi.profile.FixedAssetsMethod.*;
import static com.hongfund.efi.utils.BigDecimalUtils.*;
import static com.hongfund.efi.utils.MyDocument.*;

/**
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class FixedAssetsService {
    private static final Logger logger = LoggerFactory.getLogger(FixedAssetsService.class);

    @Autowired
    private FixedAssetsDao fixedAssetsDao;

    @Autowired
    private PeriodDao periodDao;

    @Autowired
    private AccountBookDao accountBookDao;

    @Autowired
    private VoucherRecordDao voucherRecordDao;

    @Autowired
    private VoucherDao voucherDao;

    @Autowired
    private SubjectDao subjectDao;

    @Autowired
    private BalanceDao balanceDao;

    @Autowired
    private DimBalanceDao assBalancesDao;

    @Autowired
    private BalanceService balanceService;

    @Autowired
    private DimItemDao dimItemDao;

    @Autowired
    private DimCustomerDao dimCustomerDao;

    @Autowired
    private DimDepartmentDao dimDepartmentDao;

    @Autowired
    private DimEmployeeDao dimEmployeeDao;

    @Autowired
    private DimInventoryDao dimInventoryDao;

    @Autowired
    private DimProviderDao dimProviderDao;

    @Autowired
    private FixedAssetsCategoryDao fixedAssetsCategoryDao;

    @Autowired
    private SubjectService subjectService;

    @Autowired
    private SubjectDimRelationDao subjectDimRelationDao;


    /**
     * 新增固定资产
     */
    public FixedAssetsDto addFixedAssets(DBCache cache, FixedAssetsRowDto fixedAssetsRowDto) {
        if (!fixedAssetsRowDto.buyPeriod.matches(RegExpUtil.PERIOD_MATCHER)) {
            throw new ServiceException("购买日期格式有误！请重新输入！正确格式：202001", ErrorCode.BAD_REQUEST);
        }
        if (StringUtils.isBlank(fixedAssetsRowDto.endPeriod)) {
            //如果没传期间
            fixedAssetsRowDto.endPeriod = periodDao.findMaxPeriod(fixedAssetsRowDto.accountBookId);
        }
        if (fixedAssetsRowDto.assetType == null) {
            fixedAssetsRowDto.assetType = ASSET_TYPE_1.typeCode;
        }
        if (StringUtils.isBlank(fixedAssetsRowDto.number)) {
            fixedAssetsRowDto.number = "0.00";
        }

        if (cache == null) {
            cache = new DBCache();
            cache.accountBook = accountBookDao.findAccountBookById(fixedAssetsRowDto.accountBookId);
            cache.relations = subjectDimRelationDao.findByAccountBookId(fixedAssetsRowDto.accountBookId);
            cache.subjects = subjectDao.findByAccountBookId(fixedAssetsRowDto.accountBookId);
            cache.fixedAssetsCategorys = fixedAssetsCategoryDao.findByAccountBookId(fixedAssetsRowDto.accountBookId);
        }
        cache.vouchers = voucherDao.findByAccountBookIdAndPeriodOrderByVoucherNoAsc(fixedAssetsRowDto.accountBookId, fixedAssetsRowDto.endPeriod);

        FixedAssetsDto result = new FixedAssetsDto();
        checkAssets(cache, fixedAssetsRowDto);
        //新增资产
        if (fixedAssetsRowDto.id == null) {
            if (StringUtils.isBlank(fixedAssetsRowDto.thisDepreciate) && StringUtils.isNotBlank(fixedAssetsRowDto.monthDepreciate)) {
                fixedAssetsRowDto.thisDepreciate = fixedAssetsRowDto.monthDepreciate;
            }
            // 计算固定资产
            calculateAssets(fixedAssetsRowDto);
            fixedAssetsRowDto.assetsName = fixedAssetsRowDto.assetsName.trim();
            FixedAssetsCategory fc = cache.getCategoryByNoAndAssetType(fixedAssetsRowDto.fixedAssetsType, fixedAssetsRowDto.assetType);
            fixedAssetsRowDto.fixedAssetsTypeName = fc.name;
            fixedAssetsRowDto.method = String.valueOf(getFixedMethodTypeCode(fixedAssetsRowDto.method));
            //折旧科目
            if (StringUtils.isBlank(fixedAssetsRowDto.depreciateSubject)) {
                // 维护老数据，如果是老数据
                if (StringUtils.isNotBlank(fc.depreciateLongText)) {
                    fixedAssetsRowDto.depreciateSubject = getSubjectIds(cache, fixedAssetsRowDto.depreciateSubjectOBJ, fc.depreciateLongText);
                } else {
                    fixedAssetsRowDto.depreciateSubject = fc.depreciateSubject;
                }
            }
            //费用科目
            if (StringUtils.isBlank(fixedAssetsRowDto.feeSubject)) {
                if (StringUtils.isNotBlank(fc.feeLongText)) {
                    fixedAssetsRowDto.feeSubject = getSubjectIds(cache, fixedAssetsRowDto.feeSubjectOBJ, fc.feeLongText);
                } else {
                    fixedAssetsRowDto.feeSubject = fc.feeSubject;
                }
            }
            FixedAssets fas = fixedAssetsDao.save(BeanMapper.map(fixedAssetsRowDto, FixedAssets.class));

            //设置生成凭证
            result.voucher = new VoucherDto();
            result.voucher.accountBookId = fas.accountBookId;
            result.voucher.voucherNo = cache.getCurrentVoucherNo(fixedAssetsRowDto.endPeriod);
            //购买日期的最后一天
            result.voucher.voucherDate = CalendarUtils.getLastDayDateOfMonth(fas.writePeriod);

            // 固定资产凭证科目
            String jfLongText = SubjectLongTextType.getSubjectLongText(cache.accountBook.accountingSystem, fas.assetType, "资产");
            Subject jfSubject = cache.getSubjectByText(jfLongText);
            String dfLongText = SubjectLongTextType.getSubjectLongText(cache.accountBook.accountingSystem, fas.assetType, "往来");
            Subject dfSubject = cache.getSubjectByText(dfLongText);
            List<Subject> jfSubjects = cache.getChildSubjects(jfSubject.no);
            List<Subject> dfSubjects = cache.getChildSubjects(dfSubject.no);
            result.voucher.voucherRecords.add(new VoucherRecordDto(BigDecimal.ZERO, jfSubjects.get(0).no + " " + jfSubjects.get(0).longText, fas.originalValue, "购入" + fas.assetsName, jfSubjects.get(0).id));
            result.voucher.voucherRecords.add(new VoucherRecordDto(fas.originalValue, dfSubjects.get(0).no + " " + dfSubjects.get(0).longText, BigDecimal.ZERO, "购入" + fas.assetsName, dfSubjects.get(0).id));
            //合计值
            result.voucher.jftotal = FormatUtils.DECIMAL_FORMAT_2.format(fas.originalValue);
            result.voucher.dftotal = FormatUtils.DECIMAL_FORMAT_2.format(fas.originalValue);
        }
        //修改固定资产
        else {
            FixedAssets originalFixedAssets = fixedAssetsDao.findOne(fixedAssetsRowDto.id);
            if (fixedAssetsRowDto.endPeriod.equals(fixedAssetsRowDto.writePeriod)) {

                // 修改期初累计折旧
                if (getDecimalValue(fixedAssetsRowDto.totalDepreciate).compareTo(originalFixedAssets.totalDepreciate) != 0) {
                    // 判断修改金额是否有效
                    String message = isModifyTotal(originalFixedAssets, fixedAssetsRowDto);
                    if (StringUtils.isBlank(message)) {
                        calculateAssets(fixedAssetsRowDto);
                    } else {
                        result.text = message;
                    }
                } else {
                    // 是否重新计算期初累计折旧
                    if (isRecalculateTotal(originalFixedAssets, fixedAssetsRowDto)) {
                        fixedAssetsRowDto.totalDepreciate = "";
                        calculateAssets(fixedAssetsRowDto);
                    }
                }

                // 修改本月折旧
                if (StringUtils.isNotBlank(fixedAssetsRowDto.thisDepreciate)) {
                    String text = isRecalculate(originalFixedAssets, fixedAssetsRowDto);
                    if (StringUtils.isBlank(text)) {
                        calculateAssets(fixedAssetsRowDto);
                    } else {
                        result.text = text;
                    }
                }

                fixedAssetsRowDto.method = String.valueOf(getFixedMethodTypeCode(fixedAssetsRowDto.method));
                originalFixedAssets = BeanMapper.map(fixedAssetsRowDto, FixedAssets.class);
            }
            originalFixedAssets.depreciateSubject = getSubjectIds(cache, fixedAssetsRowDto.depreciateSubjectOBJ);
            originalFixedAssets.feeSubject = getSubjectIds(cache, fixedAssetsRowDto.feeSubjectOBJ);
            originalFixedAssets.fixedAssetsType = fixedAssetsRowDto.fixedAssetsType;
            FixedAssets saveFixedAssets = fixedAssetsDao.save(originalFixedAssets);
            result.fads.add(BeanMapper.map(saveFixedAssets, FixedAssetsRowDto.class));
        }
        return result;
    }

    /**
     * 检查固定资产
     */
    private void checkAssets(DBCache cache, FixedAssetsRowDto fixedAssetsRowDto) {
        //不能为空
        if (StringUtils.isBlank(fixedAssetsRowDto.assetsName)) {
            logger.info("资产名称不能为空！请重新输入！");
            throw new ServiceException("资产名称不能为空！请重新输入！", ErrorCode.BAD_REQUEST);
        }
        if (StringUtils.isBlank(fixedAssetsRowDto.fixedAssetsType)) {
            logger.info("资产类别不能为空！请重新输入！");
            throw new ServiceException("资产类别不能为空！请重新输入！", ErrorCode.BAD_REQUEST);
        }
        if (StringUtils.isBlank(fixedAssetsRowDto.method)) {
            logger.info("折旧方法不能为空！请重新输入！");
            throw new ServiceException("折旧方法不能为空！请重新输入！", ErrorCode.BAD_REQUEST);
        }
        if (StringUtils.isBlank(fixedAssetsRowDto.buyPeriod)) {
            logger.info("购买日期不能为空！请检查！");
            throw new ServiceException("购买日期不能为空！请检查！", ErrorCode.BAD_REQUEST);
        }
        if (StringUtils.isBlank(fixedAssetsRowDto.oldValueRate) || !fixedAssetsRowDto.oldValueRate.matches(RegExpUtil.NUMBER)) {
            logger.info("残值率有误！请检查！");
            throw new ServiceException("残值率有误！请检查！", ErrorCode.BAD_REQUEST);
        }
        //判断使用期限（月）是否为包含小数或负数
        int maxNumber = 1200;
        boolean isBiggerThanZero = fixedAssetsRowDto.useMonth.matches(RegExpUtil.ONLY_NUMBER) && Integer.parseInt(fixedAssetsRowDto.useMonth) >= 0;
        if (isBiggerThanZero || Integer.parseInt(fixedAssetsRowDto.useMonth) <= maxNumber) {
            fixedAssetsRowDto.useMonth = fixedAssetsRowDto.useMonth.replace(".00", "");
        } else {
            logger.info("期限请输入0-1200的整数");
            throw new ServiceException("期限请输入0-1200的整数", ErrorCode.BAD_REQUEST);
        }
        //购买日期
        String buyPeriod = fixedAssetsRowDto.buyPeriod;
        if (!buyPeriod.matches(RegExpUtil.PERIOD_MATCHER) && buyPeriod.compareTo(fixedAssetsRowDto.endPeriod) > 0) {
            logger.info("购买日期输入有误!");
            throw new ServiceException("购买日期输入有误!", ErrorCode.BAD_REQUEST);
        }
        //原值
        if (StringUtils.isBlank(fixedAssetsRowDto.originalValue) || !fixedAssetsRowDto.originalValue.matches(RegExpUtil.NUMBER)) {
            logger.info("原值输入格式有误！请重新输入！");
            throw new ServiceException("原值输入格式有误！请重新输入！", ErrorCode.BAD_REQUEST);
        }
        //录入期间
        if (!fixedAssetsRowDto.writePeriod.matches(RegExpUtil.PERIOD_MATCHER)) {
            logger.info("录入期间输入格式不正确!");
            throw new ServiceException("录入期间输入格式不正确!", ErrorCode.BAD_REQUEST);
        }
        //固定资产类别
        FixedAssetsCategory fixedAssetsCategory = cache.getCategoryByNoAndAssetType(fixedAssetsRowDto.fixedAssetsType, fixedAssetsRowDto.assetType);
        if (fixedAssetsCategory == null) {
            logger.info("资产类别编码错误！");
            throw new ServiceException("资产类别编码错误！", ErrorCode.BAD_REQUEST);
        }
        if (StringUtils.isNotBlank(fixedAssetsCategory.pno)) {
            if (cache.isCategoryLeaf(fixedAssetsCategory.pno, fixedAssetsCategory.assetType)) {
                logger.info("资产类别不是最末级!");
                throw new ServiceException("资产类别不是最末级!", ErrorCode.BAD_REQUEST);
            }
        }
    }

    /**
     * 获取累计折旧科目、固定资产折旧科目id（保存时使用）
     */
    private String getSubjectIds(DBCache cache, FixedAssetsSubjectDto fixedAssetsSubjectDto, String longText) {
        String result;
        if (fixedAssetsSubjectDto == null) {
            Subject accumulatedDepreciation = cache.getSubjectByLongText(longText);
            List<Subject> depreciateSubjectLeaf = cache.getChildSubjects(accumulatedDepreciation.no);
            result = depreciateSubjectLeaf.get(0).id + "";
        } else {
            Subject accumulatedDepreciation = cache.getSubjectById(fixedAssetsSubjectDto.id);
            fixedAssetsSubjectDto.relation = subjectService.getRelation(cache, fixedAssetsSubjectDto.relation, accumulatedDepreciation);
            result = fixedAssetsSubjectDto.id + "." + fixedAssetsSubjectDto.relation.id;
        }
        return result;
    }

    public String getSubjectIds(DBCache cache, FixedAssetsSubjectDto fixedAssetsSubjectDto) {
        String result;
        Subject subject = cache.getSubjectById(fixedAssetsSubjectDto.id);
        if (fixedAssetsSubjectDto.relation != null) {
            result = fixedAssetsSubjectDto.id + "." + subjectService.getRelation(cache, fixedAssetsSubjectDto.relation, subject).id;
        } else {
            result = String.valueOf(fixedAssetsSubjectDto.id);
        }
        return result;
    }

    FixedAssetsSubjectDto getSubjectById(DBCache cache, String idStr) {
        FixedAssetsSubjectDto result = new FixedAssetsSubjectDto();
        Subject subject;
        //费用科目、折旧科目
        if (idStr.contains(".")) {
            String feeSubjectId = idStr.substring(0, idStr.indexOf("."));
            subject = cache.getSubjectById(Long.parseLong(feeSubjectId));
            //找出费用科目辅助核算项目
            String assId = idStr.substring(idStr.indexOf(".") + 1);
            SubjectDimRelation relation = cache.getRelationById(Long.parseLong(assId));
            result.relation = relation;
            if (relation != null) {
                result.dimAccountName = cache.getDimAccountNames(relation);
                result.dimAccountNo = cache.getDimAccountNos(relation);
            }
        } else {
            subject = cache.getSubjectById(Long.parseLong(idStr));
        }
        result.id = subject.id;
        result.no = subject.no;
        result.accountBookId = subject.accountBookId;
        result.longText = subject.longText;
        result.text = subject.text;
        result.dimAccountType = subject.dimAccountType;
        return result;
    }


    /**
     * 是否重新计算月折旧额
     */
    private String isRecalculate(FixedAssets originalFixedAssets, FixedAssetsRowDto newFixedAssets) {
        String result = "";
        //剩余折旧额
        BigDecimal cs = getPercentage2(newFixedAssets.oldValueRate);
        BigDecimal czl = getDecimalValue("1").subtract(cs);
        BigDecimal syyz = multiply(getDecimalValue(newFixedAssets.originalValue), czl);
        //剩余月份
        int month = Integer.parseInt(newFixedAssets.useMonth) - PeriodUtils.getDifferenceMonthNum(newFixedAssets.endPeriod, newFixedAssets.buyPeriod);
        if (month - 1 == 0) {
            result = "亲，已折旧完毕，不能再修改本月折旧额！";
        }
        if (new BigDecimal(newFixedAssets.thisDepreciate).signum() == -1) {
            result = "亲，本月折旧额不能为负数！";
        }
        if (Integer.parseInt(newFixedAssets.useMonth) == 0) {
            result = "亲，使用期限已结束，本月折旧额不能修改！";
        }
        if (new BigDecimal(newFixedAssets.thisDepreciate).compareTo((syyz)) > 0) {
            result = "亲，本月折旧额不能超过剩余可折旧额！";
        }
        // 固定资产和长期待摊费用次月开始折旧，无形资产当月开始折旧
        if (!newFixedAssets.assetType.equals(ASSET_TYPE_2.typeCode)){
            if (newFixedAssets.endPeriod.equals(newFixedAssets.writePeriod) && newFixedAssets.buyPeriod.equals(newFixedAssets.writePeriod)) {
                if (StringUtils.compare(newFixedAssets.thisDepreciate, "0.00") != 0) {
                    if (newFixedAssets.assetType.equals(ASSET_TYPE_1.typeCode)) {
                        result = "亲，当月购买的固定资产下月才开始提折旧！";
                    } else {
                        result = "亲，当月购买的长期待摊费用是本月开始摊销！";
                    }
                }
            }
        }
        if (originalFixedAssets.cleanPeriod != null && new BigDecimal(newFixedAssets.originalValue).compareTo(BigDecimal.ZERO) == 0) {
            //已经有清理期间，再修改，如果原值为零则显示提示信息
            result = "亲，已生成计提折旧，不能修改固定资产！";
        }
        return result;
    }

    /**
     * 是否修改期初
     */
    private boolean isRecalculateTotal(FixedAssets originalFixedAssets, FixedAssetsRowDto newFixedAssets) {
        // 只有录入期间=当前期间才能修改期初
        if (StringUtils.equals(newFixedAssets.endPeriod, newFixedAssets.writePeriod)) {
            // 期初累计金额是根据 购买日期、原值、残值率、折旧年限计算，如果修改这些则需要重新计算期初累计折旧
            if (StringUtils.compare(originalFixedAssets.buyPeriod, newFixedAssets.buyPeriod) != 0) {
                return true;
            }
            if (originalFixedAssets.originalValue.compareTo(getDecimalValue(newFixedAssets.originalValue)) != 0) {
                return true;
            }
            if (originalFixedAssets.oldValueRate.compareTo(getDecimalValue(newFixedAssets.oldValueRate)) != 0) {
                return true;
            }
            return originalFixedAssets.useMonth != Integer.parseInt(newFixedAssets.useMonth);
        }
        return false;
    }

    /**
     * 是否修改期初累计折旧
     */
    private String isModifyTotal(FixedAssets originalFixedAssets, FixedAssetsRowDto newFixedAssets) {
        String result = "";
        // 残值
        BigDecimal cs = getPercentage2(newFixedAssets.oldValueRate);
        BigDecimal czl = getDecimalValue("1").subtract(cs);
        BigDecimal residualValue = multiply(getDecimalValue(newFixedAssets.originalValue), czl);
        //剩余月份
        int month = Integer.parseInt(newFixedAssets.useMonth) - PeriodUtils.getDifferenceMonthNum(newFixedAssets.endPeriod, newFixedAssets.buyPeriod);
        if (month - 1 == 0) {
            result = "亲，已折旧完毕，不能再修改期初累计折旧额！";
        }
        if (Integer.parseInt(newFixedAssets.useMonth) == 0) {
            result = "亲，使用期限已结束，期初累计折旧额不能修改！";
        }
        if (new BigDecimal(newFixedAssets.totalDepreciate).signum() == -1) {
            result = "亲，期初累计折旧额不能为负数！";
        }
        if (new BigDecimal(newFixedAssets.totalDepreciate).compareTo((residualValue)) > 0) {
            result = "亲，期初累计折旧额不能超过剩余可折旧额！";
        }
        // 固定资产和长期待摊费用次月开始折旧，无形资产当月开始折旧
        if (!newFixedAssets.assetType.equals(ASSET_TYPE_2.typeCode) ) {
            if (newFixedAssets.endPeriod.equals(newFixedAssets.writePeriod) && newFixedAssets.buyPeriod.equals(newFixedAssets.writePeriod)) {
                result = "亲，当月购买的资产下月才开始折旧或摊销！";
            }
        }
        if (originalFixedAssets.cleanPeriod != null && new BigDecimal(newFixedAssets.originalValue).compareTo(BigDecimal.ZERO) == 0) {
            //已经有清理期间，再修改，如果原值为零则显示提示信息
            result = "亲，已生成计提折旧，期初累计折旧额不能修改！";
        }
        return result;
    }

    /**
     * 计算固定资产
     */
    private void calculateAssets(FixedAssetsRowDto fixed) {
        int month = 24;

        // 计算本年累计折旧
        String startPeriod = PeriodUtils.getYear(fixed.endPeriod) + "01";
        String totalDepreciate = fixed.totalDepreciate;
        String thisDepreciate = fixed.thisDepreciate;
        List<String> periods = PeriodUtils.getPeriods(startPeriod, fixed.endPeriod);
        Integer method = getFixedMethodTypeCode(fixed.method);
        for (String period : periods) {
            if (StringUtils.compare(period, fixed.writePeriod) >= 0) {
                int useMonth = Integer.parseInt(fixed.useMonth);
                fixed.endPeriod = period;
                fixed.totalDepreciate = totalDepreciate;
                fixed.thisDepreciate = thisDepreciate;
                // 已折旧期间
                int monthBetweenCurrentPeriodAndBuyPeriod = PeriodUtils.getDifferenceMonthNum(fixed.endPeriod, fixed.buyPeriod);
                fixed.depreciatePeriod = monthBetweenCurrentPeriodAndBuyPeriod;
                // 如果是无形资产、长期待摊费用，当月就开始折旧
                if (!fixed.assetType.equals(ASSET_TYPE_1.typeCode)){
                    fixed.depreciatePeriod = monthBetweenCurrentPeriodAndBuyPeriod + 1;
                }
                if (useMonth < fixed.depreciatePeriod) {
                    fixed.depreciatePeriod = useMonth;
                }
                if (method.equals(FIXED_ASSETS_METHOD_1.typeCode) || Integer.parseInt(fixed.useMonth) <= month) {
                    //平均年限
                    calculateFixedAssetsByAverageAgeMethod(fixed);
                } else if (method.equals(FIXED_ASSETS_METHOD_2.typeCode)) {
                    //双倍余额递减法
                    calculateFixedAssetsByDoubleBalance(fixed);
                } else {
                    fixed.totalDepreciate = "0.00";
                    fixed.thisDepreciate = "0.00";
                    fixed.monthDepreciate = fixed.thisDepreciate;
                    // 净值 = 原值 - 期初累计折旧
                    fixed.netValue = "0.00";
                    // 期初净值 = 原值
                    fixed.initRemainder = fixed.originalValue;
                    // 期末累计折旧 =
                    fixed.endTotal = "0.00";
                    // 期末净值 = 原值
                    fixed.endRemainder = fixed.originalValue;
                }
                // 已清理的固定资产不计算本年累计折旧
                if (StringUtils.isBlank(fixed.cleanPeriod) || StringUtils.compare(fixed.cleanPeriod, fixed.endPeriod) >= 0) {
                    fixed.yearDepreciate = sum(fixed.thisDepreciate, fixed.yearDepreciate);
                }
            }
        }
    }


    /**
     * 平均年限折旧法
     */
    private void calculateFixedAssetsByAverageAgeMethod(FixedAssetsRowDto fixed) {
        //h录入期间和购买日期a的相差月份
        int monthBetweenWritePeriodAndBuyPeriod = PeriodUtils.getDifferenceMonthNum(fixed.writePeriod, fixed.buyPeriod);
        //当前期间H和录入期间h的相差月份
        int monthBetweenCurrentPeriodAndWritePeriod = PeriodUtils.getDifferenceMonthNum(fixed.endPeriod, fixed.writePeriod);
        //当前期间H和购买日期a的相差月份
        int monthBetweenCurrentPeriodAndBuyPeriod = PeriodUtils.getDifferenceMonthNum(fixed.endPeriod, fixed.buyPeriod);
        //原值
        BigDecimal originalValue = new BigDecimal(fixed.originalValue);
        //折旧月份
        int depreciationMonth = 1;
        if (StringUtils.compare(fixed.useMonth, "0") > 0) {
            depreciationMonth = Integer.parseInt(fixed.useMonth);
        }
        //残值率
        BigDecimal oldValueRate = getDecimalValue(fixed.oldValueRate);
        BigDecimal residualRate = getPercentage2(fixed.oldValueRate);
        BigDecimal needDepreciation = multiply(getDecimalValue(fixed.originalValue), getDecimalValue("1").subtract(residualRate));

        //期初累计折旧
        BigDecimal qcAccumulatedDepreciation;
        if (StringUtils.isNotBlank(fixed.totalDepreciate)) {
            qcAccumulatedDepreciation = getDecimalValue(fixed.totalDepreciate);
        } else {
            // 如果是无形资产，当月就开始折旧
            if (!fixed.assetType.equals(ASSET_TYPE_1.typeCode)){
                qcAccumulatedDepreciation = multiply(divide(needDepreciation, getDecimalValue(String.valueOf(depreciationMonth))), new BigDecimal(monthBetweenWritePeriodAndBuyPeriod));
                if (monthBetweenCurrentPeriodAndBuyPeriod >= depreciationMonth) {
                    qcAccumulatedDepreciation = needDepreciation;
                }
            } else {
                qcAccumulatedDepreciation = multiply(divide(needDepreciation, getDecimalValue(String.valueOf(depreciationMonth))), new BigDecimal(monthBetweenWritePeriodAndBuyPeriod - 1));
                if (monthBetweenCurrentPeriodAndBuyPeriod - 1 >= depreciationMonth) {
                    qcAccumulatedDepreciation = needDepreciation;
                }
            }

            boolean isDepreciation = BigDecimalUtils.compare(getDecimalValue(fixed.oldValueRate), BigDecimal.ZERO) == 0 && depreciationMonth == 1;
            if (monthBetweenWritePeriodAndBuyPeriod == 0 || isDepreciation) {
                qcAccumulatedDepreciation = BigDecimal.ZERO;
            }
        }

        //净值
        BigDecimal netValue = originalValue.subtract(qcAccumulatedDepreciation);
        //月折旧额
        fixed.monthDepreciate = fixed.thisDepreciate;
        BigDecimal monthDepreciation = calculateMonthDepreciation(fixed.monthDepreciate, originalValue, netValue, oldValueRate, monthBetweenWritePeriodAndBuyPeriod, monthBetweenCurrentPeriodAndBuyPeriod, depreciationMonth);
        //期初累计折旧
        BigDecimal totalDepreciation = qcAccumulatedDepreciation;
        if (!fixed.writePeriod.equals(fixed.endPeriod)) {
            totalDepreciation = multiply(monthDepreciation, new BigDecimal(monthBetweenCurrentPeriodAndWritePeriod)).add(qcAccumulatedDepreciation);
            // 如果是无形资产、长期待摊费用，当月就开始折旧
            if (!fixed.assetType.equals(ASSET_TYPE_1.typeCode)){
                //购买日期=录入期间
                if (monthBetweenWritePeriodAndBuyPeriod == 0) {
                    totalDepreciation = multiply(monthDepreciation, new BigDecimal(monthBetweenCurrentPeriodAndWritePeriod)).add(qcAccumulatedDepreciation);
                }
                //最后一个月，全部折旧完
                if (monthBetweenCurrentPeriodAndBuyPeriod >= depreciationMonth || totalDepreciation.compareTo(needDepreciation) > 0) {
                    totalDepreciation = needDepreciation;
                }
            } else {
                //购买日期=录入期间
                if (monthBetweenWritePeriodAndBuyPeriod == 0) {
                    totalDepreciation = multiply(monthDepreciation, new BigDecimal(monthBetweenCurrentPeriodAndWritePeriod - 1)).add(qcAccumulatedDepreciation);
                }
                //最后一个月，全部折旧完
                if (monthBetweenCurrentPeriodAndBuyPeriod - 1 >= depreciationMonth || totalDepreciation.compareTo(needDepreciation) > 0) {
                    totalDepreciation = needDepreciation;
                }
            }
            //期初累计折旧<0
            if (totalDepreciation.compareTo(BigDecimal.ZERO) < 0) {
                totalDepreciation = BigDecimal.ZERO;
            }
            netValue = originalValue.subtract(totalDepreciation);
        }
        BigDecimal thisDepreciate = calculateThisMonthDepreciation(fixed.assetType, monthDepreciation, originalValue.subtract(needDepreciation), netValue, monthBetweenWritePeriodAndBuyPeriod, monthBetweenCurrentPeriodAndBuyPeriod, depreciationMonth, fixed.writePeriod, fixed.endPeriod);
        fixed.totalDepreciate = FormatUtils.DECIMAL_FORMAT_2.format(totalDepreciation);
        fixed.thisDepreciate = fixed.monthDepreciate = FormatUtils.DECIMAL_FORMAT_2.format(thisDepreciate);
        // 净值 = 原值 - 期初累计折旧
        fixed.netValue = FormatUtils.DECIMAL_FORMAT_2.format(netValue);
        // 期初净值 = 净值 = 原值 - 期初累计折旧
        fixed.initRemainder = fixed.netValue;
        // 期末累计折旧 = 期初累计折旧 + 本月折旧
        fixed.endTotal = sum(fixed.totalDepreciate, fixed.thisDepreciate);
        // 期末净值 = 原值 - 期末累计折旧
        fixed.endRemainder = subtract(fixed.originalValue, fixed.endTotal);
        // 如果是无形资产、长期待摊费用摊销
        if (!fixed.assetType.equals(ASSET_TYPE_1.typeCode)){
            // 期初剩余摊销 = 原值-期初累计摊销
            fixed.initRemainder = subtract(fixed.originalValue, fixed.totalDepreciate);
            // 期末累计摊销 = 期初累计摊销+本月摊销
            fixed.endTotal = sum(fixed.totalDepreciate, fixed.thisDepreciate);
            // 期末剩余摊销 = 原值-期末累计摊销
            fixed.endRemainder = subtract(fixed.originalValue, fixed.endTotal);
        }
    }

    /**
     * 双倍余额递减法
     */
    private void calculateFixedAssetsByDoubleBalance(FixedAssetsRowDto fixed) {
        int method = 2;
		/*
			原值：M = fixed.originalValue = b
			使用年限：N = fixed.useMonth = c/12;
			第n年：n = H_a
			残值：m = M*残值率 = fixed.oldValueRate = D
			 */
        List<String> periods = PeriodUtils.getPeriods(fixed.buyPeriod, fixed.endPeriod);
        BigDecimal residualRate = getPercentage2(fixed.oldValueRate);
        //当前期间H和录入期间h的相差月份
        int monthBetweenCurrentPeriodAndBuyPeriod = PeriodUtils.getDifferenceMonthNum(fixed.endPeriod, fixed.buyPeriod);
        //非最后两年应提的年折旧额 * 已折旧的月份/12 = 期初累计折旧
        BigDecimal yearDepreciate;
        //非最后两年应提的月折旧额
        BigDecimal monthDepreciate = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
        //期初累计折旧
        BigDecimal yearTotal = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
        BigDecimal total = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
        int uesYear = Integer.parseInt(fixed.useMonth) / 12;
        // 使用年限 超过2年，计算期初累计折旧
        if (uesYear > method) {
            for (int i = 0; i < periods.size() - method; i++) {
                yearDepreciate = getYearDepreciate(fixed.useMonth, fixed.buyPeriod, periods.get(i), fixed.originalValue, fixed.oldValueRate);
                total = total.add(yearDepreciate.divide(new BigDecimal(12), 2, RoundingMode.HALF_UP));
                if (i % 12 == 0) {
                    yearTotal = yearTotal.add(yearDepreciate);
                    if (yearTotal.compareTo(total) != 0) {
                        total = yearTotal;
                    }
                }
            }
        }
        yearDepreciate = getYearDepreciate(fixed.useMonth, fixed.buyPeriod, fixed.endPeriod, fixed.originalValue, fixed.oldValueRate);
        if (yearDepreciate.compareTo(BigDecimal.ZERO) < 0 || total.compareTo(getDecimalValue(fixed.originalValue)) > 0) {
            fixed.totalDepreciate = fixed.originalValue;
        } else {
            BigDecimal cs;
            BigDecimal czl;
            BigDecimal syyz;
            //月折旧额
            monthDepreciate = yearDepreciate.divide(new BigDecimal(12), 2, RoundingMode.HALF_UP);

            // 本月折旧金额 随折旧年限变化而变化
            if (StringUtils.isNotBlank(fixed.thisDepreciate) && !getDecimalValue(fixed.thisDepreciate).equals(monthDepreciate) && fixed.thisDepreciate.compareTo("0.00") != 0) {
                //剩余折旧额
                cs = getPercentage2(fixed.oldValueRate);
                czl = getDecimalValue("1").subtract(cs);
                syyz = multiply(getDecimalValue(fixed.originalValue), czl).subtract(getDecimalValue(fixed.totalDepreciate));
                if (getDecimalValue(fixed.thisDepreciate).compareTo(syyz) > 0) {
                    monthDepreciate = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
                    total = getDecimalValue(fixed.originalValue).subtract(multiply(getDecimalValue(fixed.originalValue), residualRate));
                } else {
                    int currentYear = Integer.parseInt(fixed.useMonth) / (monthBetweenCurrentPeriodAndBuyPeriod - 1);
                    // 当前年份非折旧第一年时，期初累计折旧 = 之前的月份的累计折旧 + 当年的月折旧额
                    if (currentYear < uesYear) {
                        total = total.add(monthDepreciate);
                    } else {
                        // 折旧第一年，期初累计折旧 = 之前的月份的累计折旧 + 第一年的本月折旧
                        total = total.add(getDecimalValue(fixed.thisDepreciate));
                    }
                }
            } else {
                if (monthBetweenCurrentPeriodAndBuyPeriod > 1) {
                    total = total.add(monthDepreciate);
                }
            }
            //期初累计折旧 = 非最后两年应提的月折旧额 * 已折旧的月份
            fixed.totalDepreciate = FormatUtils.DECIMAL_FORMAT_2.format(total);
        }
        fixed.thisDepreciate = FormatUtils.DECIMAL_FORMAT_2.format(monthDepreciate);
        fixed.monthDepreciate = fixed.thisDepreciate;
        // 净值 = 原值 - 期初累计折旧
        fixed.netValue = subtract(fixed.originalValue, fixed.totalDepreciate);
        // 期初净值 = 净值 = 原值 - 期初累计折旧
        fixed.initRemainder = fixed.netValue;
        // 期末累计折旧 = 期初累计折旧 + 本月折旧
        fixed.endTotal = sum(fixed.totalDepreciate, fixed.thisDepreciate);
        // 期末净值 = 原值 - 期末累计折旧
        fixed.endRemainder = subtract(fixed.originalValue, fixed.endTotal);
        // 如果是无形资产、长期待摊费用摊销
        if (!fixed.assetType.equals(ASSET_TYPE_1.typeCode)){
            // 期初剩余摊销 = 原值-期初累计摊销
            fixed.initRemainder = subtract(fixed.originalValue, fixed.totalDepreciate);
            // 期末累计摊销 = 期初累计摊销+本月摊销
            fixed.endTotal = sum(fixed.originalValue, fixed.thisDepreciate);
            // 期末剩余摊销 = 原值-期末累计摊销
            fixed.endRemainder = subtract(fixed.originalValue, fixed.endTotal);
        }
    }

    /**
     * 计算本月折旧
     */
    private BigDecimal calculateThisMonthDepreciation(Integer assetType, BigDecimal monthDepreciation, BigDecimal residualValue, BigDecimal netValue, int monthBetweenWritePeriodAndBuyPeriod, int monthBetweenCurrentPeriodAndBuyPeriod, int depreciationMonth, String writePeriod, String endPeriod) {
        BigDecimal result;
        boolean isMoreThanNetValue = monthDepreciation.add(residualValue).compareTo(netValue) > 0 && netValue.compareTo(residualValue) > 0;
        //本月折旧 如果净值k<=残值或者录入日期等于购买日期
        if (!writePeriod.equals(endPeriod)) {
            if (netValue.compareTo(residualValue) <= 0) {
                result = BigDecimal.ZERO;
            } else if (isMoreThanNetValue || monthBetweenCurrentPeriodAndBuyPeriod == depreciationMonth) {
                result = netValue.subtract(residualValue);
            } else {
                result = monthDepreciation;
            }
        } else {
            // 固定资产次月开始折旧、其他资产当月开始折旧
            if (netValue.compareTo(residualValue) <= 0 || (assetType.equals(ASSET_TYPE_1.typeCode) && monthBetweenWritePeriodAndBuyPeriod == 0) || monthBetweenCurrentPeriodAndBuyPeriod > depreciationMonth) {
                result = BigDecimal.ZERO;
            } else if (isMoreThanNetValue || monthBetweenCurrentPeriodAndBuyPeriod == depreciationMonth) {
                result = netValue.subtract(residualValue);
            } else {
                result = monthDepreciation;
            }
        }

        return result;
    }

    /**
     * 计算月折旧额
     */
    private BigDecimal calculateMonthDepreciation(String monthDepreciate, BigDecimal originalValue, BigDecimal netValue, BigDecimal oldValueRate, int monthBetweenWritePeriodAndBuyPeriod, int monthBetweenCurrentPeriodAndBuyPeriod, int depreciationMonth) {
        String zero = "0.00";
        BigDecimal result = BigDecimal.ZERO;
        BigDecimal residualRate = getPercentage(oldValueRate, 5);
        BigDecimal residualValue = multiply(originalValue, residualRate);
        //月折旧额
        if (StringUtils.isBlank(monthDepreciate) || monthDepreciate.compareTo(zero) == 0) {
            // 购买日期和录入期间 之间的月份 > 0
            if (monthBetweenWritePeriodAndBuyPeriod > 0) {
                if (depreciationMonth - monthBetweenWritePeriodAndBuyPeriod + 1 != 0) {
                    result = divide(multiply(originalValue, new BigDecimal(1).subtract(residualRate)), new BigDecimal(depreciationMonth));
                }
            } else if (monthBetweenWritePeriodAndBuyPeriod == 0) {
                result = divide(netValue.subtract(residualValue), new BigDecimal(depreciationMonth));
            } else if (monthBetweenCurrentPeriodAndBuyPeriod != 0) {
                result = divide(netValue.subtract(residualRate), new BigDecimal(monthBetweenCurrentPeriodAndBuyPeriod));
            }
        } else {
            result = getDecimalValue(monthDepreciate);
        }
        //年限为0
        if (depreciationMonth == 1) {
            if (BigDecimalUtils.compare(oldValueRate, BigDecimal.ZERO) > 0) {
                result = multiply(originalValue, new BigDecimal(1).subtract(BigDecimal.valueOf(1 * 0.01)));
            }
            //残值率为0
            else {
                result = originalValue;
            }
        }
        return result;
    }

    /**
     * 获取固定资产年累计折旧
     */
    private BigDecimal getYearDepreciate(String useMonth, String buyPeriod, String endPeriod, String originalValue, String oldValueRate) {
        int month = Integer.parseInt(useMonth);
        BigDecimal year = new BigDecimal(month).divide(new BigDecimal("12"), 2, RoundingMode.HALF_UP);

        //截止年份 = 购买日期  +  使用期限
        String stopPeriod = PeriodUtils.getNextPeriod(buyPeriod, month);
        //最后两年
        String lastTwoYears = PeriodUtils.getPreviousPeriod(stopPeriod, 24);

        //当前期间H和购买日期a的相差月份
        int montBetweenCurrentPeriodAndBuyPeriod = PeriodUtils.getDifferenceMonthNum(endPeriod, buyPeriod);
        //当前年份
        int n = (montBetweenCurrentPeriodAndBuyPeriod - 1) / 12 + 1;
        //非最后两年应提的年折旧额 * 已折旧的月份/12 = 期初累计折旧
        BigDecimal yearDepreciate = BigDecimal.ZERO;
        BigDecimal oldValue = new BigDecimal(originalValue).multiply(new BigDecimal(oldValueRate).multiply(new BigDecimal("0.01")));

        BigDecimal pow = BigDecimal.ONE.subtract(new BigDecimal("2").divide(year, 2, RoundingMode.HALF_UP)).pow(n - 1);
        BigDecimal lastPow = BigDecimal.ONE.subtract(new BigDecimal("2").divide(year, 2, RoundingMode.HALF_UP).pow(year.subtract(new BigDecimal("2")).intValue()));
        if (montBetweenCurrentPeriodAndBuyPeriod > 0) {
            //如果使用年限>3年，除最后两年外，其他年份按双倍余额递减计算
            if (endPeriod.compareTo(lastTwoYears) <= 0) {
                //非最后两年应提的年折旧额   M*(1-2/N)^(n-1)*2/N
                yearDepreciate = (new BigDecimal(originalValue).multiply(pow).multiply(new BigDecimal(2))).divide(year, 2, RoundingMode.HALF_UP);
            } else {
                //最后两年应提的年折旧额  [M*(1-2/N)^(N-2)-m]/2
                yearDepreciate = (new BigDecimal(originalValue).multiply(lastPow).subtract(oldValue)).divide(new BigDecimal(2), 2, RoundingMode.HALF_UP);
            }
        }
        return yearDepreciate;
    }

    /**
     * 根据固定资产id删除固定资产
     */
    public void deleteFixedAssets(Long id) {
        fixedAssetsDao.deleteFixedAssetsById(id);
    }

    /**
     * 检查当前月份是否有计提折旧凭证
     */
    boolean checkDepreciation(List<VoucherAndRecordDto> voucherAndRecords, String endPeriod, Integer assetType) {
        for (VoucherAndRecordDto var : voucherAndRecords) {
            String subjectText;
            if (assetType.equals(ASSET_TYPE_1.typeCode)) {
                // 固定资产
                subjectText = "累计折旧";
            } else {
                // 无形资产
                subjectText = "累计摊销";
            }
            if (var.foresubText.contains(subjectText)) {
                if (var.period.compareTo(endPeriod) == 0) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 根据帐套id和期间查询固定资产（分页）
     */
    @Transactional(readOnly = true, rollbackFor = Exception.class)
    public FixedAssetsDto findByAccountBookIdAndPeriod(DBCache cache, Long id, String endPeriod, Integer isClean, Integer assetType, String fixedAssetsType, String feeSubject, String query, Pageable pageable) {
        FixedAssetsDto result = findByAccountBookIdAndPeriod(cache, id, endPeriod, isClean, assetType, fixedAssetsType, feeSubject, query);
        RamPager<FixedAssetsRowDto> pager = new RamPager<>(result.fads, pageable);
        result.pagFads = pager.pageResult();
        return result;
    }

    /**
     * 根据帐套id和期间查询固定资产
     */
    @Transactional(readOnly = true, rollbackFor = Exception.class)
    public FixedAssetsDto findByAccountBookIdAndPeriod(DBCache cache, Long id, String endPeriod, Integer isClean, Integer assetType, String fixedAssetsType, String feeSubject, String query) {

        //没有选择期间，期间为当前帐套的最大期间
        if ("".equals(endPeriod) || endPeriod == null) {
            endPeriod = cache.getMaxPeriod();
        }
        if (assetType == null) {
            assetType = ASSET_TYPE_1.typeCode;
        }
        if (StringUtils.isBlank(fixedAssetsType)){
            fixedAssetsType = null;
        }
        if (StringUtils.isBlank(feeSubject)){
            feeSubject = null;
        }
        if (StringUtils.isBlank(query)){
            query = "";
        }

        if (cache == null) {
            cache = new DBCache();
            cache.subjects = subjectDao.findByAccountBookId(id);
            cache.periods = periodDao.findByAccountBookId(id);
            cache.accountBook = accountBookDao.findAccountBookById(id);
            cache.dimItems = dimItemDao.findByAccountBookId(id);
            cache.dimCustomers = dimCustomerDao.findByAccountBookId(id);
            cache.dimProviders = dimProviderDao.findByAccountBookId(id);
            cache.dimDepartments = dimDepartmentDao.findByAccountBookId(id);
            cache.dimEmployees = dimEmployeeDao.findByAccountBookId(id);
            cache.dimInventories = dimInventoryDao.findByAccountBookId(id);
            cache.relations = subjectDimRelationDao.findByAccountBookId(id);
            cache.fixedAssetsCategorys = fixedAssetsCategoryDao.findByAccountBookId(id);
        }
        //根据帐套id和期间查询固定资产
        List<FixedAssets> fixedAssets;
        if (isClean == 1) {
            fixedAssets = fixedAssetsDao.findByAccountBookIdAndWritePeriodAndAssetType1(id, endPeriod, assetType, fixedAssetsType, feeSubject, query);
        } else if (isClean == 2) {
            fixedAssets = fixedAssetsDao.findByAccountBookIdAndWritePeriodAndAssetType2(id, endPeriod, assetType, fixedAssetsType, feeSubject, query);
        } else {
            fixedAssets = fixedAssetsDao.findByAccountBookIdAndWritePeriodAndAssetType(id, endPeriod, assetType, fixedAssetsType, feeSubject, query);
        }

        String text = "本年利润";
        if (StringUtils.equals(AS_CJT, cache.accountBook.accountingSystem)){
            text = "本年收益";
        }

        String subjectText = SubjectLongTextType.getSubjectLongText(cache.accountBook.accountingSystem, assetType, "折旧");
        List<VoucherAndRecordDto> voucherAndRecords = voucherRecordDao.findByAccountBookIdAndForesubText(id, subjectText, text, "限定性净资产", "非限定性净资产");

        List<FixedAssetsRowDto> fixes = new ArrayList<>();
        FixedAssetsDto result = new FixedAssetsDto();

        //查询帐套
        AccountBook accountBook = cache.accountBook;
        String closePeriod = accountBook.closePeriod;

        //如果有固定资产明细
        if (fixedAssets.size() != 0) {
            //判断当月是否结账
            boolean isClose = closePeriod != null && endPeriod.compareTo(closePeriod) <= 0;
            // 检查当前月份是否计提折旧
            boolean isDepreciation = checkDepreciation(voucherAndRecords, endPeriod, assetType);
            // 检查是否结转损益
            boolean isAdjusting = checkAdjusting(accountBook, voucherAndRecords, endPeriod);

            BigDecimal countOriginalValue = BigDecimal.ZERO;
            BigDecimal countTotalDepreciate = BigDecimal.ZERO;
            BigDecimal netValue = BigDecimal.ZERO;
            BigDecimal monthDepreciate = BigDecimal.ZERO;
            BigDecimal thisDepreciate = BigDecimal.ZERO;
            BigDecimal yearDepreciate = BigDecimal.ZERO;
            BigDecimal initRemainder = BigDecimal.ZERO;
            BigDecimal endRemainder = BigDecimal.ZERO;
            BigDecimal endTotal = BigDecimal.ZERO;
            BigDecimal totalNumber = BigDecimal.ZERO;
            for (int i = 0; i < fixedAssets.size(); i++) {
                FixedAssets fa = fixedAssets.get(i);
                FixedAssetsRowDto fixed = BeanMapper.map(fa, FixedAssetsRowDto.class);
                fixed.feeSubjectOBJ = getSubjectById(cache, fa.feeSubject);
                fixed.depreciateSubjectOBJ = getSubjectById(cache, fa.depreciateSubject);
                fixed.method = getFixedMethodName(fa.method, assetType);
                fixed.endPeriod = endPeriod;
                FixedAssetsCategory fc = cache.getCategoryByNoAndAssetType(fa.fixedAssetsType, fa.assetType);
                fixed.fixedAssetsTypeName = fc.name;
                fixed.code = "00" + (i + 1);
                calculateAssets(fixed);

                //默认不显示清理还原（应为2，这里暂时设为0便于测试）
                int cleanStatus = 2;
                //清理期间
                String cleanPeriod = fixed.cleanPeriod;

                //不可以修改删除：当月已结账，录入期间<当前期间
                int canDel = isClose || endPeriod.compareTo(fixed.writePeriod) > 0 ? 1 : 0;

                // 无形资产 还原按钮显示规则（npo、村集体没有清理）
                if (assetType.equals(ASSET_TYPE_2.typeCode)) {
                    if (!StringUtils.equals(AS_NPO, cache.accountBook.accountingSystem) && !StringUtils.equals(AS_CJT, cache.accountBook.accountingSystem)) {
                        // 已清理，是否显示还原按钮
                        if (cleanPeriod != null) {
                            // 如果清理期间 = 当前期间、没有结账、当月没有结转凭证， 显示还原按钮
                            if (endPeriod.equals(cleanPeriod) && !isClose && !isDepreciation) {
                                cleanStatus = 1;
                            }
                            // 清理期间当月及以后月份都不显示 折旧数据
                            if (StringUtils.compare(endPeriod, cleanPeriod) >= 0) {
                                fixed.originalValue = "0.00";
                                fixed.totalDepreciate = "0.00";
                                fixed.netValue = "0.00";
                                fixed.monthDepreciate = "0.00";
                                fixed.thisDepreciate = "0.00";
                                fixed.initRemainder = "0.00";
                                fixed.endTotal = "0.00";
                                fixed.endRemainder = "0.00";
                            }
                            // 当前期间 < 清理期间，显示清理前的数据
                            else {
                                if (StringUtils.compare(endPeriod, cleanPeriod) < 0) {
                                    cleanStatus = 2;
                                }
                            }
                        }
                        // 未清理
                        else {
                            //已还原，显示清理按钮
                            if (!endPeriod.equals(fixed.buyPeriod) && !isDepreciation && !isAdjusting && !isClose) {
                                cleanStatus = 0;
                            } else {
                                //不显示清理、还原按钮
                                cleanStatus = 2;
                            }
                        }
                    }
                }
                // 其他资产 还原按钮显示规则
                else {
                    // 固定资产清理条件、长期待摊费用没有清理功能
                    if (assetType.equals(ASSET_TYPE_1.typeCode)){
                        //已清理，显示还原图标，原值等字段显示为0（ cleanStatus 0:未清理（显示清理）   1:已清理（显示还原）  2:没有清理或还原按钮）
                        if (cleanPeriod != null) {
                            // 如果清理期间 = 当前期间、没有结账、当月有结转凭证， 显示还原按钮
                            if (endPeriod.equals(cleanPeriod) && !isClose && isDepreciation) {
                                cleanStatus = 1;
                            }
                            if (StringUtils.compare(cleanPeriod, endPeriod) < 0) {
                                fixed.originalValue = "0.00";
                                fixed.totalDepreciate = "0.00";
                                fixed.netValue = "0.00";
                                fixed.monthDepreciate = "0.00";
                                fixed.thisDepreciate = "0.00";
                                fixed.initRemainder = "0.00";
                                fixed.endTotal = "0.00";
                                fixed.endRemainder = "0.00";
                            }
                            //当前期间<=清理期间，显示清理前的数据
                            else {
                                if (StringUtils.compare(cleanPeriod, endPeriod) > 0) {
                                    cleanStatus = 2;
                                }
                            }
                        }
                        // 未清理
                        else {
                            //已还原，显示清理按钮
                            if (!endPeriod.equals(fixed.buyPeriod) && isDepreciation && !isAdjusting && !isClose) {
                                cleanStatus = 0;
                            } else {
                                //不显示清理、还原按钮
                                cleanStatus = 2;
                            }
                        }
                    }
                }
                fixed.cleanStatus = cleanStatus;
                fixed.canDel = canDel;
                if (StringUtils.isNotBlank(cleanPeriod)) {
                    fixed.message = "该资产已在" + cleanPeriod.substring(0, 4) + "年" + cleanPeriod.substring(4) + "月清理";
                }

                //查询清理期间是否有累计折旧凭证
                boolean isTotalDepreciation = false;
                for (VoucherAndRecordDto var : voucherAndRecords) {
                    if (var.period.equals(fixed.cleanPeriod) && var.dfmoney.compareTo(BigDecimal.ZERO) != 0) {
                        isTotalDepreciation = true;
                        break;
                    }
                }

                if (StringUtils.isEmpty(fixed.cleanPeriod) || StringUtils.compare(endPeriod, fixed.cleanPeriod) < 0 || !isTotalDepreciation) {
                    countOriginalValue = sum(countOriginalValue, getDecimalValue(fixed.originalValue));
                    countTotalDepreciate = sum(countTotalDepreciate, getDecimalValue(fixed.totalDepreciate));
                    netValue = sum(netValue, getDecimalValue(fixed.netValue));
                    monthDepreciate = sum(monthDepreciate, getDecimalValue(fixed.monthDepreciate));
                    thisDepreciate = sum(thisDepreciate, getDecimalValue(fixed.thisDepreciate));
                    yearDepreciate = sum(yearDepreciate, getDecimalValue(fixed.yearDepreciate));
                    initRemainder = sum(initRemainder, getDecimalValue(fixed.initRemainder));
                    endTotal = sum(endTotal, getDecimalValue(fixed.endTotal));
                    endRemainder = sum(endRemainder, getDecimalValue(fixed.endRemainder));
                    totalNumber = sum(totalNumber, getDecimalValue(fixed.number));
                }
                fixes.add(fixed);
            }
            result.total = new FixedAssetsRowDto(null, null, null, "合计", getStringValue(totalNumber), null, getStringValue(countOriginalValue), null, null,
                    getStringValue(countTotalDepreciate), getStringValue(netValue), getStringValue(monthDepreciate), getStringValue(thisDepreciate), getStringValue(yearDepreciate),
                    getStringValue(initRemainder), getStringValue(endRemainder), getStringValue(endTotal), null, null, null, null, 0, 1, endPeriod);
            result.fads = fixes;
        }
        // 当月已结账，不可以添加固定资产
        result.canAdd = StringUtils.isNotBlank(closePeriod) && endPeriod.compareTo(closePeriod) <= 0 ? 1 : 0;
        return result;
    }

    /**
     * 检查是否有结转损益凭证
     */
    boolean checkAdjusting(AccountBook accountBook, List<VoucherAndRecordDto> voucherAndRecords,String endPeriod) {
        //2013和2007制度，查询是否结转损益
        if (!AS_NPO.equals(accountBook.accountingSystem)) {
            String text = "本年利润";
            if (StringUtils.equals(AS_CJT, accountBook.accountingSystem)) {
                text = "本年收益";
            }
            for (VoucherAndRecordDto var : voucherAndRecords) {
                if (var.foresubText.contains(text) && var.period.compareTo(endPeriod) == 0) {
                    return true;
                }
            }
        } else {
            boolean isRestricted = false;
            boolean isUnrestricted = false;
            for (VoucherAndRecordDto var : voucherAndRecords) {
                //限定性净资产
                if (var.foresubText.contains("限定性净资产") && var.period.compareTo(endPeriod) == 0 && var.dfmoney.compareTo(BigDecimal.ZERO) != 0) {
                    isRestricted = true;
                }
                //非限定性净资产
                if (var.foresubText.contains("非限定性净资产") && var.period.compareTo(endPeriod) == 0 && var.dfmoney.compareTo(BigDecimal.ZERO) != 0) {
                    isUnrestricted = true;
                }
            }
            return isRestricted && isUnrestricted;
        }
        return false;
    }

    /**
     * 还原固定资产
     */
    public void reductionFixedAssets(FixedAssetsRowDto fixedAssetsDto) {
        //根据id查询固定资产
        FixedAssets fixedAssets = fixedAssetsDao.findOne(fixedAssetsDto.id);
        fixedAssets.cleanPeriod = null;
        fixedAssetsDao.save(fixedAssets);
    }

    /**
     * 根据id清理某个固定资产
     */
    public FixedAssetsDto cleanFixedAssets(Long id, DetailDto detailDto) {

        if (detailDto.assetType == null) {
            detailDto.assetType = ASSET_TYPE_1.typeCode;
        }

        // 修改固定资产清理期间
        String endPeriod = detailDto.endPeriod;
        FixedAssets fa = fixedAssetsDao.findOne(id);
        fa.cleanPeriod = endPeriod;
        fixedAssetsDao.save(fa);

        DBCache cache = new DBCache();
        cache.accountBook = accountBookDao.findAccountBookById(fa.accountBookId);
        cache.fixedAssetsCategorys = fixedAssetsCategoryDao.findByAccountBookId(fa.accountBookId);
        cache.vouchers = voucherDao.findByAccountBookIdAndPeriodOrderByVoucherNoAsc(fa.accountBookId, endPeriod);
        cache.subjects = subjectDao.findByAccountBookId(fa.accountBookId);

        // 重新计算固定资产
        FixedAssetsRowDto fixed = BeanMapper.map(fa, FixedAssetsRowDto.class);
        fixed.method = getFixedMethodName(fa.method, fixed.assetType);
        fixed.endPeriod = endPeriod;
        FixedAssetsCategory fc = cache.getCategoryByNoAndAssetType(fa.fixedAssetsType, fa.assetType);
        fixed.fixedAssetsTypeName = fc.name;
        calculateAssets(fixed);

        //设置生成凭证
        FixedAssetsDto result = new FixedAssetsDto();
        result.voucher = new VoucherDto();
        result.voucher.accountBookId = fa.accountBookId;
        result.voucher.voucherNo = cache.getCurrentVoucherNo(endPeriod);
        //当前期间的最后一天
        result.voucher.voucherDate = CalendarUtils.getLastDayDateOfMonth(endPeriod);

        // 固定资产凭证借方科目
        String jfLongText = SubjectLongTextType.getSubjectLongText(cache.accountBook.accountingSystem, fa.assetType, "清理");
        Subject jfSubject = cache.getSubjectByLongText(jfLongText);
        Subject jfSubject1 = cache.getSubjectById(Long.parseLong(fixed.depreciateSubject));

        String dfLongText = SubjectLongTextType.getSubjectLongText(cache.accountBook.accountingSystem, fa.assetType, "资产");
        Subject dfSubject = cache.getSubjectByLongText(dfLongText);
        String summary = "清理固定资产";
        if (fixed.assetType.equals(ASSET_TYPE_2.typeCode)) {
            String jfLongText1 = SubjectLongTextType.getSubjectLongText(cache.accountBook.accountingSystem, fa.assetType, "折旧");
            jfSubject1 = cache.getSubjectByLongText(jfLongText1);
            summary = "清理无形资产";
        }
        List<Subject> jfSubjects = cache.getChildSubjects(jfSubject.no);
        List<Subject> jfSubjects1 = cache.getChildSubjects(jfSubject1.no);
        List<Subject> dfSubjects = cache.getChildSubjects(dfSubject.no);
        String cleanBalance = BigDecimalUtils.subtract(BigDecimalUtils.subtract(fixed.originalValue, fixed.totalDepreciate), fixed.thisDepreciate);
        String totalDepreciateBalance = BigDecimalUtils.sum(fixed.totalDepreciate, fixed.thisDepreciate);
        // 无形资产借方金额
        if (fixed.assetType.equals(ASSET_TYPE_2.typeCode)) {
            cleanBalance = fixed.totalDepreciate;
            totalDepreciateBalance = BigDecimalUtils.subtract(fixed.originalValue, fixed.totalDepreciate);
        }
        result.voucher.voucherRecords.add(new VoucherRecordDto(BigDecimal.ZERO, jfSubjects.get(0).no + " " + jfSubjects.get(0).longText, getDecimalValue(cleanBalance), summary, jfSubjects.get(0).id));
        result.voucher.voucherRecords.add(new VoucherRecordDto(BigDecimal.ZERO, jfSubjects1.get(0).no + " " + jfSubjects1.get(0).longText, getDecimalValue(totalDepreciateBalance), summary, jfSubjects1.get(0).id));
        result.voucher.voucherRecords.add(new VoucherRecordDto(getDecimalValue(fixed.originalValue), dfSubjects.get(0).no + " " + dfSubjects.get(0).longText, BigDecimal.ZERO, summary, dfSubjects.get(0).id));
        //合计值
        result.voucher.jftotal = fixed.originalValue;
        result.voucher.dftotal = fixed.originalValue;
        return result;
    }

    /**
     * 根据帐套id和期间对账
     */
    @Transactional(readOnly = true, rollbackFor = Exception.class)
    public MessageDto reconciliationFixedAssets(DBCache cache, Long id, String endPeriod, Integer assetType) {

        //没有选择期间，期间为当前帐套的最大期间
        if ("".equals(endPeriod) || endPeriod == null) {
            endPeriod = periodDao.findMaxPeriod(id);
        }
        if (assetType == null) {
            assetType = ASSET_TYPE_1.typeCode;
        }

        //固定资产的原值和累计折旧
        FixedAssetsDto fad = findByAccountBookIdAndPeriod(null, id, endPeriod, 0, assetType, null, null, null);
        FixedAssetsRowDto fard = new FixedAssetsRowDto();
        if (fad.fads != null && fad.fads.size() != 0) {
            fard = fad.total;
        } else {
            fard.endRemainder = "0.00";
            fard.originalValue = "0.00";
            fard.thisDepreciate = "0.00";
            fard.totalDepreciate = "0.00";
        }

        if (cache == null) {
            cache = new DBCache();
            cache.accountBook = accountBookDao.findAccountBookById(id);
            cache.subjects = subjectDao.findByAccountBookId(id);
            cache.balances = balanceDao.findByAccountBookIdAndPeriod(id, endPeriod);
            cache.assBalances = assBalancesDao.findByAccountBookIdAndPeriod(id, endPeriod);
        }

        // 资产数据：资产原值、资产净值（取期末剩余摊销）
        String originalValue;
        // 科目余额：固定资产、无形资产、长期待摊费用
        String gdzc;

        // 资产数据：资产累计折旧、资产累计摊销、
        String totalDepreciate;
        // 科目余额：累计折旧、累计摊销
        String ljzj;

        // 资产原值：true：相等；false：不相等
        boolean ZCYZ = false;
        // 累计折旧：true：相等；false：不相等
        boolean LJZJ = false;


        String message;

        String zcLongText = SubjectLongTextType.getSubjectLongText(cache.accountBook.accountingSystem, assetType, "资产");
        String zjLongText = SubjectLongTextType.getSubjectLongText(cache.accountBook.accountingSystem, assetType, "折旧");

        // 资产科目余额
        Subject zcSubject = cache.getSubjectByLongText(zcLongText);
        BaseBalance zcBalance = balanceService.findBalanceByCache(cache, zcSubject, endPeriod);
        // 累计折旧科目余额表
        Subject zjSubject = cache.getSubjectByLongText(zjLongText);
        BaseBalance zjBalance = new Balance();
        if (zjSubject != null) {
            zjBalance = balanceService.findBalanceByCache(cache, zjSubject, endPeriod);
        }

        // 固定资产
        if (assetType.equals(ASSET_TYPE_1.typeCode)) {
            // 比较固定资产的【资产原值=固定资产原值之和】和科目余额表"固定资产"科目余额（贷方余额显示为负数）是否相等
            if (fard.originalValue.equals(getStringValue(zcBalance.endbalance))) {
                ZCYZ = true;
            }

            // 比较固定资产的【累计折旧=期初累计折旧+本月折旧】和科目余额表"累计折旧"科目余额（借方余额显示为负数）是否相等
            BigDecimal sum = getDecimalValue(sum(fard.thisDepreciate, fard.totalDepreciate));
            if (sum.compareTo(zjBalance.endbalance) == 0) {
                LJZJ = true;
            }

            originalValue = fard.originalValue;
            gdzc = getStringValue(zcBalance.endbalance);
            totalDepreciate = getStringValue(sum);
            ljzj = getStringValue(zjBalance.endbalance);
            if (ZCYZ && LJZJ) {
                message = "平衡";
            } else {
                message = "不平衡";
            }
            return new MessageDto(message, originalValue, gdzc, totalDepreciate, ljzj, ZCYZ, LJZJ);
        }
        // 无形资产
        else if (assetType.equals(ASSET_TYPE_2.typeCode)) {
            if (!AS_NPO.equals(cache.accountBook.accountingSystem)) {

                // 比较无形资产的【资产原值=无形资产原值之和】和科目余额表"无形资产"科目余额（贷方余额显示为负数）是否相等
                if (fard.originalValue.equals(getStringValue(zcBalance.endbalance))) {
                    ZCYZ = true;
                }

                // 比较无形资产的【累计折旧=期末累计摊销】和科目余额表"累计摊销"科目余额（借方余额显示为负数）是否相等
                if (getDecimalValue(fard.endTotal).compareTo(zjBalance.endbalance) == 0) {
                    LJZJ = true;
                }
                originalValue = fard.originalValue;
                gdzc = getStringValue(zcBalance.endbalance);
                totalDepreciate = fard.endTotal;
                ljzj = getStringValue(zjBalance.endbalance);
                if (ZCYZ && LJZJ) {
                    message = "平衡";
                } else {
                    message = "不平衡";
                }
                return new MessageDto(message, originalValue, gdzc, totalDepreciate, ljzj, ZCYZ, LJZJ);
            }
            // 民间非营利没有 累计摊销 科目
            else {
                // 比较无形资产的【资产原值=无形资产期末剩余摊销之和】和科目余额表"无形资产"科目余额（贷方余额显示为负数）是否相等
                if (fard.endRemainder.equals(getStringValue(zcBalance.endbalance))) {
                    ZCYZ = true;
                }
                originalValue = fard.endRemainder;
                gdzc = getStringValue(zcBalance.endbalance);
                if (ZCYZ) {
                    message = "平衡";
                } else {
                    message = "不平衡";
                }
                return new MessageDto(message, originalValue, gdzc, ZCYZ);
            }

        }
        // 长期待摊费用对账
        else {
            // 比较长期待摊费用的【资产原值=长期待摊费用期末剩余摊销之和】和科目余额表"长期待摊费用"科目余额（贷方余额显示为负数）是否相等
            if (fard.endRemainder.equals(getStringValue(zcBalance.endbalance))) {
                ZCYZ = true;
            }
            originalValue = fard.endRemainder;
            gdzc = getStringValue(zcBalance.endbalance);
            if (ZCYZ) {
                message = "平衡";
            } else {
                message = "不平衡";
            }
            return new MessageDto(message, originalValue, gdzc, ZCYZ);
        }
    }

    /**
     * 导出固定资产excel
     */
    @Transactional(readOnly = true, rollbackFor = Exception.class)
    MyWorkbook getAssetsWorkbook(DBCache cache, Long accountBookId, String endPeriod, Integer assetType) {

        FixedAssetsDto fixedAssetsDto = findByAccountBookIdAndPeriod(cache, accountBookId, endPeriod, 0, assetType, null, null, null);
        fixedAssetsDto.fads.add(fixedAssetsDto.total);
        List<FixedAssetsRowDto> result = fixedAssetsDto.fads;

        // 创建工作簿
        MyWorkbook workbook = new MyWorkbook();

        // 根据资产类别，获取Workbook
        if (assetType.equals(ASSET_TYPE_1.typeCode)) {
            workbook = getAssetsWorkbook1(workbook, result, accountBookId, endPeriod);
        }
        else if (assetType.equals(ASSET_TYPE_2.typeCode)) {
            workbook = getAssetsWorkbook2(workbook, result, accountBookId, endPeriod);
        }
        else {
            workbook = getAssetsWorkbook3(workbook, result, accountBookId, endPeriod);
        }

        // 下载
        return workbook;
    }

    /**
     * 获取固定资产 Workbook
     */
    MyWorkbook getAssetsWorkbook1(MyWorkbook workbook, List<FixedAssetsRowDto> result, Long accountBookId, String endPeriod) {
        AccountBook book = accountBookDao.findAccountBookById(accountBookId);

        workbook.createSheet("固定资产", new int[]{3003, 2928, 2928, 2636, 3003, 3149, 2636, 3003, 3149, 3149, 3149, 2928, 3149, 2928, 6880});
        workbook.mergeCell("A1,T1 A2,E2 F2,L2 M2,T2");
        // 添加副标题
        workbook.addTitle("A1", "固定资产");
        workbook.addLeftSubTitle("A2", "编制单位：" + book.bookName);
        workbook.addCenterSubTitle("F2", PeriodUtils.getDownloadPeriod(endPeriod, endPeriod));
        workbook.addRightSubTitle("L2", "单位：元");
        workbook.addHeaders("A3", new String[]{"资产名称", "数量", "购买日期", "原值", "期限（月）", "残值率（%）", "期初累计折旧", "期初剩余折旧", "期初净值", "本月折旧", "期末累计折旧", "期末净值", "本年累计折旧", "费用科目", "折旧科目", "录入期间", "资产类别编码", "折旧方法", "是否清理", "备注"});

        // 设置excel数据
        for (int i = 0; i < result.size(); i++) {
            if (result.get(i) != null) {
                int rowIndex = 4 + i;
                FixedAssetsRowDto data = result.get(i);
                String feeText = "";
                String depreciateText = "";
                if (data.feeSubjectOBJ != null) {
                    if (data.feeSubject.contains(".")) {
                        feeText = data.feeSubjectOBJ.no + data.feeSubjectOBJ.dimAccountNo + " " + data.feeSubjectOBJ.longText + data.feeSubjectOBJ.dimAccountName;
                    } else {
                        feeText = data.feeSubjectOBJ.no + " " + data.feeSubjectOBJ.longText;
                    }
                }
                if (data.depreciateSubjectOBJ != null) {
                    if (data.depreciateSubject.contains(".")) {
                        depreciateText = data.depreciateSubjectOBJ.no + data.depreciateSubjectOBJ.dimAccountNo + " " + data.depreciateSubjectOBJ.longText + data.depreciateSubjectOBJ.dimAccountName;
                    } else {
                        depreciateText = data.depreciateSubjectOBJ.no + " " + data.depreciateSubjectOBJ.longText;
                    }
                }
                workbook.addCenterData("A" + rowIndex, data.assetsName);
                workbook.addBigDecimalData("B" + rowIndex, data.number);
                workbook.addCenterData("C" + rowIndex, data.buyPeriod);
                workbook.addBigDecimalData("D" + rowIndex, data.originalValue);
                workbook.addCenterData("E" + rowIndex, data.useMonth);
                workbook.addBigDecimalData("F" + rowIndex, data.oldValueRate);
                workbook.addBigDecimalData("G" + rowIndex, data.totalDepreciate);
                workbook.addBigDecimalData("H" + rowIndex, data.initRemainder);
                workbook.addBigDecimalData("I" + rowIndex, data.netValue);
                workbook.addBigDecimalData("J" + rowIndex, data.thisDepreciate);
                workbook.addBigDecimalData("K" + rowIndex, data.endTotal);
                workbook.addBigDecimalData("L" + rowIndex, data.endRemainder);
                workbook.addBigDecimalData("M" + rowIndex, data.yearDepreciate);
                workbook.addCenterData("N" + rowIndex, feeText);
                workbook.addCenterData("O" + rowIndex, depreciateText);
                workbook.addCenterData("P" + rowIndex, data.writePeriod);
                workbook.addCenterData("Q" + rowIndex, data.fixedAssetsType);
                workbook.addCenterData("R" + rowIndex, data.method);
                if (StringUtils.isNotBlank(data.cleanPeriod)) {
                    workbook.addCenterData("S" + rowIndex, "是");
                } else {
                    workbook.addCenterData("S" + rowIndex, "");
                }
                workbook.addCenterData("T" + rowIndex, data.remark);
            }
        }
        // 下载
        return workbook;
    }

    /**
     * 获取无形资产 Workbook
     */
    MyWorkbook getAssetsWorkbook2(MyWorkbook workbook, List<FixedAssetsRowDto> result, Long accountBookId, String endPeriod) {
        AccountBook book = accountBookDao.findAccountBookById(accountBookId);

        workbook.createSheet("无形资产", new int[]{3003, 2928, 2928, 2636, 3003, 3149, 2636, 3003, 3149, 3149, 3149, 2928, 3149, 2928, 6880});
        workbook.mergeCell("A1,Q1 A2,E2 F2,K2 L2,Q2");
        // 添加副标题
        workbook.addTitle("A1", "无形资产");
        workbook.addLeftSubTitle("A2", "编制单位：" + book.bookName);
        workbook.addCenterSubTitle("F2", PeriodUtils.getDownloadPeriod(endPeriod, endPeriod));
        workbook.addRightSubTitle("L2", "单位：元");
        workbook.addHeaders("A3", new String[]{"资产名称", "购买日期", "原值", "期限（月）", "期初累计摊销", "期初净值", "本月摊销", "期末累计摊销", "期末净值", "本年累计摊销", "费用科目", "摊销科目", "录入期间", "资产类别编码", "折旧方法", "是否清理", "备注"});

        // 设置excel数据
        for (int i = 0; i < result.size(); i++) {
            if (result.get(i) != null) {
                int rowIndex = 4 + i;
                FixedAssetsRowDto data = result.get(i);
                String feeText = "";
                String depreciateText = "";
                if (data.feeSubjectOBJ != null) {
                    if (data.feeSubject.contains(".")) {
                        feeText = data.feeSubjectOBJ.no + data.feeSubjectOBJ.dimAccountNo + " " + data.feeSubjectOBJ.longText + data.feeSubjectOBJ.dimAccountName;
                    } else {
                        feeText = data.feeSubjectOBJ.no + " " + data.feeSubjectOBJ.longText;
                    }
                }
                if (data.depreciateSubjectOBJ != null) {
                    if (data.depreciateSubject.contains(".")) {
                        depreciateText = data.depreciateSubjectOBJ.no + data.depreciateSubjectOBJ.dimAccountNo + " " + data.depreciateSubjectOBJ.longText + data.depreciateSubjectOBJ.dimAccountName;
                    } else {
                        depreciateText = data.depreciateSubjectOBJ.no + " " + data.depreciateSubjectOBJ.longText;
                    }
                }
                workbook.addCenterData("A" + rowIndex, data.assetsName);
                workbook.addCenterData("B" + rowIndex, data.buyPeriod);
                workbook.addBigDecimalData("C" + rowIndex, data.originalValue);
                workbook.addCenterData("D" + rowIndex, data.useMonth);
                workbook.addBigDecimalData("E" + rowIndex, data.totalDepreciate);
                workbook.addBigDecimalData("F" + rowIndex, data.initRemainder);
                workbook.addBigDecimalData("G" + rowIndex, data.thisDepreciate);
                workbook.addBigDecimalData("H" + rowIndex, data.endTotal);
                workbook.addBigDecimalData("I" + rowIndex, data.endRemainder);
                workbook.addBigDecimalData("J" + rowIndex, data.yearDepreciate);
                workbook.addCenterData("K" + rowIndex, feeText);
                workbook.addCenterData("L" + rowIndex, depreciateText);
                workbook.addCenterData("M" + rowIndex, data.writePeriod);
                workbook.addCenterData("N" + rowIndex, data.fixedAssetsType);
                workbook.addCenterData("O" + rowIndex, data.method);
                if (StringUtils.isNotBlank(data.cleanPeriod)) {
                    workbook.addCenterData("P" + rowIndex, "是");
                } else {
                    workbook.addCenterData("P" + rowIndex, "");
                }
                workbook.addCenterData("Q" + rowIndex, data.remark);
            }
        }
        // 下载
        return workbook;
    }

    /**
     * 获取长期待摊费用 Workbook
     */
    MyWorkbook getAssetsWorkbook3(MyWorkbook workbook, List<FixedAssetsRowDto> result, Long accountBookId, String endPeriod) {
        AccountBook book = accountBookDao.findAccountBookById(accountBookId);

        workbook.createSheet("长期待摊费用", new int[]{3003, 2928, 2928, 2636, 3003, 3149, 2636, 3003, 3149, 3149, 3149, 2928, 3149, 2928, 6880});
        workbook.mergeCell("A1,P1 A2,D2 E2,K2 L2,P2");
        // 添加副标题
        workbook.addTitle("A1", "长期待摊费用");
        workbook.addLeftSubTitle("A2", "编制单位：" + book.bookName);
        workbook.addCenterSubTitle("E2", PeriodUtils.getDownloadPeriod(endPeriod, endPeriod));
        workbook.addRightSubTitle("L2", "单位：元");
        workbook.addHeaders("A3", new String[]{"资产名称", "购买日期", "原值", "期限（月）", "期初累计摊销", "期初净值", "本月摊销", "期末累计摊销", "期末净值", "本年累计摊销", "费用科目", "录入期间", "资产类别编码", "折旧方法", "是否清理", "备注"});

        // 设置excel数据
        for (int i = 0; i < result.size(); i++) {
            if (result.get(i) != null) {
                int rowIndex = 4 + i;
                FixedAssetsRowDto data = result.get(i);
                String feeText = "";
                if (data.feeSubjectOBJ != null) {
                    if (data.feeSubject.contains(".")) {
                        feeText = data.feeSubjectOBJ.no + data.feeSubjectOBJ.dimAccountNo + " " + data.feeSubjectOBJ.longText + data.feeSubjectOBJ.dimAccountName;
                    } else {
                        feeText = data.feeSubjectOBJ.no + " " + data.feeSubjectOBJ.longText;
                    }
                }
                workbook.addCenterData("A" + rowIndex, data.assetsName);
                workbook.addCenterData("B" + rowIndex, data.buyPeriod);
                workbook.addBigDecimalData("C" + rowIndex, data.originalValue);
                workbook.addCenterData("D" + rowIndex, data.useMonth);
                workbook.addBigDecimalData("E" + rowIndex, data.totalDepreciate);
                workbook.addBigDecimalData("F" + rowIndex, data.initRemainder);
                workbook.addBigDecimalData("G" + rowIndex, data.thisDepreciate);
                workbook.addBigDecimalData("H" + rowIndex, data.endTotal);
                workbook.addBigDecimalData("I" + rowIndex, data.endRemainder);
                workbook.addBigDecimalData("J" + rowIndex, data.yearDepreciate);
                workbook.addCenterData("K" + rowIndex, feeText);
                workbook.addCenterData("L" + rowIndex, data.writePeriod);
                workbook.addCenterData("M" + rowIndex, data.fixedAssetsType);
                workbook.addCenterData("N" + rowIndex, data.method);
                if (StringUtils.isNotBlank(data.cleanPeriod)) {
                    workbook.addCenterData("O" + rowIndex, "是");
                } else {
                    workbook.addCenterData("O" + rowIndex, "");
                }
                workbook.addCenterData("P" + rowIndex, data.remark);
            }
        }
        // 下载
        return workbook;
    }
    
    /**
     * 下载固定资产PDF
     */
    @Transactional(readOnly = true, rollbackFor = Exception.class)
    MyDocument createGdzcPDF(List<FixedAssetsRowDto> assets, AccountBook book, String period) {
        //查询需要展示到pdf中的数据结果
        MyDocument document = new MyDocument(false, 40, false);
        document.newPage(true);
        document.addTitles("固定资产", period, period, book.bookName, null);
        // 设置表头
        PdfPTable headTable = createPdfPTable(new float[]{10, 15, 6, 10, 15, 8, 8, 15, 15, 15});
        addHeaders(headTable, new String[]{"编码", "资产名称", "数量", "购买日期", "原值", "期限(月)", "残值率(%)", "本月折旧", "期末累计折旧", "期末净值"});
        document.addTable(headTable);

        // 创建表格并设置列宽
        PdfPTable dataTable = createPdfPTable(new float[]{10, 15, 6, 10, 15, 8, 8, 15, 15, 15});
        for (FixedAssetsRowDto asset : assets) {
            if (StringUtils.equals(asset.assetsName, "合计")) {
                addCenterData(dataTable, "");
                addCenterData(dataTable, "合计");
                addBigDecimalData(dataTable, asset.number);
                addCenterData(dataTable, "");
                addBigDecimalData(dataTable, asset.originalValue);
                addCenterData(dataTable, "");
                addBigDecimalData(dataTable, asset.oldValueRate);
                addBigDecimalData(dataTable, asset.thisDepreciate);
                addBigDecimalData(dataTable, asset.endTotal);
                addBigDecimalData(dataTable, asset.endRemainder);
            }
            else {
                addCenterData(dataTable, asset.code);
                addLeftData(dataTable, asset.assetsName); 
                addBigDecimalData(dataTable, asset.number);
                addCenterData(dataTable, asset.buyPeriod);
                addBigDecimalData(dataTable, asset.originalValue);
                addCenterData(dataTable, asset.useMonth);
                addBigDecimalData(dataTable, asset.oldValueRate);
                addBigDecimalData(dataTable, asset.thisDepreciate);
                addBigDecimalData(dataTable, asset.endTotal);
                addBigDecimalData(dataTable, asset.endRemainder);
            }
        }
        document.addTable(dataTable);
        return document;
    }
    
    /**
     * 下载无形资产PDF
     */
    @Transactional(readOnly = true, rollbackFor = Exception.class)
    MyDocument createWxzcPDF(List<FixedAssetsRowDto> assets, AccountBook book, String period) {
        //查询需要展示到pdf中的数据结果
        MyDocument document = new MyDocument(false, 40, false);
        document.newPage(true);
        document.addTitles("无形资产", period, period, book.bookName, null);
        // 设置表头
        PdfPTable headTable = createPdfPTable(new float[]{10, 15, 10, 15, 8, 8, 15, 15, 15});
        addHeaders(headTable, new String[]{"编码", "资产名称", "购买日期", "原值", "期限(月)", "残值率(%)", "本月摊销", "期末累计摊销", "期末净值"});
        document.addTable(headTable);

        // 创建表格并设置列宽
        PdfPTable dataTable = createPdfPTable(new float[]{10, 15, 10, 15, 8, 8, 15, 15, 15});
        for (FixedAssetsRowDto asset : assets) {
            if (StringUtils.equals(asset.assetsName, "合计")) {
                addCenterData(dataTable, "");
                addCenterData(dataTable, "合计");
                addCenterData(dataTable, "");
                addBigDecimalData(dataTable, asset.originalValue);
                addCenterData(dataTable, "");
                addBigDecimalData(dataTable, asset.oldValueRate);
                addBigDecimalData(dataTable, asset.thisDepreciate);
                addBigDecimalData(dataTable, asset.endTotal);
                addBigDecimalData(dataTable, asset.endRemainder);
            }
            else {
                addCenterData(dataTable, asset.code);
                addLeftData(dataTable, asset.assetsName);
                addCenterData(dataTable, asset.buyPeriod);
                addBigDecimalData(dataTable, asset.originalValue);
                addCenterData(dataTable, asset.useMonth);
                addBigDecimalData(dataTable, asset.oldValueRate);
                addBigDecimalData(dataTable, asset.thisDepreciate);
                addBigDecimalData(dataTable, asset.endTotal);
                addBigDecimalData(dataTable, asset.endRemainder);
            }
        }
        document.addTable(dataTable);
        return document;
    }

     /**
     * 下载长期待摊费用PDF
     */
    @Transactional(readOnly = true, rollbackFor = Exception.class)
    MyDocument createCqdtfyPDF(List<FixedAssetsRowDto> assets, AccountBook book, String period) {
        //查询需要展示到pdf中的数据结果
        MyDocument document = new MyDocument(false, 40, false);
        document.newPage(true);
        document.addTitles("长期待摊费用", period, period, book.bookName, null);
        // 设置表头
        PdfPTable headTable = createPdfPTable(new float[]{10, 15, 10, 15, 10, 15, 15, 15});
        addHeaders(headTable, new String[]{"编码", "资产名称", "启用日期", "原值", "期限(月)", "本月摊销", "期末累计摊销", "期末净值"});
        document.addTable(headTable);

        // 创建表格并设置列宽
        PdfPTable dataTable = createPdfPTable(new float[]{10, 15, 10, 15, 10, 15, 15, 15});
        for (FixedAssetsRowDto expense : assets) {
            if (StringUtils.equals(expense.assetsName, "合计")) {
                addCenterData(dataTable, "");
                addCenterData(dataTable, "合计");
                addCenterData(dataTable, "");
                addBigDecimalData(dataTable, expense.originalValue);
                addCenterData(dataTable, "");
                addBigDecimalData(dataTable, expense.thisDepreciate);
                addBigDecimalData(dataTable, expense.endTotal);
                addBigDecimalData(dataTable, expense.endRemainder);
            }
            else {
                addCenterData(dataTable, expense.code);
                addLeftData(dataTable, expense.assetsName);
                addCenterData(dataTable, expense.buyPeriod);
                addBigDecimalData(dataTable, expense.originalValue);
                addCenterData(dataTable, expense.useMonth);
                addBigDecimalData(dataTable, expense.thisDepreciate);
                addBigDecimalData(dataTable, expense.endTotal);
                addBigDecimalData(dataTable, expense.endRemainder);
            }
        }
        document.addTable(dataTable);
        return document;
    }
}
