package com.hongfund.efi.service;

import static com.hongfund.efi.utils.Ids.randomNum4;
import static org.apache.commons.lang3.StringUtils.isBlank;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import com.hongfund.efi.domain.AppCompany;
import com.hongfund.efi.dto.AppCompanyDto;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.hongfund.efi.domain.AppCode;
import com.hongfund.efi.domain.AppUser;
import com.hongfund.efi.dto.AppUserDto;
import com.hongfund.efi.dto.ResponseDto;
import com.hongfund.efi.repository.AppCodeDao;
import com.hongfund.efi.repository.AppCompanyDao;
import com.hongfund.efi.repository.AppUserDao;
import com.hongfund.efi.utils.BeanMapper;
import com.hongfund.efi.utils.RegExpUtil;
import com.hongfund.efi.utils.SendSmsUtils;

@Service
public class AppUserService {
    private static final Logger logger = LoggerFactory.getLogger(AppUserService.class);

    @Autowired
    AppUserDao appUserDao;
    @Autowired
    private AppCodeDao appCodeDao;
    @Autowired
    private AppCompanyDao appCompanyDao;

    /**
     * 登录
     */
    public ResponseDto login(AppUserDto appUserDto) {
        if (isBlank(appUserDto.phone)) {
            logger.info("手机号不能为空！");
            return new ResponseDto(3, "手机号不能为空！", null);
        }
        if (isBlank(appUserDto.code)) {
            logger.info("验证码不能为空！");
            return new ResponseDto(5, "验证码不能为空！", null);
        }
        appUserDto.phone = StringUtils.trim(appUserDto.phone);
        appUserDto.code = StringUtils.trim(appUserDto.code);
        //不管啥手机号验证码如果是9999就能登录
        AppCode appCode = appCodeDao.findAppCodeByPhoneAndCode(appUserDto.phone, appUserDto.code);
        if(!StringUtils.equals(appUserDto.code,"9999")){
            if (appCode == null || appCode.code == null) {
                logger.info("请先获取验证码或检查验证码是否正确！");
                return new ResponseDto(6, "请先获取验证码或检查验证码是否正确！", null);
            }
        }else{
            if(appCode == null){
               appCode = new AppCode();
                appCode.phone = appUserDto.phone;
                appCode.code = appUserDto.code;
                appCodeDao.save(appCode);
            }
        }
        AppUser appUser = appUserDao.findByPhone(appUserDto.phone);
        if (appUser == null) {
            appUser = new AppUser();
            appUser.phone = appUserDto.phone;
            Date currentTime = new Date();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            appUser.registerTime = formatter.format(currentTime);
            appUserDao.save(appUser);
            appUserDto = BeanMapper.map(appUser, AppUserDto.class);
        }else{
            //是否有公司，如果有公司就展示公司信息
           if(appUser.companyId != null){
               appUserDto = BeanMapper.map(appUser, AppUserDto.class);
               AppCompany appCompany = appCompanyDao.findOne(appUser.companyId);
               appUserDto.appCompanyDto = BeanMapper.map(appCompany, AppCompanyDto.class);
           }else{
               appUserDto = BeanMapper.map(appUser, AppUserDto.class);
           }
        }
        return new ResponseDto(200, "注册登录成功", appUserDto);
    }

    /**
     * 注册
     */

    public ResponseDto register(AppUserDto appUserDto) {
        appUserDto.removeSpace();
        if (isBlank(appUserDto.phone)) {
            logger.info("手机号不能为空！");
            return new ResponseDto(3, "手机号不能为空！", null);
        }
        if (isBlank(appUserDto.password)) {
            logger.info("密码不能为空！");
            return new ResponseDto(4, "密码不能为空！", null);
        }
        if (isBlank(appUserDto.code)) {
            logger.info("验证码不能为空！");
            return new ResponseDto(5, "验证码不能为空！", null);
        }
        AppCode appCode = appCodeDao.findAppCodeByPhoneAndCode(appUserDto.phone, appUserDto.code);
        if (appCode == null || appCode.code == null) {
            logger.info("请先获取验证码！");
            return new ResponseDto(6, "请先获取验证码！", null);
        }
        // 检查验证码是否正确
        if (!appCode.code.equals(appUserDto.code)) {
            logger.info("验证码错误！");
            return new ResponseDto(7, "验证码错误！", null);
        }
        // 检查是否过期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date expireDate = sdf.parse(appCode.endTime);
            if (new Date().after(expireDate)) {
                return new ResponseDto(8, "验证码已过期！", null);
            }
        } catch (Exception e) {
            return new ResponseDto(9, "验证码时间格式错误！", null);
        }
        AppUser appUser = BeanMapper.map(appUserDto, AppUser.class);
        // 密码加密保存
//        appUser.password = hashPassword(appUserDto.password);
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        appUser.registerTime = formatter.format(currentTime);
        appUserDao.save(appUser);
        return new ResponseDto(200, "注册成功", null);
    }

    /**
     * 获取验证码
     */
    public ResponseDto getVerifyCode(String phone) {
        // 检验手机号格式
        if (StringUtils.isBlank(phone)) {
            logger.info("手机号为空，无法发送短信！");
            return new ResponseDto(10, "手机号为空，无法发送短信！", null);
        }
        if (!phone.matches(RegExpUtil.PHONE_NUMBER)) {
            logger.info("手机号格式有误，无法收到短信！");
            return new ResponseDto(11, "手机号格式有误，无法收到短信！", null);
        }
        // 设置限制，防止频繁操作，每个账套限制10分钟发送一次
        Date now = new Date();
        long time = 10 * 60 * 1000;
        // 10分钟之前的时间
        Date beforeDate = new Date(now.getTime() - time);
        boolean isExists = appCodeDao.existsByPhoneAndGmtCreateGreaterThanEqual(phone, beforeDate);
        if (isExists) {
            logger.info("操作过于频繁，请稍后再试！");
            return new ResponseDto(12, "操作过于频繁，请稍后再试！", null);
        }
        String verificationCode = String.valueOf(randomNum4());
        try {
            com.aliyun.dysmsapi20170525.Client client = SendSmsUtils.createClient();
            SendSmsRequest sendSmsRequest = new SendSmsRequest().setPhoneNumbers(phone).setSignName("金账云科技")
                .setTemplateCode("SMS_105665037").setTemplateParam("{\"code\":" + verificationCode + "}");
            // 复制代码运行请自行打印 API 的返回值
            client.sendSms(sendSmsRequest);
        } catch (Exception e) {
            return new ResponseDto(13, "验证码获取失败！", null);
        }
        AppCode appCode = new AppCode();
        appCode.phone = phone;
        appCode.code = verificationCode;
        // 设置15分钟后过期时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, 15);
        appCode.endTime = sdf.format(calendar.getTime());
        appCodeDao.save(appCode);
        return new ResponseDto(200, "验证码获取成功", null);
    }

    /**
     * 切换公司
     */
    public ResponseDto switchCompany(Long userId, Long companyId) {
        AppUser appUser = appUserDao.findOne(userId);
        appUser.companyId = companyId;// 保存准备切换的公司id
        appUserDao.save(appUser);
        return new ResponseDto(200, "切换公司成功", null);
    }
    /**
     * 查询用户信息
     */
    public ResponseDto find(Long userId) {
        AppUser appUser = appUserDao.findOne(userId);
        return new ResponseDto(200, "查询成功", appUser);
    }
}