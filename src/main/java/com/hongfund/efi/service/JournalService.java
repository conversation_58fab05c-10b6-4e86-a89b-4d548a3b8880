package com.hongfund.efi.service;

import static com.hongfund.efi.profile.BankAccountType.*;
import static com.hongfund.efi.profile.ImportJournalProfiles.*;
import static com.hongfund.efi.profile.JournalType.*;
import static com.hongfund.efi.profile.NewOperateType.NEW_OPERATE_TYPE_20;
import static com.hongfund.efi.profile.ReceiptInfo.*;
import static com.hongfund.efi.profile.SubjectDir.DEBIT;
import static com.hongfund.efi.profile.SubjectNoProfiles.*;
import static com.hongfund.efi.profile.SystemSettingProfile.*;
import static com.hongfund.efi.profile.VoucherCategory.JOURNAL;
import static com.hongfund.efi.utils.BigDecimalUtils.getDecimalValue;
import static com.hongfund.efi.utils.CheckUtils.*;
import static com.hongfund.efi.utils.FormatUtils.DECIMAL_FORMAT_2;
import static com.hongfund.efi.utils.NoUtils.VOUCHER_NO_LENGTH;
import static com.hongfund.efi.utils.NoUtils.getNextVoucherNo;
import static com.hongfund.efi.utils.PdfUtils.parsePdfToPng;
import static com.hongfund.efi.utils.PdfUtils.pdfTransferImage;
import static com.hongfund.efi.utils.PeriodUtils.getDateStr;
import static com.hongfund.efi.utils.PeriodUtils.getLastDate;
import static com.hongfund.efi.utils.PoiUtils.*;
import static com.hongfund.efi.utils.RegExpUtil.PERIOD_CHAR;
import static com.hongfund.efi.utils.ZipUtils.getByte;
import static com.hongfund.efi.utils.icbcUtils.IcbcUtils.BASE_FILE_URL;
import static org.apache.poi.ss.usermodel.DateUtil.getJavaDate;
import static org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.io.RandomAccessReadBuffer;
import org.apache.pdfbox.multipdf.Splitter;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.hongfund.efi.domain.*;
import com.hongfund.efi.dto.*;
import com.hongfund.efi.profile.JournalType;
import com.hongfund.efi.repository.*;
import com.hongfund.efi.service.exception.ErrorCode;
import com.hongfund.efi.service.exception.ServiceException;
import com.hongfund.efi.utils.*;
import com.hongfund.efi.utils.bcmUtils.BcmUtils;
import com.hongfund.efi.utils.bcmUtils.domain.BankAccountDetailBcm;
import com.hongfund.efi.utils.bcmUtils.domain.BankAccountReceiptBcm;
import com.hongfund.efi.utils.bcmUtils.repository.BankAccountDetailBcmDao;
import com.hongfund.efi.utils.bcmUtils.repository.BankAccountReceiptBcmDao;
import com.hongfund.efi.utils.bcmUtils.response.pptm.B2BYQFYQFU1016ResponseV1;
import com.hongfund.efi.utils.exocr.ExocrJournalStatementSimpleDto;
import com.hongfund.efi.utils.exocr.ExocrResponseDto;
import com.hongfund.efi.utils.icbcUtils.IcbcUtils;
import com.hongfund.efi.utils.icbcUtils.domain.BankAccountDetailIcbc;
import com.hongfund.efi.utils.icbcUtils.domain.BankAccountReceiptIcbc;
import com.hongfund.efi.utils.icbcUtils.dto.AgreeNoDto;
import com.hongfund.efi.utils.icbcUtils.dto.MyBankAccountDetailDto;
import com.hongfund.efi.utils.icbcUtils.repository.BankAccountDetailIcbcDao;
import com.hongfund.efi.utils.icbcUtils.repository.BankAccountReceiptIcbcDao;
import com.hongfund.efi.utils.pabcUtils.PabcUtils;
import com.hongfund.efi.utils.pabcUtils.doman.BankAccountDetailPabc;
import com.hongfund.efi.utils.pabcUtils.doman.BankAccountReceiptPabc;
import com.hongfund.efi.utils.pabcUtils.dto.PabcDownloadDto;
import com.hongfund.efi.utils.pabcUtils.dto.PabcJournalDetailDto;
import com.hongfund.efi.utils.pabcUtils.repository.BankAccountDetailPabcDao;
import com.hongfund.efi.utils.pabcUtils.repository.BankAccountReceiptPabcDao;

/**
 * <AUTHOR>
 */
@Transactional(rollbackFor = Exception.class)
@Service
public class JournalService {
	private static final Logger logger = LoggerFactory.getLogger(JournalService.class);
    private static final int MAX_LENGTH = 64; // 数据库字段的最大长度

	/**
	 * 工行订单缓存
	 */
	private Cache<String, BankAccountDetailIcbc> orderList;

	private final JsonMapper jsonMapper = JsonMapper.nonEmptyMapper();

	@Value("${spring.profiles.active}")
	private String active;

	@Autowired
	private AccountBookDao accountBookDao;
	@Autowired
	private VoucherDao voucherDao;
	@Autowired
	private VoucherRecordDao voucherRecordDao;
	@Autowired
	private JournalDao journalDao;
	@Autowired
	private SubjectDao subjectDao;
	@Autowired
	private DimItemDao dimItemDao;
	@Autowired
	private DimCustomerDao dimCustomerDao;
	@Autowired
	private DimProviderDao dimProviderDao;
	@Autowired
	private DimDepartmentDao dimDepartmentDao;
	@Autowired
	private DimEmployeeDao dimEmployeeDao;
	@Autowired
	private DimInventoryDao dimInventoryDao;
	@Autowired
	private VoucherTemplateDao voucherTemplateDao;
	@Autowired
    private SubjectDimRelationDao subjectDimRelationDao;
	@Autowired
    private BalanceDao balanceDao;
	@Autowired
    private DimBalanceDao dimBalanceDao;
	@Autowired
	private CashFlowItemDao cashFlowItemDao;
	@Autowired
	private BankAccountDao bankAccountDao;
	@Autowired
	private BankAccountDetailIcbcDao bankAccountDetailIcbcDao;
	@Autowired
	private BankAccountDetailPabcDao bankAccountDetailPabcDao;
	@Autowired
	private BankAccountDetailBcmDao bankAccountDetailBcmDao;
	@Autowired
	private BankAccountReceiptPabcDao bankAccountReceiptPabcDao;
	@Autowired
	private BankAccountReceiptBcmDao bankAccountReceiptBcmDao;
	@Autowired
	private BankAccountReceiptIcbcDao bankAccountReceiptIcbcDao;
	@Autowired
	private SystemSettingDao systemSettingDao;
    @Autowired
    private BankTaskDao bankTaskDao;
	@Autowired
	private BankTaskService bankTaskService;

	@Autowired
	private BalanceService balanceService;
	@Autowired
	private SubjectService subjectService;
	@Autowired
	private VoucherService voucherService;
	@Autowired
	private FileHistoryService fileHistoryService;
	@Autowired
	private CollectionPeriodService collectionPeriodService;
	@Autowired
	private AccountService accountService;
	@Autowired
	private SystemSettingService systemSettingService;
	@Autowired
	private NewOperationLogService newOperationLogService;

	@Autowired
	RestTemplate restTemplate;
    @Autowired
    private AccountBookInfoDao accountBookInfoDao;

	@PostConstruct
	public void init() {
		//初始化时获得缓存用户的集合
		orderList = CacheBuilder.newBuilder().maximumSize(100).expireAfterAccess(3, TimeUnit.HOURS)
				.build();
	}

	/**
	 * 添加日记账
	 */
	public JournalDto addJournal(JournalDto journalDto) {
		// 格式检查
		checkNumber(journalDto.id, "id错误！");
		checkNumberNotBlank(journalDto.accountBookId, "账套id错误！");
		checkNumber(journalDto.voucherId, "凭证id错误！");
		checkNumber(journalDto.voucherTemplateId, "凭证模板id错误！");
		checkDateNotBlank(journalDto.journalDate, "日期错误！");
		checkBalance(journalDto.income, "收入金额错误！");
		checkBalance(journalDto.pay, "支出金额错误！");
		checkPeriod(journalDto.period, "期间错误！");
		checkNumberNotBlank(journalDto.journalType, "类型错误！");
		checkNumberNotBlank(journalDto.incomePayType, "收支类型错误！");
		checkLength(journalDto.oppositeAccountName, 255, "对方户名太长，无法保存！");

		journalDto.summary = StringUtils.trim(journalDto.summary);
		journalDto.oppositeAccountName = StringUtils.trim(journalDto.oppositeAccountName);

		// 设置默认值
		if (StringUtils.isBlank(journalDto.income)) {
			journalDto.income = "0";
		}
		if (StringUtils.isBlank(journalDto.pay)) {
			journalDto.pay = "0";
		}
		if (StringUtils.isBlank(journalDto.status)) {
			journalDto.status = "0";
		}
		Long accountBookId = Long.parseLong(journalDto.accountBookId);
		AccountBook accountBook = accountBookDao.findAccountBookById(accountBookId);
		if (StringUtils.isBlank(journalDto.period)) {
			journalDto.period = accountBook.currentPeriod;
		}
		if (StringUtils.isBlank(journalDto.openingBank)) {
			journalDto.openingBank = null;
		}
		else {
			journalDto.openingBank = journalDto.openingBank.trim();
		}

		if (accountBook.closePeriod != null && journalDto.period.compareTo(accountBook.closePeriod) <= 0) {
			logger.info("已结账的期间无法添加日记账!");
			throw new ServiceException("已结账的期间无法添加日记账!", ErrorCode.BAD_REQUEST);
		}

		Journal journal = BeanMapper.map(journalDto, Journal.class);
		if ((BigDecimal.ZERO.compareTo(journal.income) == 0) == (BigDecimal.ZERO.compareTo(journal.pay) == 0)) {
			logger.info("收入和支出金额错误！");
			throw new ServiceException("收入和支出金额错误！", ErrorCode.BAD_REQUEST);
		}
		Journal result = journalDao.save(journal);
		// 生成凭证
		DBCache dbCache = new DBCache();
		dbCache.systemSettings = systemSettingDao.findByAccountBookId(Long.valueOf(journalDto.accountBookId));
		int isAuto = systemSettingService.getSystemSettingValue(dbCache, JOURNAL_AUTO.keyword).intValue();
		if (isAuto == 1) {
			List<Journal> list = new ArrayList<>();
			list.add(result);
			createVoucher(list, null);
		}
		return BeanMapper.map(result, JournalDto.class);
	}

	/**
	 * 添加日记账
	 */
	private Journal addJournal(DBCache cache, JournalDto journalDto, String currentPeriod) {
		// 格式检查
		checkNumber(journalDto.id, "id错误！");
		checkNumberNotBlank(journalDto.accountBookId, "账套id错误！");
		checkNumber(journalDto.voucherId, "凭证id错误！");
		checkNumber(journalDto.voucherTemplateId, "凭证模板id错误！");
		checkDateNotBlank(journalDto.journalDate, "日期错误！");
		checkBalance(journalDto.income, "收入金额错误！");
		checkBalance(journalDto.pay, "支出金额错误！");
		checkPeriod(journalDto.period, "期间错误！");
		checkNumberNotBlank(journalDto.journalType, "类型错误！");
		checkNumberNotBlank(journalDto.incomePayType, "收支类型错误！");
		checkLength(journalDto.oppositeAccountName, 255, "对方户名太长，无法保存！");

		journalDto.summary = StringUtils.trim(journalDto.summary);
		journalDto.oppositeAccountName = StringUtils.trim(journalDto.oppositeAccountName);

		// 设置默认值
		if (StringUtils.isBlank(journalDto.income)) {
			journalDto.income = "0";
		}
		if (StringUtils.isBlank(journalDto.pay)) {
			journalDto.pay = "0";
		}
		if (StringUtils.isBlank(journalDto.status)) {
			journalDto.status = "0";
		}
		if (StringUtils.isBlank(journalDto.period)) {
			journalDto.period = cache.accountBook.currentPeriod;
		}
		if (StringUtils.isBlank(journalDto.openingBank)) {
			journalDto.openingBank = null;
		}
		else {
			journalDto.openingBank = journalDto.openingBank.trim();
		}

		if (cache.accountBook.closePeriod != null && journalDto.period.compareTo(cache.accountBook.closePeriod) <= 0) {
			logger.info("已结账的期间无法添加日记账!");
			throw new ServiceException("已结账的期间无法添加日记账!", ErrorCode.BAD_REQUEST);
		}

		Journal journal = BeanMapper.map(journalDto, Journal.class);
		if ((BigDecimal.ZERO.compareTo(journal.income) == 0) == (BigDecimal.ZERO.compareTo(journal.pay) == 0)) {
			logger.info("收入和支出金额错误！");
			throw new ServiceException("收入和支出金额错误！", ErrorCode.BAD_REQUEST);
		}
		Journal saveJournal = journalDao.save(journal);
        return saveJournal;
	}

	/**
	 * 删除日记账，批量删除日记账
	 */
	public void deleteJournal(String idStrs, HttpServletRequest request) {
		String[] idStrList = idStrs.split(",");
		Set<Long> voucherIds = new HashSet<>();
		AccountBook accountBook = null;
		for (String idStr : idStrList) {
			checkNumberNotBlank(idStr, "id错误！");
			Long id = Long.parseLong(idStr);
			Journal journal = journalDao.findOne(id);
			if (accountBook == null) {
				accountBook = accountBookDao.findOne(journal.accountBookId);
			}
			if (accountBook.closePeriod != null && journal.period.compareTo(accountBook.closePeriod) <= 0) {
				logger.info("已结账的期间无法删除日记账!");
				throw new ServiceException("已结账的期间无法删除日记账!", ErrorCode.BAD_REQUEST);
			}
			if (journal.voucherId != null) {
				voucherIds.add(journal.voucherId);
			}
			journalDao.delete(journal);
		}
		voucherService.deleteVoucherList(voucherIds, accountBook, request);
		for (Long voucherId : voucherIds) {
			List<Journal> journals = journalDao.findByVoucherId(voucherId);
			if (journals.size() > 0) {
				createVoucher(journals, null);
			}
		}
	}

	/**
	 * 删除日记账，批量删除日记账
	 */
	public void updateJournalPeriod(String idStrs, String period) {
		String[] idStrList = idStrs.split(",");
		AccountBook accountBook = null;
		for (String idStr : idStrList) {
			checkNumberNotBlank(idStr, "id错误！");
			Long id = Long.parseLong(idStr);
			Journal journal = journalDao.findOne(id);
			if (accountBook == null) {
				accountBook = accountBookDao.findOne(journal.accountBookId);
			}
			if (accountBook.closePeriod != null && journal.period.compareTo(accountBook.closePeriod) <= 0) {
				logger.info("已结账的期间无法重设入账期间!");
				throw new ServiceException("已结账的期间无法重设入账期间!", ErrorCode.BAD_REQUEST);
			}
			JournalDto journalDto = BeanMapper.map(journal, JournalDto.class);
			journalDto.id = null;
			journalDto.period = period;
			addJournal(journalDto);
		}
		deleteJournal(idStrs, null);
	}

	/**
	 * 批量修改日记账模板
	 */
	public void multiModifyJournal(BillDto billDto) {
		checkNumberNotBlank(billDto.voucherTemplateId, "模板id错误！");
		for (JournalDto journal : billDto.journalList) {
			journal.voucherTemplateId = billDto.voucherTemplateId;
			modifyJournal(journal);
		}
	}

	/**
	 * 修改日记账
	 */
	public JournalDto modifyJournal(JournalDto journalDto) {
		checkNumberNotBlank(journalDto.id, "id错误！");
		checkNumberNotBlank(journalDto.incomePayType, "收支类型错误！");
		checkNumberNotBlank(journalDto.accountBookId, "账套id错误！");
		checkDateNotBlank(journalDto.journalDate, "日期错误！");
		checkBalance(journalDto.income, "收入金额错误！");
		checkBalance(journalDto.pay, "支出金额错误！");
		checkPeriod(journalDto.period, "期间错误！");
		checkNumberNotBlank(journalDto.journalType, "类型错误！");

		journalDto.summary = StringUtils.trim(journalDto.summary);
		journalDto.oppositeAccountName = StringUtils.trim(journalDto.oppositeAccountName);

		Long id = Long.parseLong(journalDto.id);

		int incomePayType = Integer.parseInt(journalDto.incomePayType);
		Journal journal = journalDao.findOne(id);
		// 如果修改了收支类型，清空凭证模板
		if (journal.incomePayType != incomePayType) {
			journalDto.voucherTemplateId = null;
		}
		if (StringUtils.isBlank(journalDto.income)) {
			journalDto.income = "0";
		}
		if (StringUtils.isBlank(journalDto.pay)) {
			journalDto.pay = "0";
		}
		// 开户行可以为空
		if (StringUtils.isBlank(journalDto.openingBank)) {
			journalDto.openingBank = null;
		}
		else {
			journalDto.openingBank = journalDto.openingBank.trim();
		}

		Long accountBookId = Long.parseLong(journalDto.accountBookId);
		AccountBook accountBook = accountBookDao.findAccountBookById(accountBookId);
		if (accountBook.closePeriod != null && journalDto.period.compareTo(accountBook.closePeriod) <= 0) {
			logger.info("已结账的期间无法修改日记账!");
			throw new ServiceException("已结账的期间无法修改日记账!", ErrorCode.BAD_REQUEST);
		}

		Journal saveJournal = BeanMapper.map(journalDto, Journal.class);
		if ((BigDecimal.ZERO.compareTo(journal.income) == 0) == (BigDecimal.ZERO.compareTo(journal.pay) == 0)) {
			logger.info("收入和支出金额错误！");
			throw new ServiceException("收入和支出金额错误！", ErrorCode.BAD_REQUEST);
		}

		Journal result = journalDao.save(saveJournal);
		if (journal.voucherId != null) {
			// 删除对应凭证
			VoucherDto voucherDto = BeanMapper.map(voucherDao.findOne(journal.voucherId), VoucherDto.class);
			if (voucherDto == null) {
				logger.info("合并过的票据无法批量审核");
				throw new ServiceException("合并过的票据无法批量审核", ErrorCode.BAD_REQUEST);
			}
			if (StringUtils.isNotBlank(accountBook.reviewer) && voucherDto.state == 1) {
				throw new ServiceException("生成的凭证已审核，无法修改票据！", ErrorCode.BAD_REQUEST);
			}
			voucherService.deleteVoucher(voucherDto, true, null);
			// 重新生成凭证
			List<Journal> list = new ArrayList<>(journalDao.findByVoucherId(result.voucherId));
			if (list.size() > 1) {
				throw new ServiceException("生成的凭证已合并，无法修改票据！", ErrorCode.BAD_REQUEST);
			}
			createVoucher(list, voucherDto);
		}
		return BeanMapper.map(result, JournalDto.class);
	}

	/**
	 * 批量修改日记账收支类型
	 */
	public ResponseDto modifyJournalIncomePayType(BillDto billDto, HttpServletRequest request) {
		checkNumberNotBlank(billDto.incomePayType, "收支类型错误！");
		int success = 0;
		int error = 0;
		for (JournalDto journal : billDto.journalList) {
			journal.incomePayType = billDto.incomePayType;
			// 模拟前端循环调修改接口
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON);
			headers.set("token", request.getHeader("token"));
			headers.set("version", request.getHeader("version"));
			headers.set("accountBookId", request.getHeader("accountBookId"));
			HttpEntity<JournalDto> journalEntity = new HttpEntity<>(journal, headers);
			String host;
			switch (active) {
				case "prod": host = "https://fc.hongfund.com:8601"; break;
				case "qa": host = "https://fctest.hongfund.com:8501"; break;
				case "dev": host = "http://localhost:8088"; break;
				default:
					if (StringUtils.startsWith(active, "jzs")) {
						host = "https://acloud.hongfund.com";
						break;
					} else {
						throw new ServiceException("环境地址错误！", ErrorCode.BAD_REQUEST);
					}
			}

			// 创建一个SimpleClientHttpRequestFactory实例
			SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
			// 设置连接超时时间（毫秒）
			requestFactory.setConnectTimeout(60000);
			// 设置读取超时时间（毫秒）
			requestFactory.setReadTimeout(60000);

			// 将请求工厂设置到RestTemplate
			restTemplate.setRequestFactory(requestFactory);
			try {
				restTemplate.put(host + "/api/journals", journalEntity);
				success++;
			} catch (Exception e) {
				error++;
			}
		}
		return new ResponseDto(200, "批量修改成功" + success + "个，异常" + error + "个！", null);
	}

	/**
	 * 查询日记账的列表
	 */
	public JournalListDto findJournals(String period, String accountBookIdStr, String journalTypeStr, Integer direction, boolean isTemplate, String isUnMatch, String isSortById,
									   String searchKey, String incomePayType, String startDate, String endDate, BigDecimal minAmount, BigDecimal maxAmount, String summary,
									   String oppositeAccountName, Pageable pageable) {
		checkPeriod(period, "期间错误！");
		checkNumberNotBlank(accountBookIdStr, "账套id错误！");
		checkNumberNotBlank(journalTypeStr, "类型错误！");
		Long accountBookId = Long.parseLong(accountBookIdStr);
		Integer journalType = Integer.parseInt(journalTypeStr);
		JournalListDto result = new JournalListDto();
		String status;
		if (StringUtils.isNotBlank(isUnMatch) && "1".equals(isUnMatch)) {
			status = "1";
		}
		else {
			status = "%";
		}
		Sort sort;
		if (StringUtils.isNotBlank(isSortById) && "1".equals(isSortById)) {
			sort = Sort.by("id").ascending();
		}
		else {
			sort = Sort.by("journalDate").ascending()
					.and(Sort.by("id").ascending());
		}

		if (StringUtils.isBlank(incomePayType)) {
			incomePayType = null;
		}
		if (StringUtils.isBlank(startDate)) {
			startDate = null;
		}
		if (StringUtils.isBlank(endDate)) {
			endDate = null;
		}
		if (StringUtils.isBlank(summary)) {
			summary = null;
		}
		if (StringUtils.isBlank(oppositeAccountName)) {
			oppositeAccountName = null;
		}
		List<Journal> journals = journalDao.findJournals(accountBookId, period, journalType, status, incomePayType, startDate, endDate, minAmount, maxAmount, summary, oppositeAccountName, direction, sort);

		DBCache cache = new DBCache();
		cache.accountBook = accountBookDao.findAccountBookById(accountBookId);
		cache.subjects = subjectDao.findByAccountBookId(accountBookId);
		cache.dimItems = dimItemDao.findByAccountBookId(accountBookId);
		cache.dimCustomers = dimCustomerDao.findByAccountBookId(accountBookId);
		cache.dimProviders = dimProviderDao.findByAccountBookId(accountBookId);
		cache.dimDepartments = dimDepartmentDao.findByAccountBookId(accountBookId);
		cache.dimEmployees = dimEmployeeDao.findByAccountBookId(accountBookId);
		cache.dimInventories = dimInventoryDao.findByAccountBookId(accountBookId);
		cache.relations = subjectDimRelationDao.findByAccountBookId(accountBookId);
		cache.vouchers = voucherDao.findByAccountBookIdAndPeriodOrderByVoucherNoAsc(accountBookId, period);
		cache.balances = balanceDao.findByAccountBookIdAndPeriod(accountBookId, period);
		cache.assBalances = dimBalanceDao.findByAccountBookIdAndPeriod(accountBookId, period);
		cache.systemSettings = systemSettingDao.findByAccountBookId(accountBookId);

		// 查询银行存款的科目
		Subject bankBaseSubject = cache.getSubjectByLongText("银行存款");
		Subject bankSubject = bankBaseSubject;
		// 如果指定开户行，查询对应开户行的余额
		if (!isTemplate && StringUtils.isNotBlank(searchKey)) {
			bankSubject = cache.getChildLeafSubject(bankBaseSubject.no, searchKey);
		}
		BaseBalance bankDepositBalance = balanceService.findBalanceByCache(cache, bankSubject, period);
		BigDecimal balance = bankDepositBalance.initbalance;
		BigDecimal totalIncome = BigDecimal.ZERO;
		BigDecimal totalPay = BigDecimal.ZERO;
		boolean isReceipt = false;
		result.initBalance = DECIMAL_FORMAT_2.format(balance);
		List<JournalDto> journalDtoList = new ArrayList<>();

		for (Journal journal : journals) {
			JournalDto journalDto = BeanMapper.map(journal, JournalDto.class);
			Voucher voucher = cache.getVoucherByVoucherId(journal.voucherId);
			if (voucher != null) {
				journalDto.voucherNo = "记-" + voucher.voucherNo;
			}
			// 凭证号搜索或开户行筛选
			if (StringUtils.isNotBlank(searchKey) && !StringUtils.contains(journalDto.voucherNo, searchKey) && !StringUtils.equals(journalDto.openingBank, searchKey)) {
				continue;
			}

			journal.setDecimal();
			balance = balance.add(journal.income);
			balance = balance.subtract(journal.pay);
			totalIncome = totalIncome.add(journal.income);
			totalPay = totalPay.add(journal.pay);
			if("0.00".equals(journalDto.income)) {
				journalDto.income = "";
			}
			if("0.00".equals(journalDto.pay)) {
				journalDto.pay = "";
			}
			journalDto.balance = DECIMAL_FORMAT_2.format(balance);

			if (isTemplate) {
				// 设置凭证模板数据
				VoucherTemplate voucherTemplate = voucherTemplateDao.findOne(journal.voucherTemplateId);
				if (voucherTemplate != null) {
					VoucherTemplateDto voucherTemplateDto = BeanMapper.map(voucherTemplate, VoucherTemplateDto.class);
					voucherTemplateDto.records = jsonMapper.fromJson(voucherTemplate.content, jsonMapper.contructCollectionType(ArrayList.class,VoucherRecordTemplateDto.class));
					boolean isSameType = isSameType(voucherTemplateDto.records);
					for (VoucherRecordTemplateDto record : voucherTemplateDto.records) {
						record.fixSubjectNo(cache);
						Subject subject = cache.getSubjectByNo(record.subjectNo);
						if (record.subjectDimRelationId != null) {
							SubjectDimRelation relation = cache.getRelationById(record.subjectDimRelationId);
							record.showNo = subject.no+cache.getDimAccountNos(relation);
							record.showLongText = subject.longText+cache.getDimAccountNames(relation);
						}
						else {
							record.showNo = subject.no;
							record.showLongText = subject.longText;
							if (!isSameType) {
								record.dir = null;
							}
							VoucherRecordDto voucherRecord = getVoucherRecord(cache, journal, record);
							int index = voucherRecord.foresubText.indexOf(" ");
							String showRealNo = voucherRecord.foresubText.substring(0, index);
							String showRealLongText = voucherRecord.foresubText.substring(index + 1);
							if (showRealNo.length() > record.showNo.length()) {
								record.leafNo = showRealNo.substring(record.showNo.length());
							}
							if (showRealLongText.length() > record.showLongText.length()) {
								record.leafLongText = showRealLongText.substring(record.showLongText.length());
							}
						}
					}
					voucherTemplateDto.records.sort((o1, o2) -> o2.dir - o1.dir);
					journalDto.voucherTemplate = voucherTemplateDto;
				}
			}
			journalDtoList.add(journalDto);
			if (StringUtils.isNotBlank(journalDto.receiptUrl)) {
				isReceipt = true;
			}
		}
		RamPager<JournalDto> pager = new RamPager<>(journalDtoList, pageable);
		result.journalList = pager.pageResult();
		result.endBalance = DECIMAL_FORMAT_2.format(balance);
		result.totalIncome = DECIMAL_FORMAT_2.format(totalIncome);
		result.totalPay = DECIMAL_FORMAT_2.format(totalPay);
		result.isReceipt = isReceipt;
		return result;
	}

	/**
	 * 日记账生成凭证（批量生成凭证）
	 */
	public void createVoucher(List<Journal> journals, VoucherDto oldVoucher) {
        Long accountBookId = journals.get(0).accountBookId;
        DBCache cache = new DBCache();
        cache.accountBook = accountBookDao.findOne(accountBookId);
        cache.subjects = subjectDao.findByAccountBookId(accountBookId);
        cache.dimItems = dimItemDao.findByAccountBookId(accountBookId);
        cache.dimCustomers = dimCustomerDao.findByAccountBookId(accountBookId);
        cache.dimProviders = dimProviderDao.findByAccountBookId(accountBookId);
        cache.dimDepartments = dimDepartmentDao.findByAccountBookId(accountBookId);
        cache.dimEmployees = dimEmployeeDao.findByAccountBookId(accountBookId);
        cache.dimInventories = dimInventoryDao.findByAccountBookId(accountBookId);
        cache.voucherTemplates = voucherTemplateDao.findByAccountBookId(accountBookId);
		cache.relations = subjectDimRelationDao.findByAccountBookId(accountBookId);
		cache.journals = journalDao.findByAccountBookIdOrderByIdDesc(accountBookId);
		cache.systemSettings = systemSettingDao.findByAccountBookId(accountBookId);

        List<VoucherRecordDto> voucherRecords = new ArrayList<>();
		List<Long> accessoryIds = new ArrayList<>();
		for (Journal journal : journals) {
			accessoryIds.add(journal.id);
			// 凭证模板
			VoucherTemplate voucherTemplate = null;
			if (journal.voucherTemplateId != null) {
				voucherTemplate = cache.getVoucherTemplateById(journal.voucherTemplateId);
			}
			if (voucherTemplate == null) {
				// 如果是其他且指定了对方户名，使用最近一次使用过的模板
				if (StringUtils.isNotBlank(journal.oppositeAccountName)) {
					Long voucherTemplateId = cache.getLastVoucherTemplateId(journal);
					if (voucherTemplateId != null) {
						voucherTemplate = cache.getVoucherTemplateById(voucherTemplateId);
					}
				}
				if (voucherTemplate == null) {
					String showName = getShowName(journal.incomePayType);
					voucherTemplate = cache.getInitVoucherTemplateByCateAndShowName(3, showName);
				}
			}
			if (voucherTemplate == null) {
				logger.info("找不到对应的凭证模板！");
				throw new ServiceException("找不到对应的凭证模板！", ErrorCode.BAD_REQUEST);
			}
			journal.voucherTemplateId = voucherTemplate.id;
			List<VoucherRecordTemplateDto> records = jsonMapper.fromJson(voucherTemplate.content, jsonMapper.contructCollectionType(ArrayList.class,VoucherRecordTemplateDto.class));
            boolean isSameType = isSameType(records);
			for (VoucherRecordTemplateDto record : records) {
				record.fixSubjectNo(cache);
			    // 如果不是划款，删除模板分录的借贷，根据日记账发生额方向确定借贷
			    if (!isSameType) {
			        record.dir = null;
                }
				VoucherRecordDto voucherRecord = getVoucherRecord(cache, journal, record);
				voucherRecords.add(voucherRecord);
			}
		}

		// 借方分录在前，不合并
		List<VoucherRecordDto> jfVoucherRecords = voucherService.getOneSideVoucherRecord("借", voucherRecords);
		// 合并后新的分录列表
		List<VoucherRecordDto> newVoucherRecords = new ArrayList<>(voucherService.trimVoucherRecords(jfVoucherRecords, "Journal"));
		// 贷方分录在后，相同科目余额合并
		List<VoucherRecordDto> dfVoucherRecords = voucherService.getOneSideVoucherRecord("贷", voucherRecords);
		List<VoucherRecordDto> trimVoucherRecords = voucherService.trimVoucherRecords(dfVoucherRecords, "Journal");
		newVoucherRecords.addAll(trimVoucherRecords);
		BigDecimal voucherTotal = BigDecimal.ZERO;
		for (VoucherRecordDto voucherRecord : newVoucherRecords) {
			voucherRecord.id = null;
			voucherTotal = voucherTotal.add(voucherRecord.jfmoney);
		}
		// 生成凭证
		Journal journal = journals.get(0);
		String voucherNo = null;
		if (oldVoucher != null && StringUtils.isNotBlank(oldVoucher.voucherNo)) {
			voucherNo = oldVoucher.voucherNo;
		}

		VoucherDto voucherDto = new VoucherDto();
		voucherDto.category = JOURNAL;
		voucherDto.accessoryNo = accessoryIds.size();
		voucherDto.accountBookId = journal.accountBookId;
		voucherDto.jftotal = DECIMAL_FORMAT_2.format(voucherTotal);
		voucherDto.dftotal = DECIMAL_FORMAT_2.format(voucherTotal);
		voucherDto.period = journal.period;
		voucherDto.voucherDate = getLastDate(journal.period);
		voucherDto.voucherNo = voucherNo;
		if (oldVoucher != null && StringUtils.isNotBlank(oldVoucher.remark)) {
			voucherDto.remark = oldVoucher.remark;
		}
		voucherDto.writtenPerson = cache.accountBook.maker;
		voucherDto.voucherRecords = newVoucherRecords;

		VoucherDto saveVoucher;
		try {
			saveVoucher = voucherService.createVoucher(null, voucherDto);
		} catch (Exception e) {
			logger.error("生成凭证错误！具体：" + e.getMessage());
			throw new ServiceException("生成凭证错误！具体：" + e.getMessage(), ErrorCode.BAD_REQUEST);
		}
		journalDao.updateVoucherId(saveVoucher.id, accessoryIds);
	}

	/**
	 * 生成凭证
	 */
	public void createJournalVoucher(List<Long> idList, List<String> nos) {
		List<Journal> journalList = journalDao.findByIdIn(idList);
		if (journalList.isEmpty()) {
			throw new ServiceException("请选择需要生成凭证的发票", ErrorCode.BAD_REQUEST);
		}
		for (Journal journal : journalList) {
			if (journal.voucherId != null) {
				Voucher voucher = voucherDao.findOne(journal.voucherId);
				if (voucher != null) {
					throw new ServiceException("无法重复生成凭证", ErrorCode.BAD_REQUEST);
				}
			}
		}
		Long accountBookId = journalList.get(0).accountBookId;
		String period = journalList.get(0).period;

		DBCache cache = new DBCache();
		cache.accountBook = accountBookDao.findOne(accountBookId);
		cache.accountBookInfo = accountBookInfoDao.findByAccountBookId(accountBookId);
		cache.subjects = subjectDao.findByAccountBookId(accountBookId);
		cache.dimItems = dimItemDao.findByAccountBookId(accountBookId);
		cache.dimCustomers = dimCustomerDao.findByAccountBookId(accountBookId);
		cache.dimProviders = dimProviderDao.findByAccountBookId(accountBookId);
		cache.dimDepartments = dimDepartmentDao.findByAccountBookId(accountBookId);
		cache.dimEmployees = dimEmployeeDao.findByAccountBookId(accountBookId);
		cache.dimInventories = dimInventoryDao.findByAccountBookId(accountBookId);
		cache.voucherTemplates = voucherTemplateDao.findByAccountBookId(accountBookId);
		cache.relations = subjectDimRelationDao.findByAccountBookId(accountBookId);
		cache.systemSettings = systemSettingDao.findByAccountBookId(accountBookId);
		cache.cashFlowItems = cashFlowItemDao.findByAccountBookId(accountBookId);
		cache.journals = journalDao.findByAccountBookIdOrderByIdDesc(accountBookId);

		// 获取当前日期的所属期
		String currentPeriod = collectionPeriodService.getCollectionPeriod(null, cache.accountBook.area);
		// 导入
		List<VoucherRecordDto> records = new ArrayList<>();
		String maxNo = voucherDao.findMaxVoucherNo(accountBookId, period);
		for (Journal journal : journalList) {
			if (nos != null) {
				int index = journalList.indexOf(journal);
				maxNo = nos.get(index);
			}
			else {
				maxNo = getNextVoucherNo(maxNo, VOUCHER_NO_LENGTH);
			}
			// 生成凭证
			VoucherDto voucherDto = createVoucher(cache, journal, maxNo, currentPeriod, null);
			records.addAll(voucherDto.voucherRecords);
		}

		// 更新余额表数据
		List<VoucherRecordDto> copyVrs2 = BeanMapper.mapList(records, VoucherRecordDto.class);
		balanceService.changeBalance(null, cache.accountBook.id, voucherService.trimVoucherRecords(copyVrs2, null), period, true);
	}

	/**
	 * 日记账生成凭证
	 */
	public VoucherDto createVoucher(DBCache cache, Journal journal, String voucherNo, String currentPeriod, String exchangeRateStr) {
		List<VoucherRecordDto> voucherRecords = new ArrayList<>();
		BigDecimal exchangeRate = getDecimalValue(exchangeRateStr, 5);
		// 凭证模板
		VoucherTemplate voucherTemplate = null;
		if (journal.voucherTemplateId != null) {
			voucherTemplate = cache.getVoucherTemplateById(journal.voucherTemplateId);
		}
		if (voucherTemplate == null) {
			// 如果是其他且指定了对方户名，使用最近一次使用过的模板
			if (StringUtils.isNotBlank(journal.oppositeAccountName)) {
				Long voucherTemplateId = cache.getLastVoucherTemplateId(journal);
				if (voucherTemplateId != null) {
					voucherTemplate = cache.getVoucherTemplateById(voucherTemplateId);
				}
			}
			if (voucherTemplate == null) {
				String showName = getShowName(journal.incomePayType);
				voucherTemplate = cache.getInitVoucherTemplateByCateAndShowName(3, showName);
			}
		}
		if (voucherTemplate == null) {
			logger.info("找不到对应的凭证模板！");
			throw new ServiceException("找不到对应的凭证模板！", ErrorCode.BAD_REQUEST);
		}
		journal.voucherTemplateId = voucherTemplate.id;
		List<VoucherRecordTemplateDto> records = jsonMapper.fromJson(voucherTemplate.content, jsonMapper.contructCollectionType(ArrayList.class,VoucherRecordTemplateDto.class));
		boolean isSameType = isSameType(records);
		for (VoucherRecordTemplateDto record : records) {
			record.fixSubjectNo(cache);
			// 如果不是划款，删除模板分录的借贷，根据日记账发生额方向确定借贷
			if (!isSameType) {
				record.dir = null;
			}
			VoucherRecordDto voucherRecord = getVoucherRecord(cache, journal, record);
			voucherRecords.add(voucherRecord);
		}

		// 借方分录在前，不合并
		List<VoucherRecordDto> jfVoucherRecords = voucherService.getOneSideVoucherRecord("借", voucherRecords);
		// 合并后新的分录列表
		List<VoucherRecordDto> newVoucherRecords = new ArrayList<>(voucherService.trimVoucherRecords(jfVoucherRecords, "Journal"));
		// 贷方分录在后，相同科目余额合并
		List<VoucherRecordDto> dfVoucherRecords = voucherService.getOneSideVoucherRecord("贷", voucherRecords);
		List<VoucherRecordDto> trimVoucherRecords = voucherService.trimVoucherRecords(dfVoucherRecords, "Journal");
		newVoucherRecords.addAll(trimVoucherRecords);
		BigDecimal voucherTotal = BigDecimal.ZERO;
		for (VoucherRecordDto voucherRecord : newVoucherRecords) {
			voucherRecord.id = null;
			if (BigDecimal.ZERO.compareTo(exchangeRate) != 0) {
				voucherRecord.exchangeRate = exchangeRate;
				if (voucherRecord.jfmoney.compareTo(BigDecimal.ZERO) != 0) {
					voucherRecord.jfmoneyOrg = voucherRecord.jfmoney;
					voucherRecord.jfmoney = voucherRecord.jfmoneyOrg.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP);
				}
				else {
					voucherRecord.dfmoneyOrg = voucherRecord.dfmoney;
					voucherRecord.dfmoney = voucherRecord.dfmoneyOrg.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP);
				}
			}
			voucherTotal = voucherTotal.add(voucherRecord.jfmoney);
		}
		// 生成凭证
		VoucherDto voucherDto = new VoucherDto();
		voucherDto.category = JOURNAL;
		voucherDto.accessoryNo = 1;
		voucherDto.accountBookId = journal.accountBookId;
		voucherDto.jftotal = DECIMAL_FORMAT_2.format(voucherTotal);
		voucherDto.dftotal = DECIMAL_FORMAT_2.format(voucherTotal);
		voucherDto.period = journal.period;
		voucherDto.voucherDate = getLastDate(journal.period);
		voucherDto.voucherNo = voucherNo;
		voucherDto.writtenPerson = cache.accountBook.maker;
		voucherDto.voucherRecords = newVoucherRecords;
		VoucherDto saveVoucher;
		try {
			saveVoucher = voucherService.createVoucherIgnoreBalance(cache, voucherDto, currentPeriod);
		} catch (Exception e) {
			logger.error("生成凭证错误！具体：" + e.getMessage());
			throw new ServiceException("生成凭证错误！具体：" + e.getMessage(), ErrorCode.BAD_REQUEST);
		}
		journal.voucherId = saveVoucher.id;
		return saveVoucher;
	}

    /**
     * 日记账模板分录为同一类型（两个银行存款），此时为划款，使用模板分录的方向判断借贷
     */
    private boolean isSameType(List<VoucherRecordTemplateDto> records) {
        for (VoucherRecordTemplateDto record : records) {
            if (record.subjectNo == null || !record.subjectNo.startsWith(BANK_DEPOSIT)) {
                return false;
            }
        }
        return true;
    }

    private VoucherRecordDto getVoucherRecord(DBCache cache, Journal journal, VoucherRecordTemplateDto record) {
		Subject subject;
    	if (StringUtils.isNotBlank(record.subjectLongText)) {
			subject = subjectService.getSubjectByLongText(cache, record.subjectLongText, true);
			record.subjectNo = subject.no;
		}
    	else {
			subject = cache.getSubjectByNo(record.subjectNo);
		}
		VoucherRecordDto voucherRecord = new VoucherRecordDto();
		setMoney(voucherRecord, journal, record, cache.accountBook.accountingSystem);
		voucherRecord.summary = getRecordSummary(cache, record.summary, journal);
		String leafText;
		// 如果是银行存款类科目，根据开户行匹配
		if (subject.no.startsWith(BANK_DEPOSIT)) {
			leafText = journal.openingBank;
		}
		// 如果不是银行存款类科目，需要根据对方户名匹配
		else {
			leafText = StringUtils.isBlank(journal.oppositeAccountName) ? "其他" : journal.oppositeAccountName;
		}
		if (StringUtils.isNotBlank(leafText)) {
			// 如果是末级科目
			if (subject.leaf == 1) {
				// 如果有辅助核算，根据收支类型匹配辅助核算，匹配不到则新增一个辅助核算
				if (StringUtils.isNotBlank(subject.dimAccountType)) {
					SubjectDimRelation relation = subjectService.getRelation(cache, leafText, record.subjectDimRelationId, subject);
					voucherRecord.relation = BeanMapper.map(relation, SubjectDimRelationDto.class);
					voucherRecord.foresubText = subject.no + cache.getDimAccountNos(relation) + " " + subject.longText + cache.getDimAccountNames(relation);
				}
				// 如果没有辅助核算，则就用当前科目
				else {
					voucherRecord.foresubText = subject.no + " " + subject.longText;
				}
				voucherRecord.subjectId = subject.id;
			}
			// 如果是非末级科目，根据leafText找匹配下级科目，如果匹配不到则新增一个下级科目
			else {
				Subject recordSubject = subjectService.getChildSubject(cache, subject, leafText, null);
				voucherRecord.subjectId = recordSubject.id;
				voucherRecord.foresubText = recordSubject.no + " " + recordSubject.longText;
			}
		}
		else {
			Subject recordSubject = cache.getChildSubjects(subject.no).get(0);
			voucherRecord.subjectId = recordSubject.id;
			voucherRecord.foresubText = recordSubject.no + " " + recordSubject.longText;
		}
		return voucherRecord;
	}

	private String getRecordSummary(DBCache cache, String recordSummary, Journal journal) {
    	String summary = "银";
		int isDate = systemSettingService.getSystemSettingValue(cache, JOURNAL_DATE.keyword).intValue();
		if (isDate == 1) {
			if (PeriodUtils.isSameYear(journal.period, journal.journalDate)) {
				summary += journal.journalDate.substring(4, 6) + "-" + journal.journalDate.substring(6) + recordSummary;
			}
			else {
				summary += journal.journalDate.substring(0, 4) + "-" + journal.journalDate.substring(4, 6) + "-" + journal.journalDate.substring(6) + recordSummary;
			}
		}
		int isName = systemSettingService.getSystemSettingValue(cache, JOURNAL_NAME.keyword).intValue();
		if (isName == 1) {
			if (StringUtils.isNotBlank(journal.oppositeAccountName)) {
				summary += "#" + journal.oppositeAccountName;
			}
		}
		int isBalance = systemSettingService.getSystemSettingValue(cache, JOURNAL_BALANCE.keyword).intValue();
		if (isBalance == 1) {
			if (journal.income.compareTo(BigDecimal.ZERO) > 0) {
				summary += "#" + journal.income;
			} else {
				summary += "#" + journal.pay;
			}
		}
		int isSummary = systemSettingService.getSystemSettingValue(cache, JOURNAL_SUMMARY.keyword).intValue();
		if (isSummary == 1) {
			summary += "#" + journal.summary;
		}
		if (isDate == 0 && isName == 0 && isBalance == 0 && isSummary == 0) {
			summary = recordSummary;
		}
		return summary;
	}

	/**
	 * 设置借贷方向和金额正负（规律：银行帐发生额在借方，银行存款在贷方）
	 */
	private void setMoney(VoucherRecordDto voucherRecord, Journal journal, VoucherRecordTemplateDto record, String accountingSystem) {
        BigDecimal total;
        if (BigDecimal.ZERO.compareTo(journal.income) == 0) {
            total = journal.pay;
        }
        else {
            total = journal.income;
        }
        if (record.dir == null) {
            record.dir = getDir(record, journal, accountingSystem);
        }
        if (record.dir == 1) {
            voucherRecord.jfmoney = total;
        }
        else {
            voucherRecord.dfmoney = total;
        }
        // 利息费用日记账发生额在贷方时，利息费用的科目为借方红字
        if (BigDecimal.ZERO.compareTo(journal.income) != 0
                && record.subjectNo.startsWith(getInterestNo(accountingSystem))) {
            voucherRecord.jfmoney = total.negate();
        }
	}

    /**
     * 获取借贷方向
     */
    private int getDir(VoucherRecordTemplateDto record, Journal journal, String accountingSystem) {
        // 银行存款类科目与日记账发生额方向相反
        if (record.subjectNo.startsWith(BANK_DEPOSIT)) {
            if (journal.pay.compareTo(BigDecimal.ZERO) != 0) {
                return -1;
            }
            else {
                return 1;
            }
        }
        // 利息费用科目只能是借方
        else if (record.subjectNo.startsWith(getInterestNo(accountingSystem))) {
            return 1;
        }
        // 其他科目与日记账发生额方向相同
        else {
            if (journal.pay.compareTo(BigDecimal.ZERO) != 0) {
                return 1;
            }
            else {
                return -1;
            }
        }
    }

	/**
	 * 日记账生成的凭证列表
	 */
	@Transactional(readOnly = true, rollbackFor = Exception.class)
	public Page<VoucherDto> findJournalVouchers(String accountBookIdStr, String period,  String query, String incomePayType, Pageable pageable) {
		checkPeriodNotBlank(period, "期间格式错误！");
		checkNumberNotBlank(accountBookIdStr, "账套id错误！");
		Long accountBookId = Long.parseLong(accountBookIdStr);
		if (StringUtils.isBlank(incomePayType)) {
			incomePayType = null;
		}

		//凭证分录、科目等数据查询
		DBCache cache = new DBCache();
		cache.vrs = voucherRecordDao.findByAccountBookIdAndPeriod(accountBookId, period);
		cache.subjects = subjectDao.findByAccountBookId(accountBookId);
		cache.journals = journalDao.findJournals(accountBookId, period, incomePayType);
		List<Voucher> vouchers = voucherDao.findJournalVouchers(accountBookId, period);

		//处理凭证列表需要的信息
		List<VoucherDto> voucherDtos = new ArrayList<>();
		for (Voucher voucher : vouchers) {
			List<VoucherRecord> vrs = cache.getVoucherRecordsByVoucherId(voucher.id);
			List<Journal> journals = cache.getJournalsByVoucherId(voucher.id);
			if (journals.size() > 0 && isMatch(voucher, vrs, journals, query)) {
				VoucherDto voucherDto = BeanMapper.map(voucher, VoucherDto.class);
				List<VoucherRecordDto> vrds = BeanMapper.mapList(vrs, VoucherRecordDto.class);
				for(VoucherRecordDto vrd : vrds){
					Subject subject = cache.getSubjectById(vrd.subjectId);
					vrd.currSymbol = subject.currSymbol;
					vrd.measuringUnit = subject.measuringUnit;
				}
				voucherDto.voucherRecords = vrds;
				//将凭证日期转成页面需要的字符串格式
				voucherDto.voucherDateStr = FormatUtils.DATE_FORMAT_YMD_.get().format(voucherDto.voucherDate);
				voucherDto.journals = BeanMapper.mapList(journals, JournalDto.class);
				voucherDtos.add(voucherDto);
			}
		}
		RamPager<VoucherDto> pager = new RamPager<>(voucherDtos, pageable);
		return pager.pageResult();
	}

	/**
	 * 检查指定凭证、分录、票据和票据分录是否匹配搜索关键字（凭证号，摘要，科目，金额，业务类别）
	 */
	public boolean isMatch(Voucher voucher, List<VoucherRecord> records, List<Journal> journals, String query) {
		// 检查凭证和分录
		if (StringUtils.contains(voucher.voucherNo, query)) {
			return true;
		}
		for (VoucherRecord record : records) {
			if (StringUtils.contains(record.summary, query)) {
				return true;
			}
			if (StringUtils.contains(record.dfmoney.toString(), query) || StringUtils.contains(record.jfmoney.toString(), query)) {
				return true;
			}
			if (StringUtils.contains(record.foresubText, query)) {
				return true;
			}
		}

		for (Journal journal : journals) {
			if (StringUtils.contains(journal.oppositeAccountName, query)) {
				return true;
			}
			if (StringUtils.contains(journal.summary, query)) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 查询开户行的选项
	 */
	@Transactional(readOnly = true, rollbackFor = Exception.class)
	public Map<String, Object> findOpeningBanks(String accountBookIdStr) {
		checkNumberNotBlank(accountBookIdStr, "账套id错误！");
		Map<String, Object> result = new HashMap<>();
		Map<String, Boolean> openingBanks = new HashMap<>();
		Long accountBookId = Long.parseLong(accountBookIdStr);
		DBCache cache = new DBCache();
		cache.subjects = subjectDao.findByAccountBookId(accountBookId);
		cache.dimItems = dimItemDao.findByAccountBookId(accountBookId);
		cache.dimCustomers = dimCustomerDao.findByAccountBookId(accountBookId);
		cache.dimProviders = dimProviderDao.findByAccountBookId(accountBookId);
		cache.dimDepartments = dimDepartmentDao.findByAccountBookId(accountBookId);
		cache.dimEmployees = dimEmployeeDao.findByAccountBookId(accountBookId);
		cache.dimInventories = dimInventoryDao.findByAccountBookId(accountBookId);
		Subject oppositeSubject = cache.getSubjectByNo(BANK_DEPOSIT);
		boolean isCurr = false;
		if (oppositeSubject != null) {
			isCurr = StringUtils.isNotBlank(oppositeSubject.currSymbol);
			// 如果是末级科目
			if (oppositeSubject.leaf == 1) {
				// 如果是辅助核算科目，返回辅助核算名称的集合
				if (oppositeSubject.dimAccountType != null) {
					String dimAccountType = StringUtils.split(oppositeSubject.dimAccountType, ",")[0];
					List<DimAccountDto> dimAccounts = cache.getDimAccountByAssTypeName(dimAccountType);

					for (DimAccountDto dimAccountDto : dimAccounts) {
						openingBanks.put(dimAccountDto.name, isCurr);
					}
				}
			}
			// 如果有下级科目，返回下级科目名称的集合
			else {
				List<Subject> childSubjects = cache.getChildSubjects(oppositeSubject.no);
				for (Subject childSubject : childSubjects) {
					if (childSubject.status == 0) {
						openingBanks.put(childSubject.text, StringUtils.isNotBlank(childSubject.currSymbol));
					}
				}
			}
		}
		result.put("openingBanks", openingBanks);
		result.put("isCurr", isCurr);
		return result;
	}

	/**
	 * 查询对方户名的选项
	 */
	@Transactional(readOnly = true, rollbackFor = Exception.class)
	public List<String> findOppositeAccountNames(String accountBookIdStr, String incomePayTypeStr) {
		checkNumberNotBlank(accountBookIdStr, "账套id错误！");
		checkNumberNotBlank(incomePayTypeStr, "收支类型错误！");
		Long accountBookId = Long.parseLong(accountBookIdStr);
		DBCache cache = new DBCache();
		Integer incomePayType = Integer.parseInt(incomePayTypeStr);
		cache.subjects = subjectDao.findByAccountBookId(accountBookId);
		cache.dimItems = dimItemDao.findByAccountBookId(accountBookId);
		cache.dimCustomers = dimCustomerDao.findByAccountBookId(accountBookId);
		cache.dimProviders = dimProviderDao.findByAccountBookId(accountBookId);
		cache.dimDepartments = dimDepartmentDao.findByAccountBookId(accountBookId);
		cache.dimEmployees = dimEmployeeDao.findByAccountBookId(accountBookId);
		cache.dimInventories = dimInventoryDao.findByAccountBookId(accountBookId);

		List<String> result = new ArrayList<>();
		// 找对方科目
		String oppositeSubjectLongText = getLongText(incomePayType);
		Subject oppositeSubject = cache.getSubjectByLongText(oppositeSubjectLongText);
		if (oppositeSubject != null) {
			// 如果是末级科目
			if (oppositeSubject.leaf == 1) {
				// 如果是辅助核算科目，返回辅助核算名称的集合
				if (oppositeSubject.dimAccountType != null) {
					String dimAccountType = StringUtils.split(oppositeSubject.dimAccountType, ",")[0];
					List<DimAccountDto> dimAccounts = cache.getDimAccountByAssTypeName(dimAccountType);
					for (DimAccountDto dimAccountDto : dimAccounts) {
						result.add(dimAccountDto.name);
					}
				}
			}
			// 如果有下级科目，返回下级科目名称的集合
			else {
				List<Subject> childSubjects = cache.getChildSubjects(oppositeSubject.no);
				for (Subject childSubject : childSubjects) {
					result.add(childSubject.text);
				}
			}
		}
		return result;
	}

	private String getShowName(Integer index) {
		for (JournalType type : JournalType.values ()) {
			if (index == type.code) {
				return type.showName;
			}
		}
		logger.error("业务类型错误！");
		throw new ServiceException("业务类型错误！", ErrorCode.BAD_REQUEST);
	}

	/**
	 * 根据业务类型获取对方科目
	 */
	private String getLongText(Integer index) {
		for (JournalType type : JournalType.values()) {
			if (index == type.code) {
				return type.longText;
			}
		}
		logger.error("业务类型错误！");
		throw new ServiceException("业务类型错误！", ErrorCode.BAD_REQUEST);
	}

    /**
     * 导入日记账
     */
    public ResponseDto importJournal(MultipartHttpServletRequest request) {
        String accountBookIdStr = request.getParameter("accountBookId");
        Long accountBookId = Long.parseLong(accountBookIdStr);
        String endPeriod = request.getParameter("endPeriod");
        String openingBank = request.getParameter("openingBank");
        String exchangeRate = request.getParameter("exchangeRate");
		if (StringUtils.isNotBlank(openingBank) && "银行存款".equals(openingBank)) {
			openingBank = null;
		}
        Workbook wb = fileHistoryService.saveFileHistory(request);

		// 获得第一个表单
		Sheet sheet = wb.getSheetAt(0);

		// 日期列
        Cell journalDate = getByValue(sheet, HEADER_DATE);
        if (journalDate == null) {
            logger.info("无法匹配日期的列");
            throw new ServiceException("无法匹配日期的列", ErrorCode.BAD_REQUEST);
        }
		int journalDateColumn = journalDate.getColumnIndex();
        Row headRow = journalDate.getRow();
		Row headRow2 = sheet.getRow(headRow.getRowNum()+1);
        // 对方户名列
        int oppositeAccountNameColumn = PoiUtils.getColumnNumByValue(headRow, HEADER_OPPOSITE_ACCOUNT_NAME);
        // 付款人名称列
        int payerColumn = PoiUtils.getColumnNumByValue(headRow, HEADER_PAYER);
        // 收款人名称列
		int payeeColumn = PoiUtils.getColumnNumByValue(headRow, HEADER_PAYEE);
        // 收入列
        int incomeColumn = PoiUtils.getColumnNumByValue(headRow, HEADER_INCOME);
        if (incomeColumn == -1) {
			incomeColumn = PoiUtils.getColumnNumByValue(headRow2, HEADER_INCOME);
		}
        // 支出
        int payColumn = PoiUtils.getColumnNumByValue(headRow, HEADER_PAY);
        if (payColumn == -1) {
			payColumn = PoiUtils.getColumnNumByValue(headRow2, HEADER_PAY);
		}
		// 发生额
		int balanceColumn = PoiUtils.getColumnNumByValue(headRow, HEADER_BALANCE);
		// 借贷标志
		int directionColumn = PoiUtils.getColumnNumByValue(headRow, HEADER_DIRECTION);
        // 摘要
		int summaryColumn = PoiUtils.getColumnNumByValue(headRow, HEADER_SUMMARY);
        // 开户行
		int openingBankColumn = PoiUtils.getColumnNumByValue(headRow, HEADER_OPENING_BANK);
		// 备注
        int remarkColumn = PoiUtils.getColumnNumByValue(headRow, HEADER_REMARK);
		// 收支类型
		int incomePayTypeColumn = PoiUtils.getColumnNumByValue(headRow, HEADER_INCOME_PAY_TYPE);
		// 数据的起始行是表头的下一行（表头日期下方第一个有数据的行）
		Cell startCell = getDownData(sheet, journalDate.getRowIndex(), journalDate.getColumnIndex());
		if (startCell == null) {
			logger.info("没有数据行");
			throw new ServiceException("没有数据行", ErrorCode.BAD_REQUEST);
		}
		int startRow = startCell.getRowIndex();
		// 数据的结束行是最后一行（有合计行的去除合计行）
		int endRow = sheet.getLastRowNum();
		DBCache cache = new DBCache();
		cache.accountBook = accountBookDao.findOne(accountBookId);
		if (cache.accountBook.closePeriod != null && endPeriod.compareTo(cache.accountBook.closePeriod) <= 0) {
			logger.info("已结账的期间无法导入日记账!");
			throw new ServiceException("已结账的期间无法导入日记账!", ErrorCode.BAD_REQUEST);
		}
		cache.subjects = subjectDao.findByAccountBookId(accountBookId);
		cache.dimItems = dimItemDao.findByAccountBookId(accountBookId);
		cache.dimCustomers = dimCustomerDao.findByAccountBookId(accountBookId);
		cache.dimProviders = dimProviderDao.findByAccountBookId(accountBookId);
		cache.dimDepartments = dimDepartmentDao.findByAccountBookId(accountBookId);
		cache.dimEmployees = dimEmployeeDao.findByAccountBookId(accountBookId);
		cache.dimInventories = dimInventoryDao.findByAccountBookId(accountBookId);
		cache.voucherTemplates = voucherTemplateDao.findByAccountBookId(accountBookId);
		cache.relations = subjectDimRelationDao.findByAccountBookId(accountBookId);
		cache.journals = journalDao.findByAccountBookIdOrderByIdDesc(accountBookId);
		cache.cashFlowItems = cashFlowItemDao.findByAccountBookId(accountBookId);
		cache.vrs = voucherRecordDao.findByAccountBookIdOrderByIdDesc(accountBookId);
		cache.systemSettings = systemSettingDao.findByAccountBookId(accountBookId);

		List<JournalDto> result = new ArrayList<>();
		for (int i = startRow ; i <= endRow ; i++) {
			Row row = sheet.getRow(i);
			if (isNotBlank(row)) {
				JournalDto journalBook = new JournalDto();
				journalBook.exchangeRate = exchangeRate;

				// 日期
				// 判断单元格的类型是否则NUMERIC类型
				if(row.getCell(journalDateColumn) != null && row.getCell(journalDateColumn).getCellType() == CellType.NUMERIC){
					// Excel Date类型处理
					if (isCellDateFormatted(row.getCell(journalDateColumn))) {
						Date date = getJavaDate(row.getCell(journalDateColumn).getNumericCellValue());
						SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
						journalBook.journalDate = getDateStr(sdf.format(date));
					} else {
						row.getCell(journalDateColumn).setCellType(CellType.STRING);
						journalBook.journalDate = getDateStr(getStringData(row.getCell(journalDateColumn)));
					}
				}else{
					journalBook.journalDate = getDateStr(getStringData(row.getCell(journalDateColumn)));
				}

				// 日期格式错误的跳过处理
				if (journalBook.journalDate == null) {
					continue;
				}

				// 摘要
				if (summaryColumn != -1) {
					journalBook.summary = getStringData(row.getCell(summaryColumn));
				}

				// 收入支出
				// 有借贷两列的情况
				if (incomeColumn != -1 && payColumn != -1 && incomeColumn != payColumn) {
					journalBook.income = String.valueOf(getBalanceData(row.getCell(incomeColumn),wb));
					journalBook.pay = String.valueOf(getBalanceData(row.getCell(payColumn),wb));
				}
				// 只有发生额的情况
				else if (balanceColumn != -1) {
					BigDecimal balance = getBalanceData(row.getCell(balanceColumn),wb);
					if (directionColumn != -1) {
						String direction = getStringData(row.getCell(directionColumn));
						if (Arrays.asList(HEADER_DIRECTION_JF).contains(direction)) {
							journalBook.income = String.valueOf(balance);
						}
						else {
							journalBook.pay = String.valueOf(balance);
						}
					}
					else {
						if (balance.compareTo(BigDecimal.ZERO) > 0) {
							journalBook.income = String.valueOf(balance.abs());
						}
						else {
							journalBook.pay = String.valueOf(balance.abs());
						}
					}
				}
				else {
					// 无法匹配金额则忽略
					continue;
				}

				// 金额全为0或全不为0的是错误的，忽略
				if (StringUtils.isBlank(journalBook.income) || new BigDecimal(journalBook.income).compareTo(BigDecimal.ZERO) == 0) {
					if (StringUtils.isBlank(journalBook.pay) || new BigDecimal(journalBook.pay).compareTo(BigDecimal.ZERO) == 0) {
						continue;
					}
				}
				else {
					if (StringUtils.isNotBlank(journalBook.pay) && new BigDecimal(journalBook.pay).compareTo(BigDecimal.ZERO) != 0) {
						continue;
					}
				}

				// 备注
				String remark = null;
				if (remarkColumn != -1) {
				    remark = getStringData(row.getCell(remarkColumn));
                }
                //对方户名
                if (oppositeAccountNameColumn != -1) {
                    journalBook.oppositeAccountName = getStringData(row.getCell(oppositeAccountNameColumn));
                    if (journalBook.oppositeAccountName != null) {
                        if (journalBook.oppositeAccountName.length() > MAX_LENGTH) {
                            journalBook.oppositeAccountName = journalBook.oppositeAccountName.substring(0, MAX_LENGTH);
                        }
                    }
                }
				// 收支类型
				String typeName = null;
				if (incomePayTypeColumn != -1) {
					typeName = getStringData(row.getCell(incomePayTypeColumn));
				}
				// 如果有收款和付款账户信息，根据方向选择，收入选付款账户信息，支出选收款账户信息
				if (payerColumn != -1 && payeeColumn != -1) {
					if (StringUtils.isNotBlank(journalBook.income) && new BigDecimal(journalBook.income).compareTo(BigDecimal.ZERO) != 0) {
						journalBook.oppositeAccountName = getStringData(row.getCell(payerColumn));
					}
					else {
						journalBook.oppositeAccountName = getStringData(row.getCell(payeeColumn));
					}
				}

                // 收支类型（不能匹配的用方向选择，收入选收款，支出选付款）
                journalBook.incomePayType = getIncomePayType(cache, journalBook, remark, typeName);

				// 如果匹配到税费相关，设置状态为未成功审核
				if (journalBook.incomePayType.equals(TYPE_6.codeValue)) {
					journalBook.status = "1";
				}

				//开户行
				if (StringUtils.isNotBlank(openingBank)) {
					journalBook.openingBank = openingBank;
				}
				else if (openingBankColumn != -1) {
					journalBook.openingBank = getStringData(row.getCell(openingBankColumn));
				}
				//期间
				journalBook.period = endPeriod;
				//帐套id
				journalBook.accountBookId = String.valueOf(accountBookId);
				//类型
				journalBook.journalType = "0";
				result.add(journalBook);
			}
		}
		if (!result.isEmpty() && StringUtils.compare(result.get(0).journalDate, result.get(result.size()-1).journalDate) > 0) {
			Collections.reverse(result);
		}
		//导入
		int countJournal = importJournal(cache, result);
		String message = "本次成功导入" + countJournal + "条日记账。";
		if (countJournal == 0) {
			message += "请检查Excel格式是否符合要求！";
		}
		return new ResponseDto(200, "导入成功！", message);
	}

	public ResponseDto getBankData(String accountBookIdStr, String period, Integer type, Integer collectType, HttpServletRequest request) {
		if(collectType == 0){
			switch (type) {
				case ICBC : return getIcbcData(accountBookIdStr, period, collectType, request);
				case PABC : return getPabcData(accountBookIdStr, period, collectType, request);
				case BCM : return getBcmData(accountBookIdStr, period, collectType, request);
				default: return bankTaskService.saveBankTask(accountBookIdStr, period, request, type, collectType);
			}
		}else{
			return bankTaskService.saveBankTask(accountBookIdStr, period, request, type, collectType);
		}
	}

	/**
	 * （工行）提取对账单和流水
	 */
	public ResponseDto getIcbcData(String accountBookIdStr, String period, Integer collectType, HttpServletRequest request) {
		long accountBookId = Long.parseLong(accountBookIdStr);
		BankAccount bankAccount = bankAccountDao.findByAccountBookIdAndTypeAndCollectType(accountBookId, ICBC, collectType);
		if (bankAccount == null || StringUtils.isBlank(bankAccount.account)) {
			return new ResponseDto(1, "银行账号设置不完整，无法查询是否签约！", null);
		}
		String baseDateStr = StringUtils.substring(period, 0, 4) + "-" + StringUtils.substring(period, 4);
		String startDate = baseDateStr + "-01";
		String endDateStr = PeriodUtils.getLastDayStr(period);
		String endDate = baseDateStr + "-" + endDateStr;
		List<MyBankAccountDetailDto> myBankAccountDetailDtoList = IcbcUtils.getJournals(startDate, endDate, bankAccount.account);
		if (myBankAccountDetailDtoList.isEmpty()) {
			return new ResponseDto(1, "未获取到流水数据！", null);
		}

		List<BankAccountDetailIcbc> bankAccountDetailIcbcList = BeanMapper.mapList(myBankAccountDetailDtoList, BankAccountDetailIcbc.class);
		int countJournal = 0;
		for (BankAccountDetailIcbc bankAccountDetailIcbc : bankAccountDetailIcbcList) {
			bankAccountDetailIcbc.accountBookId = accountBookId;
			bankAccountDetailIcbc.period = period;
			countJournal++;
		}
		bankAccountDetailIcbcDao.deleteByAccountBookIdAndPeriod(accountBookId, period);

		int countReceipt = 0;
		List<Journal> journalList = journalDao.findByAccountBookIdAndPeriod(accountBookId, period);
		List<BankAccountReceiptIcbc> receiptList = bankAccountReceiptIcbcDao.findByAccountAndReceiptDateLike(bankAccount.account, period+"%");
		List<Journal> journalSaveList = new ArrayList<>();
		for (BankAccountReceiptIcbc receipt : receiptList) {
			BankAccountDetailIcbc bankAccountDetailIcbc = getBySerialno(bankAccountDetailIcbcList, receipt.receiptNo);
			if (bankAccountDetailIcbc != null && StringUtils.isBlank(bankAccountDetailIcbc.receiptUrl)) {
				bankAccountDetailIcbc.receiptUrl = receipt.receiptUrl;
				countReceipt++;
			}
			Journal journal = getByReceiptNo(journalList, receipt.receiptNo);
			if (journal != null) {
				journal.receiptUrl = receipt.receiptUrl;
				journalSaveList.add(journal);
			}
		}
		journalDao.saveAll(journalSaveList);
		bankAccountDetailIcbcDao.saveAll(bankAccountDetailIcbcList);

		//添加提取银行对账单的操作记录
		Account tokenAccount = accountService.getLoginUser(request.getHeader("token"));
		AccountBook accountBook = accountBookDao.findAccountBookById(accountBookId);
		String ip = IpUtils.getIp(request);
		newOperationLogService.saveNewOperationLog(NEW_OPERATE_TYPE_20.typeCode, tokenAccount.loginId, tokenAccount.id, tokenAccount.no, tokenAccount.name, "提取" + accountBook.bookName + PeriodUtils.getYear(period) + "年第" + period.substring(4) + "期工商银行对账单", ip);

		String message = "本次成功提取" + countJournal + "条流水数据，"+countReceipt+"条回单数据。";
		return new ResponseDto(200, "提取成功", message);
	}

	/**
	 * （平安银行）提取对账单和流水
	 */
	public ResponseDto getPabcData(String accountBookIdStr, String period, Integer collectType, HttpServletRequest request) {
		long accountBookId = Long.parseLong(accountBookIdStr);
		BankAccount bankAccount = bankAccountDao.findByAccountBookIdAndTypeAndCollectType(accountBookId, PABC, collectType);
		if (bankAccount == null || StringUtils.isBlank(bankAccount.account)) {
			return new ResponseDto(1, "银行账号设置不完整，无法查询是否签约！", null);
		}
		String startDate = period + "01";
		String endDate = period + PeriodUtils.getLastDayStr(period);
		List<PabcJournalDetailDto> pabcJournalDetailDtoList = PabcUtils.getJournals(startDate, endDate, bankAccount.account, accountBookId);
		if (pabcJournalDetailDtoList.isEmpty()) {
			return new ResponseDto(1, "未获取到流水数据！", null);
		}

		List<BankAccountDetailPabc> bankAccountDetailPabcList = BeanMapper.mapList(pabcJournalDetailDtoList, BankAccountDetailPabc.class);
		int countJournal = 0;
		for (BankAccountDetailPabc bankAccountDetailPabc : bankAccountDetailPabcList) {
			bankAccountDetailPabc.accountBookId = accountBookId;
			bankAccountDetailPabc.period = period;
			countJournal++;
		}
		bankAccountDetailPabcDao.deleteByAccountBookIdAndPeriod(accountBookId, period);

		int countReceipt = 0;
		List<Journal> journalList = journalDao.findByAccountBookIdAndPeriod(accountBookId, period);
		List<BankAccountReceiptPabc> receiptList = bankAccountReceiptPabcDao.findByAcctDateLike(period);
		List<Journal> journalSaveList = new ArrayList<>();
		for (BankAccountReceiptPabc receipt : receiptList) {
			BankAccountDetailPabc bankAccountDetailPabc = getByHostTraceAndAbstractStr(bankAccountDetailPabcList, receipt.HostTrace, receipt.AbstractStr);
			if (bankAccountDetailPabc != null && StringUtils.isBlank(bankAccountDetailPabc.receiptUrl)) {
				bankAccountDetailPabc.receiptUrl = receipt.receiptUrl;
				countReceipt++;
			}
			Journal journal = getByReceiptNo(journalList, receipt.HostTrace+"#"+receipt.AbstractStr);
			if (journal != null) {
				journal.receiptUrl = receipt.receiptUrl;
				journalSaveList.add(journal);
			}
		}
		journalDao.saveAll(journalSaveList);
		bankAccountDetailPabcDao.saveAll(bankAccountDetailPabcList);

		//添加提取银行对账单的操作记录
		Account tokenAccount = accountService.getLoginUser(request.getHeader("token"));
		AccountBook accountBook = accountBookDao.findAccountBookById(accountBookId);
		String ip = IpUtils.getIp(request);
		newOperationLogService.saveNewOperationLog(NEW_OPERATE_TYPE_20.typeCode, tokenAccount.loginId, tokenAccount.id, tokenAccount.no, tokenAccount.name, "提取" + accountBook.bookName + PeriodUtils.getYear(period) + "年第" + period.substring(4) + "期平安银行对账单", ip);

		String message = "本次成功提取" + countJournal + "条流水数据，"+countReceipt+"条回单数据。";
		return new ResponseDto(200, "提取成功", message);
	}

	/**
	 * （交通银行）提取对账单和流水
	 */
	public ResponseDto getBcmData(String accountBookIdStr, String period, Integer collectType, HttpServletRequest request) {
		long accountBookId = Long.parseLong(accountBookIdStr);
		BankAccount bankAccount = bankAccountDao.findByAccountBookIdAndTypeAndCollectType(accountBookId, BCM, collectType);
		if (bankAccount == null || StringUtils.isBlank(bankAccount.account)) {
			return new ResponseDto(1, "银行账号设置不完整，无法查询是否签约！", null);
		}
		String startDate = period + "01";
		String endDate = period + PeriodUtils.getLastDayStr(period);
		String today = FormatUtils.DATE_FORMAT_YMD.get().format(new Date());
		if (StringUtils.compare(endDate, today) > 0) {
			endDate = today;
		}
		List<B2BYQFYQFU1016ResponseV1.PptmBody.TxnDetl> txnDetlList = BcmUtils.getJournals(bankAccount.account, startDate, endDate);
		if (txnDetlList.isEmpty()) {
			return new ResponseDto(1, "未获取到流水数据！", null);
		}

		List<BankAccountDetailBcm> bankAccountDetailBcmList = BeanMapper.mapList(txnDetlList, BankAccountDetailBcm.class);
		int countJournal = 0;
		for (BankAccountDetailBcm bankAccountDetailBcm : bankAccountDetailBcmList) {
			bankAccountDetailBcm.accountBookId = accountBookId;
			bankAccountDetailBcm.period = period;
			countJournal++;
		}
		bankAccountDetailBcmDao.deleteByAccountBookIdAndPeriod(accountBookId, period);

		int countReceipt = 0;
		List<Journal> journalList = journalDao.findByAccountBookIdAndPeriod(accountBookId, period);
		List<BankAccountReceiptBcm> receiptList = bankAccountReceiptBcmDao.findByAccountAndReceiptDateLike(bankAccount.account, period+"%");
		List<Journal> journalSaveList = new ArrayList<>();
		for (BankAccountReceiptBcm receipt : receiptList) {
			BankAccountDetailBcm bankAccountDetailBcm = getByJrnlNo(bankAccountDetailBcmList, receipt.jrnlNo, receipt.jrnlSeqNo);
			if (bankAccountDetailBcm != null && StringUtils.isBlank(bankAccountDetailBcm.receiptUrl)) {
				bankAccountDetailBcm.receiptUrl = receipt.receiptUrl;
				countReceipt++;
			}
			Journal journal = getByReceiptNo(journalList, receipt.jrnlNo+"#"+receipt.jrnlSeqNo);
			if (journal != null) {
				journal.receiptUrl = receipt.receiptUrl;
				journalSaveList.add(journal);
			}
		}
		journalDao.saveAll(journalSaveList);
		bankAccountDetailBcmDao.saveAll(bankAccountDetailBcmList);

		//添加提取银行对账单的操作记录
		Account tokenAccount = accountService.getLoginUser(request.getHeader("token"));
		AccountBook accountBook = accountBookDao.findAccountBookById(accountBookId);
		String ip = IpUtils.getIp(request);
		newOperationLogService.saveNewOperationLog(NEW_OPERATE_TYPE_20.typeCode, tokenAccount.loginId, tokenAccount.id, tokenAccount.no, tokenAccount.name, "提取" + accountBook.bookName + PeriodUtils.getYear(period) + "年第" + period.substring(4) + "期交通银行对账单", ip);

		String message = "本次成功提取" + countJournal + "条流水数据，"+countReceipt+"条回单数据。";
		return new ResponseDto(200, "提取成功", message);
	}

	/**
	 * 定时获取平安银行回单
	 */
	@Transactional
	public ResponseDto schedulePabcReceipt(String queryDate) {
		if (StringUtils.isBlank(queryDate)) {
			Date date = DateUtils.addDays(new Date(), -2);
			queryDate = FormatUtils.DATE_FORMAT_YMD.get().format(date);
		}
		List<String> fileNameList = new ArrayList<>();
		List<BankAccount> bankAccountList = bankAccountDao.findByType(PABC);
		// 从银行下载
		for (BankAccount bankAccount : bankAccountList) {
			PabcDownloadDto pabcDownloadDto = PabcUtils.getReceipt(queryDate, bankAccount.account, bankAccount.accountBookId);
			if (pabcDownloadDto != null) {
				boolean isDownload = PabcUtils.download(pabcDownloadDto.FileName, pabcDownloadDto.RandomPwd, bankAccount.account, bankAccount.accountBookId);
				if (isDownload) {
					fileNameList.add(pabcDownloadDto.FileName);
				}
			}
		}
		// 延迟1分钟
		try {
			Thread.sleep(60000);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		// 处理本地数据
		ResponseDto responseDto = FtpUtils.fetchByFileName(fileNameList);
		List<BankAccountReceiptPabc> saveList = new ArrayList<>();
		if (responseDto.isSuccess()) {
			Map<String, String> fileNameMap = (Map<String, String>) responseDto.data;
			for (Map.Entry<String, String> temp : fileNameMap.entrySet()) {
				String fileName = temp.getKey();
				BankAccountReceiptPabc bankAccountReceiptPabc = new BankAccountReceiptPabc();
				bankAccountReceiptPabc.parseFileName(fileName);
				String objectName = temp.getValue();
				bankAccountReceiptPabc.receiptUrl = BASE_FILE_URL + objectName;
				saveList.add(bankAccountReceiptPabc);
			}
		}
		bankAccountReceiptPabcDao.saveAll(saveList);
		return new ResponseDto(200, "获取平安银行回单成功，保存"+saveList.size()+"条数据", null);
	}

	/**
	 * 定时获取平安银行回单
	 */
	@Transactional
	public ResponseDto repairPabcReceipt() {
		List<BankAccount> bankAccountList = bankAccountDao.findByType(PABC);
		Set<String> accountSet = new HashSet<>();
		for (BankAccount bankAccount : bankAccountList) {
			accountSet.add(bankAccount.account);
		}
		Set<String> noSet = new HashSet<>();
		List<BankAccountReceiptPabc> list = bankAccountReceiptPabcDao.findAll();
		for (BankAccountReceiptPabc data : list) {
			noSet.add(data.account + "#" + data.HostTrace + "#" + data.AbstractStr);
		}
		// 处理本地数据
		ResponseDto responseDto = FtpUtils.repairPabc(noSet, accountSet);
		List<BankAccountReceiptPabc> saveList = new ArrayList<>();
		if (responseDto.isSuccess()) {
			Map<String, String> fileNameMap = (Map<String, String>) responseDto.data;
			for (Map.Entry<String, String> temp : fileNameMap.entrySet()) {
				String fileName = temp.getKey();
				BankAccountReceiptPabc bankAccountReceiptPabc = new BankAccountReceiptPabc();
				bankAccountReceiptPabc.parseFileName(fileName);
				String objectName = temp.getValue();
				bankAccountReceiptPabc.receiptUrl = BASE_FILE_URL + objectName;
				saveList.add(bankAccountReceiptPabc);
			}
		}
		bankAccountReceiptPabcDao.saveAll(saveList);
		return new ResponseDto(200, "获取平安银行回单成功，保存"+saveList.size()+"条数据", null);
	}

	/**
	 * 定时获取工商银行回单
	 */
	@Transactional
	public ResponseDto scheduleIcbcReceipt() {
		Date nearDate = DateUtils.addDays(new Date(), -3);
		Set<String> zipFileNameSet = bankAccountReceiptIcbcDao.findZipFileNameSet(nearDate);
		Set<String> pdfFileNameSet = bankAccountReceiptIcbcDao.findPdfFileNameSet(nearDate);
		ResponseDto fetchResult = FtpUtils.fetchByOrderid(zipFileNameSet, pdfFileNameSet);
		if (fetchResult.isSuccess()) {
			List<BankAccountReceiptIcbc> saveList = (List<BankAccountReceiptIcbc>) fetchResult.data;
			bankAccountReceiptIcbcDao.saveAll(saveList);
			return new ResponseDto(200, "获取工商银行回单成功，保存"+saveList.size()+"条数据", null);
		}
		return new ResponseDto(1, "获取工商银行回单失败", fetchResult.message);
	}

	/**
	 * 定时获取交通银行回单
	 */
	@Transactional
	public ResponseDto scheduleBcmReceipt(String queryDate) {
		if (StringUtils.isBlank(queryDate)) {
			Date date = DateUtils.addDays(new Date(), -2);
			queryDate = FormatUtils.DATE_FORMAT_YMD.get().format(date);
		}
		List<BankAccountReceiptBcm> saveList = BcmUtils.sftpDownload(queryDate);
		bankAccountReceiptBcmDao.saveAll(saveList);
		return new ResponseDto(200, "获取交通银行回单成功，保存"+saveList.size()+"条数据", null);
	}

	@SuppressWarnings("unchecked")
	private void addToOrderList(Long accountBookId, String period, ResponseDto responseDto) {
		if (responseDto.isSuccess()) {
			Map<String, String> result = (Map<String, String>) responseDto.data;
			String orderid = result.get("orderid");
			String fseqno = result.get("fseqno");
			BankAccountDetailIcbc orderData = new BankAccountDetailIcbc();
			Date date = new Date();
			orderData.gmtCreate = date;
			orderData.gmtModified = date;
			orderData.orderid = orderid;
			orderData.accountBookId = accountBookId;
			orderData.period = period;
			orderList.put(fseqno, orderData);
		}
	}

	/**
	 * （银企互联）导入日记账
	 */
	public ResponseDto importBankJournal(String period, String accountBookIdStr, String openingBank, String exchangeRate, Integer type, Integer collectType) {
		Long accountBookId = Long.parseLong(accountBookIdStr);

		DBCache cache = new DBCache();
		cache.accountBook = accountBookDao.findOne(accountBookId);
		if (cache.accountBook.closePeriod != null && period.compareTo(cache.accountBook.closePeriod) <= 0) {
			logger.info("已结账的期间无法导入日记账!");
			throw new ServiceException("已结账的期间无法导入日记账!", ErrorCode.BAD_REQUEST);
		}
		cache.subjects = subjectDao.findByAccountBookId(accountBookId);
		cache.dimItems = dimItemDao.findByAccountBookId(accountBookId);
		cache.dimCustomers = dimCustomerDao.findByAccountBookId(accountBookId);
		cache.dimProviders = dimProviderDao.findByAccountBookId(accountBookId);
		cache.dimDepartments = dimDepartmentDao.findByAccountBookId(accountBookId);
		cache.dimEmployees = dimEmployeeDao.findByAccountBookId(accountBookId);
		cache.dimInventories = dimInventoryDao.findByAccountBookId(accountBookId);
		cache.voucherTemplates = voucherTemplateDao.findByAccountBookId(accountBookId);
		cache.relations = subjectDimRelationDao.findByAccountBookId(accountBookId);
		cache.journals = journalDao.findByAccountBookIdOrderByIdDesc(accountBookId);
		cache.cashFlowItems = cashFlowItemDao.findByAccountBookId(accountBookId);
		cache.vrs = voucherRecordDao.findByAccountBookIdOrderByIdDesc(accountBookId);
		cache.systemSettings = systemSettingDao.findByAccountBookId(accountBookId);
		//0银企直联，1银企密联
		if(collectType == 0){
			switch (type) {
				case ICBC : return importIcbcJournal(cache, period, accountBookIdStr, openingBank, exchangeRate);
				case PABC : return importPabcJournal(cache, period, accountBookIdStr, openingBank, exchangeRate);
				case BCM : return importBcmJournal(cache, period, accountBookIdStr, openingBank, exchangeRate);
				default:
					return importYqhlJournal(accountBookIdStr, period, type, openingBank, exchangeRate);
			}
		}else{
			return importYqhlJournal(accountBookIdStr, period, type, openingBank, exchangeRate);
		}
	}


	/**
	 * 工行接口导入
	 */
	public ResponseDto importIcbcJournal(DBCache cache, String period, String accountBookIdStr, String openingBank, String exchangeRate) {
		List<BankAccountDetailIcbc> dataList = bankAccountDetailIcbcDao.findByAccountBookIdAndPeriod(Long.parseLong(accountBookIdStr), period);
		List<JournalDto> journalDtoList = new ArrayList<>();
		for (BankAccountDetailIcbc data : dataList) {
			JournalDto journalDto = new JournalDto();
			journalDto.accountBookId = accountBookIdStr;
			journalDto.period = period;
			journalDto.journalDate = StringUtils.removeAll(data.busiDate, "-");
			journalDto.oppositeAccountName = data.reciPnam;
			journalDto.summary = data.summary;
			journalDto.receiptNo = data.serialno;
			journalDto.receiptUrl = data.receiptUrl;
			BigDecimal amount = getDecimalValue(data.amount);
			amount = amount.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
			if (StringUtils.equals(data.drcrf, "1")) {
				journalDto.pay = DECIMAL_FORMAT_2.format(amount);
			}
			else {
				journalDto.income = DECIMAL_FORMAT_2.format(amount);
			}
			journalDto.incomePayType = getIncomePayType(cache, journalDto, null, null);
			journalDto.journalType = "0";
			if (StringUtils.isNotBlank(openingBank)) {
				journalDto.openingBank = openingBank;
			}
			if (StringUtils.isNotBlank(exchangeRate)) {
				journalDto.exchangeRate = exchangeRate;
			}
			journalDtoList.add(journalDto);
		}
		int countJournal = importJournal(cache, journalDtoList);
		String message = "本次成功导入" + countJournal + "条日记账。";
		return new ResponseDto(200, "导入成功！", message);
	}

	/**
	 * 导入扫描历史
	 */
	public ResponseDto importJournalImage(JournalImageLog journalImageLog, String openingBank) {
		Long accountBookId = journalImageLog.accountBookId;
		DBCache cache = new DBCache();
		cache.accountBook = accountBookDao.findOne(accountBookId);
		if (cache.accountBook.closePeriod != null && journalImageLog.period.compareTo(cache.accountBook.closePeriod) <= 0) {
			logger.info("已结账的期间无法导入日记账!");
			throw new ServiceException("已结账的期间无法导入日记账!", ErrorCode.BAD_REQUEST);
		}
		cache.subjects = subjectDao.findByAccountBookId(accountBookId);
		cache.dimItems = dimItemDao.findByAccountBookId(accountBookId);
		cache.dimCustomers = dimCustomerDao.findByAccountBookId(accountBookId);
		cache.dimProviders = dimProviderDao.findByAccountBookId(accountBookId);
		cache.dimDepartments = dimDepartmentDao.findByAccountBookId(accountBookId);
		cache.dimEmployees = dimEmployeeDao.findByAccountBookId(accountBookId);
		cache.dimInventories = dimInventoryDao.findByAccountBookId(accountBookId);
		cache.voucherTemplates = voucherTemplateDao.findByAccountBookId(accountBookId);
		cache.relations = subjectDimRelationDao.findByAccountBookId(accountBookId);
		cache.journals = journalDao.findByAccountBookIdOrderByIdDesc(accountBookId);
		cache.cashFlowItems = cashFlowItemDao.findByAccountBookId(accountBookId);
		cache.vrs = voucherRecordDao.findByAccountBookIdOrderByIdDesc(accountBookId);
		cache.systemSettings = systemSettingDao.findByAccountBookId(accountBookId);

		ExocrResponseDto responseDto = JsonMapper.nonEmptyMapper().fromJson(journalImageLog.resultContent, ExocrResponseDto.class);
		if (responseDto.result.simpleStatement == null) {
			throw new ServiceException("识别失败", ErrorCode.BAD_REQUEST);
		}
		List<JournalDto> journalDtoList = new ArrayList<>();
		for (ExocrJournalStatementSimpleDto data : responseDto.result.simpleStatement) {
			JournalDto journalDto = new JournalDto();
			journalDto.accountBookId = String.valueOf(journalImageLog.accountBookId);
			journalDto.period = journalImageLog.period;
			journalDto.journalDate = StringUtils.removeAll(data.date, "-");
			journalDto.summary = data.type;
			BigDecimal amount = getDecimalValue(data.amount);
			if (amount.compareTo(BigDecimal.ZERO) > 0) {
				journalDto.income = String.valueOf(amount.abs());
			}
			else {
				journalDto.pay = String.valueOf(amount.abs());
			}
			journalDto.oppositeAccountName = data.counterparty_name;
			journalDto.incomePayType = getIncomePayType(cache, journalDto, null, null);
			journalDto.journalType = "0";
			if (StringUtils.isNotBlank(openingBank)) {
				journalDto.openingBank = openingBank;
			}
			journalDtoList.add(journalDto);
		}
		int countJournal = importJournal(cache, journalDtoList);
		String message = "本次成功导入" + countJournal + "条日记账。";
		return new ResponseDto(200, "导入成功！", message);
	}

    public int importJournal(DBCache cache, List<JournalDto> journalDtoList) {
		if (journalDtoList.isEmpty()) {
            return 0;
		}

		String currentPeriod = collectionPeriodService.getCollectionPeriod(null, null);
		int isAuto = systemSettingService.getSystemSettingValue(cache, JOURNAL_AUTO.keyword).intValue();
		//导入
		int countJournal = 0;
		List<VoucherRecordDto> recordDtoList = new ArrayList<>();
		if (isAuto == 1) {
			String maxNo = voucherDao.findMaxVoucherNo(cache.accountBook.id, journalDtoList.get(0).period);
			for (JournalDto journalDto : journalDtoList) {
				maxNo = getNextVoucherNo(maxNo, VOUCHER_NO_LENGTH);
				journalDto.voucherNo = maxNo;
				// 保存日记账
				Journal temp = addJournal(cache, journalDto, currentPeriod);
				// 生成凭证
				VoucherDto voucherDto = createVoucher(cache, temp, journalDto.voucherNo, currentPeriod, journalDto.exchangeRate);
				countJournal++;
				recordDtoList.addAll(voucherDto.voucherRecords);
			}
			balanceService.changeBalance(null, Long.valueOf(journalDtoList.get(0).accountBookId), voucherService.trimVoucherRecords(recordDtoList, null), journalDtoList.get(0).period, true);
		} else {
			for (JournalDto journalDto : journalDtoList) {
				// 保存日记账
				addJournal(cache, journalDto, currentPeriod);
				countJournal++;
			}
		}
        return countJournal;
	}

	/**
	 * 根据业务类型名称获取业务的编号
	 */
	private String getIncomePayType(DBCache dbCache, JournalDto journal, String remark, String typeName) {
		for (JournalType journalType : JournalType.values()) {
			if (StringUtils.equals(journalType.importName, typeName)) {
				return journalType.codeValue;
			}
		}

	    // 根据业务类型匹配（中行）
		if (StringUtils.equals(typeName, TYPE_NAME_3)) {
			return TYPE_3.codeValue;
		}
		if (StringUtils.equals(typeName, TYPE_NAME_6)) {
			return TYPE_6.codeValue;
		}
		if (StringUtils.equals(typeName, TYPE_NAME_7)) {
			return TYPE_7.codeValue;
		}

		// 对方户名为"个人／单位存款"的做其他（取现或者存现）
		if (StringUtils.equals(journal.oppositeAccountName, OPPOSITE_ACCOUNT_NAME_8)) {
			return TYPE_8.codeValue;
		}

		// 对方户名为"宁波银行"或"手续费"的做手续费
		if (StringUtils.equalsAny(journal.oppositeAccountName, OPPOSITE_ACCOUNT_NAME_3)) {
			return TYPE_3.codeValue;
		}

		// 上海农商银行手续费
		if (StringUtils.containsAny(journal.summary, SUMMARY_3)
				|| StringUtils.containsAny(remark, SUMMARY_3)
				|| StringUtils.containsAny(journal.oppositeAccountName, SUMMARY_3)) {
			return TYPE_3.codeValue;
		}

		// 北京银行社保
		if (StringUtils.equals(REMARK_5, remark)) {
			return TYPE_5.codeValue;
		}

		// 根据对方户名、摘要、备注中的关键字匹配业务类型
	    if (StringUtils.containsAny(journal.summary, TYPE_NAME_KEY_2) ||
                StringUtils.containsAny(remark, TYPE_NAME_KEY_2) ||
                StringUtils.containsAny(journal.oppositeAccountName, TYPE_NAME_KEY_2)) {
            return TYPE_2.codeValue;
        }
        // 对方户名为空（手续费，备用金）
		boolean isIncome = StringUtils.isNotBlank(journal.income) && new BigDecimal(journal.income).compareTo(BigDecimal.ZERO) != 0;
		if (StringUtils.isBlank(journal.oppositeAccountName)) {
			if (StringUtils.containsAny(journal.summary, TYPE_NAME_KEY_3)
					|| StringUtils.containsAny(remark, TYPE_NAME_KEY_3)
					|| StringUtils.containsAny(journal.oppositeAccountName, TYPE_NAME_KEY_3)
					|| StringUtils.equals(REMARK_3, remark)) {
				if (isIncome) {
					return TYPE_7.codeValue;
				}
				else {
					return TYPE_3.codeValue;
				}
			}
			if (StringUtils.containsAny(journal.summary, TYPE_NAME_KEY_8) ||
					StringUtils.containsAny(remark, TYPE_NAME_KEY_8) ||
					StringUtils.containsAny(journal.oppositeAccountName, TYPE_NAME_KEY_8)) {
				return TYPE_8.codeValue;
			}
		}
		// 对方户名为单位名（社保，公积金）
		else if (StringUtils.length(journal.oppositeAccountName) > PERSONAL_MAX_SIZE) {
			if (StringUtils.containsAny(journal.summary, TYPE_NAME_KEY_5) ||
					StringUtils.containsAny(remark, TYPE_NAME_KEY_5) ||
					StringUtils.containsAny(journal.oppositeAccountName, TYPE_NAME_KEY_5)) {
				return TYPE_5.codeValue;
			}
			if (StringUtils.containsAny(journal.summary, TYPE_NAME_KEY_11) ||
					StringUtils.containsAny(remark, TYPE_NAME_KEY_11) ||
					StringUtils.containsAny(journal.oppositeAccountName, TYPE_NAME_KEY_11)) {
				return TYPE_11.codeValue;
			}
		}

		if (StringUtils.containsAny(journal.summary, TYPE_NAME_KEY_10) ||
				StringUtils.containsAny(remark, TYPE_NAME_KEY_10) ||
				StringUtils.containsAny(journal.oppositeAccountName, TYPE_NAME_KEY_10)) {
			return TYPE_10.codeValue;
		}

		if (StringUtils.containsAny(journal.summary, TYPE_NAME_KEY_6) ||
				StringUtils.containsAny(remark, TYPE_NAME_KEY_6) ||
				StringUtils.containsAny(journal.oppositeAccountName, TYPE_NAME_KEY_6)) {
			return TYPE_6.codeValue;
		}
        if (StringUtils.containsAny(journal.summary, TYPE_NAME_KEY_7) ||
                StringUtils.containsAny(remark, TYPE_NAME_KEY_7) ||
                StringUtils.containsAny(journal.oppositeAccountName, TYPE_NAME_KEY_7)) {
            return TYPE_7.codeValue;
        }
		if (StringUtils.containsAny(journal.summary, TYPE_NAME_KEY_12) ||
				StringUtils.containsAny(remark, TYPE_NAME_KEY_12) ||
				StringUtils.containsAny(journal.oppositeAccountName, TYPE_NAME_KEY_12)) {
			return TYPE_12.codeValue;
		}
		if (StringUtils.containsAny(journal.summary, TYPE_NAME_KEY_13) ||
				StringUtils.containsAny(remark, TYPE_NAME_KEY_13) ||
				StringUtils.containsAny(journal.oppositeAccountName, TYPE_NAME_KEY_13)) {
			return TYPE_13.codeValue;
		}

        // 其他情况大多为收付款，检查应收，应付，其他应收，其他应付中是否有匹配对方户名的下级科目
		for (VoucherRecord vr : dbCache.vrs) {
			if (StringUtils.contains(vr.foresubText, journal.oppositeAccountName)) {
				// 应收账款
				if (vr.foresubText.contains(ACCOUNTS_RECEIVABLE)) {
					return TYPE_0.codeValue;
				}
				// 其他应收款
				if (vr.foresubText.contains(OTHER_RECEIVABLE)) {
					return TYPE_9.codeValue;
				}
				// 应付账款
				if (vr.foresubText.contains(ACCOUNTS_PAYABLE)) {
					return TYPE_4.codeValue;
				}
				// 其他应付款
				if (vr.foresubText.contains(OTHER_PAYABLE)) {
					return TYPE_1.codeValue;
				}
			}
		}

		// 如果以上都无法匹配，再根据收支方向和对方户名判断
		if (isIncome) {
			if (StringUtils.length(journal.oppositeAccountName) <= PERSONAL_MAX_SIZE) {
				return TYPE_1.codeValue;
			}
			else {
				return TYPE_0.codeValue;
			}
		}
		else {
			if (StringUtils.length(journal.oppositeAccountName) <= PERSONAL_MAX_SIZE) {
				return TYPE_9.codeValue;
			}
			else {
				return TYPE_4.codeValue;
			}
		}
	}

	/**
	 * 工行签约跳转
	 */
	public ResponseDto addBankAgree(String accountBookId, Integer type, Integer collectType) {
		BankAccount bankAccount = bankAccountDao.findByAccountBookIdAndTypeAndCollectType(Long.parseLong(accountBookId), type, collectType);
		if (bankAccount == null || StringUtils.isBlank(bankAccount.account)) {
			throw new ServiceException("银行账号设置不完整，无法签约！", ErrorCode.BAD_REQUEST);
		}
		if (collectType == 0) {
			switch (type) {
				case ICBC : return new ResponseDto(200, "签约地址获取成功", "https://corporbank-simp.icbc.com.cn/icbc/normalbank/index.jsp");
				case PABC : {
					AccountBook accountBook = accountBookDao.findAccountBookById(bankAccount.accountBookId);
					PabcUtils.addAgree(bankAccount.account, accountBook.bookName, bankAccount.accountBookId);
					return new ResponseDto(200, "签约地址获取成功", "https://e.orangebank.com.cn/brcp/stp/cust/ebank/front/#/menus/accountManageConfirmSign?bussinessCode=131606");
				}
				case BCM : {
					AccountBook accountBook = accountBookDao.findAccountBookById(bankAccount.accountBookId);
					BcmUtils.addAgree(bankAccount.account, accountBook.bookName);
					return new ResponseDto(200, "签约地址获取成功", "https://www.bankcomm.com");
				}
				default : return new ResponseDto(1, "银行种类错误", null);
			}
		}else{
			return new ResponseDto(1, "银行种类错误", null);
		}
	}

	/**
	 * 工行签约查询
	 */
	public ResponseDto getBankAgree(String accountBookIdStr, String period, Integer type, Integer collectType) {
		Long accountBookId = Long.parseLong(accountBookIdStr);
		BankAccount bankAccount = bankAccountDao.findByAccountBookIdAndTypeAndCollectType(accountBookId, type, collectType);
		if (bankAccount == null || StringUtils.isBlank(bankAccount.account)) {
			throw new ServiceException("银行账号设置不完整，无法查询是否签约！", ErrorCode.BAD_REQUEST);
		}
		Map<String, Object> result = new HashMap<>();
        if (collectType == 0) {
            switch (type) {
                case ICBC: {
                    AgreeNoDto agreeNoDto = IcbcUtils.getAgreeNo(bankAccount.account);
                    boolean isAgree = agreeNoDto != null;
                    result.put("isAgree", isAgree);
                    List<BankAccountDetailIcbc> bankAccountDetailIcbcList =
                        bankAccountDetailIcbcDao.findByAccountBookIdAndPeriod(accountBookId, period);
                    result.put("isJournal", !bankAccountDetailIcbcList.isEmpty());
                    boolean isReceipt = false;
                    for (BankAccountDetailIcbc bankAccountDetailIcbc : bankAccountDetailIcbcList) {
                        if (StringUtils.isNotBlank(bankAccountDetailIcbc.receiptUrl)) {
                            isReceipt = true;
                        }
                    }
                    result.put("isReceipt", isReceipt);
                    break;
                }
                case PABC: {
                    result.put("isAgree", PabcUtils.getAgree(bankAccount.account, accountBookId));
                    List<BankAccountDetailPabc> bankAccountDetailPabcList =
                        bankAccountDetailPabcDao.findByAccountBookIdAndPeriod(accountBookId, period);
                    result.put("isJournal", !bankAccountDetailPabcList.isEmpty());
                    boolean isReceipt = false;
                    for (BankAccountDetailPabc bankAccountDetailPabc : bankAccountDetailPabcList) {
                        if (StringUtils.isNotBlank(bankAccountDetailPabc.receiptUrl)) {
                            isReceipt = true;
                        }
                    }
                    result.put("isReceipt", isReceipt);
                    break;
                }
                case BCM: {
                    AccountBook accountBook = accountBookDao.findAccountBookById(accountBookId);
                    result.put("isAgree", BcmUtils.getAgree(bankAccount.account, accountBook.bookName));
                    List<BankAccountDetailBcm> bankAccountDetailBcmList =
                        bankAccountDetailBcmDao.findByAccountBookIdAndPeriod(accountBookId, period);
                    result.put("isJournal", !bankAccountDetailBcmList.isEmpty());
                    boolean isReceipt = false;
                    for (BankAccountDetailBcm bankAccountDetailBcm : bankAccountDetailBcmList) {
                        if (StringUtils.isNotBlank(bankAccountDetailBcm.receiptUrl)) {
                            isReceipt = true;
                        }
                    }
                    result.put("isReceipt", isReceipt);
                    break;
                }
                default: {
                   throw new ServiceException("银行种类错误", ErrorCode.BAD_REQUEST);
                }
            }
        } else {
            boolean isJournal = false;
            boolean isReceipt = false;
            BankTask bankTask = bankTaskService.findByAccountBookIdAndPeriodAndBankType(accountBookId, period, type);
            if (bankTask != null) {
                if (StringUtils.isNotBlank(bankTask.journalResultUrl)) {
                    isJournal = true;
                }
                if (StringUtils.isNotBlank(bankTask.receiptResultUrl)) {
                    isReceipt = true;
                }
            }
            result.put("bankTask", bankTask);
            result.put("isJournal", isJournal);
            result.put("isReceipt", isReceipt);
		}
		Object isAgree = result.get("isAgree");
		if (isAgree != null && (Boolean) isAgree) {
			return new ResponseDto(200, "签约成功", result);
		}
		else {
			return new ResponseDto(200, "未签约", result);
		}
	}

	/**
	 * 保存银行账号
	 */
	public ResponseDto saveBankAccount(BankAccountDto bankAccountDto) {
		if (bankAccountDto.type == null) {
			bankAccountDto.type = ICBC;
		}
		if(StringUtils.contains(bankAccountDto.account," ")){
			bankAccountDto.account = bankAccountDto.account.replaceAll(" ","");
		}
		return new ResponseDto(200, "保存成功！", BeanMapper.map(bankAccountDao.save(BeanMapper.map(bankAccountDto, BankAccount.class)), BankAccountDto.class));
	}

	/**
	 * 查询银行账号默认
	 */
	public ResponseDto initBankAccount(Long accountBookId, Integer collectType) {
		int type = ICBC;
		BankAccount bankAccount = bankAccountDao.findFirstByAccountBookIdAndCollectTypeOrderByGmtModifiedDesc(accountBookId, collectType);
		if (bankAccount != null) {
			type = bankAccount.type;
		}
		return new ResponseDto(200, "获取成功", type);
	}

	/**
	 * 查询银行账号
	 */
	public BankAccountDto getBankAccount(Long accountBookId, Integer type, Integer collectType) {
		if (type == null) {
			type = ICBC;
		}
		BankAccount bankAccount = bankAccountDao.findByAccountBookIdAndTypeAndCollectType(accountBookId, type, collectType);
		if (bankAccount == null) {
			bankAccount = new BankAccount();
			bankAccount.accountBookId = accountBookId;
			bankAccount.type = type;
		}
		return BeanMapper.map(bankAccount, BankAccountDto.class);
	}

	/**
	 * 导出工行银行对账单excel
	 */
	public MyWorkbook getBankAccountDetailIcbcWorkbook(Long accountBookId, String period) {
		List<BankAccountDetailIcbc> icbcs = bankAccountDetailIcbcDao.findByAccountBookIdAndPeriod(accountBookId, period);
		AccountBook book = accountBookDao.findAccountBookById(accountBookId);
		MyWorkbook workbook = new MyWorkbook();
		// 创建工作表
		workbook.createSheet("工商银行对账单" , new int[]{2745, 2237, 2237, 2237, 4280, 4280, 4280, 3768, 3768, 3768});
		// 添加副标题
		workbook.addLeftSubTitle("A1", "账号：");
		workbook.addLeftSubTitle("A2", "户名：");
		workbook.addLeftSubTitle("B2", book.bookName);
		workbook.addLeftSubTitle("A3", "币种：");
		workbook.addLeftSubTitle("B3", "人民币");
		// 添加表头
		workbook.addHeaders("A4", new String[]{"日期", "交易类型", "凭证种类", "凭证号", "对方户名", "对方账号", "摘要", "借方发生额", "贷方发生额", "余额"});
		for (int i = 0; i < icbcs.size(); i++) {
			int rowIndex = i + 5;
			BankAccountDetailIcbc icbc = icbcs.get(i);
			BigDecimal jfBalance = BigDecimal.ZERO;
			BigDecimal dfBalance = BigDecimal.ZERO;
			int dir = Integer.parseInt(icbc.drcrf);
			if (dir == DEBIT) {
				jfBalance = BigDecimalUtils.divide(icbc.amount, "100");
			} else {
				dfBalance = BigDecimalUtils.divide(icbc.amount, "100");
			}
			workbook.addCenterData("A" + rowIndex, icbc.busiDate);
			workbook.addCenterData("B" + rowIndex, "转账");
			workbook.addCenterData("C" + rowIndex, icbc.vouhtype);
			workbook.addCenterData("D" + rowIndex, icbc.vouhno);

			workbook.addCenterData("E" + rowIndex, icbc.reciPnam);
			workbook.addCenterData("F" + rowIndex, icbc.reciPacc);
			workbook.addCenterData("G" + rowIndex, icbc.summary);
			workbook.addBigDecimalData("H" + rowIndex, jfBalance);
			workbook.addBigDecimalData("I" + rowIndex, dfBalance);
			workbook.addBigDecimalData("J" + rowIndex, BigDecimalUtils.divide(icbc.balance, "100"));
		}
		return workbook;
	}

	/**
	 * 更新回单数据
	 */
	public void refreshReceiptData(Map<String, String> fileNameMap, BankAccountDetailIcbc orderData) {
		List<BankAccountDetailIcbc> bankAccountDetailIcbcList = bankAccountDetailIcbcDao.findByAccountBookIdAndPeriod(orderData.accountBookId, orderData.period);
		List<BankAccountDetailIcbc> bankAccountDetailIcbcSaveList = new ArrayList<>();
		List<Journal> journalList = journalDao.findByAccountBookIdAndPeriod(orderData.accountBookId, orderData.period);
		List<Journal> journalSaveList = new ArrayList<>();
		for (Map.Entry<String, String> temp : fileNameMap.entrySet()) {
			String fileName = temp.getKey();
			String serialno = IcbcUtils.parseSerialno(fileName);
			String objectName = temp.getValue();
			String receiptUrl = BASE_FILE_URL + objectName;
			BankAccountDetailIcbc bankAccountDetailIcbc = getBySerialno(bankAccountDetailIcbcList, serialno);
			if (bankAccountDetailIcbc != null) {
				bankAccountDetailIcbc.receiptUrl = receiptUrl;
				bankAccountDetailIcbcSaveList.add(bankAccountDetailIcbc);
			}
			Journal journal = getByReceiptNo(journalList, serialno);
			if (journal != null) {
				journal.receiptUrl = receiptUrl;
				journalSaveList.add(journal);
			}
		}
		bankAccountDetailIcbcDao.saveAll(bankAccountDetailIcbcSaveList);
		journalDao.saveAll(journalSaveList);
	}

	private BankAccountDetailIcbc getBySerialno(List<BankAccountDetailIcbc> bankAccountDetailIcbcList, String serialno) {
		for (BankAccountDetailIcbc bankAccountDetailIcbc : bankAccountDetailIcbcList) {
			if (StringUtils.equals(bankAccountDetailIcbc.serialno, serialno)) {
				return bankAccountDetailIcbc;
			}
		}
		return null;
	}

	private BankAccountDetailPabc getByHostTraceAndAbstractStr(List<BankAccountDetailPabc> bankAccountDetailPabcList, String HostTrace, String AbstractStr) {
		for (BankAccountDetailPabc bankAccountDetailPabc : bankAccountDetailPabcList) {
			if (StringUtils.equals(bankAccountDetailPabc.HostTrace, HostTrace) && StringUtils.equals(bankAccountDetailPabc.AbstractStr, AbstractStr)) {
				return bankAccountDetailPabc;
			}
		}
		return null;
	}

	private BankAccountDetailIcbc getIcbcDetailByReceiptNo(List<BankAccountDetailIcbc> bankAccountDetailIcbcList, String receiptNo) {
		for (BankAccountDetailIcbc bankAccountDetailIcbc : bankAccountDetailIcbcList) {
			if (StringUtils.equals(bankAccountDetailIcbc.serialno, receiptNo)) {
				return bankAccountDetailIcbc;
			}
		}
		return null;
	}

	private BankAccountDetailBcm getByJrnlNo(List<BankAccountDetailBcm> bankAccountDetailBcmList, String jrnlNo, String jrnlSeqNo) {
		for (BankAccountDetailBcm bankAccountDetailBcm : bankAccountDetailBcmList) {
			if (StringUtils.equals(bankAccountDetailBcm.jrnlNo, jrnlNo) && StringUtils.equals(bankAccountDetailBcm.jrnlSeqNo, jrnlSeqNo)) {
				return bankAccountDetailBcm;
			}
		}
		return null;
	}

	private Journal getByReceiptNo(List<Journal> journalList, String serialno) {
		for (Journal journal : journalList) {
			if (StringUtils.equals(journal.receiptNo, serialno)) {
				return journal;
			}
		}
		return null;
	}

	public Map<String, BankAccountDetailIcbc> getOrderMap() {
		return orderList.asMap();
	}

	public void deleteOrderMap(String fseqno) {
		orderList.invalidate(fseqno);
	}

	/**
	 * 工行获取当月回单url
	 */
	public List<String> getReceiptUrls(Long accountBookId, String period, Integer type) {
		List<String> result = new ArrayList<>();
		switch (type) {
			case ICBC : {
				List<BankAccountDetailIcbc> bankAccountDetailIcbcList = bankAccountDetailIcbcDao.findByAccountBookIdAndPeriod(accountBookId, period);
				for (BankAccountDetailIcbc bankAccountDetailIcbc : bankAccountDetailIcbcList) {
					if (StringUtils.isNotBlank(bankAccountDetailIcbc.receiptUrl)) {
						result.add(bankAccountDetailIcbc.receiptUrl);
					}
				}
				break;
			}
			case PABC : {
				List<BankAccountDetailPabc> bankAccountDetailPabcList = bankAccountDetailPabcDao.findByAccountBookIdAndPeriod(accountBookId, period);
				for (BankAccountDetailPabc bankAccountDetailPabc : bankAccountDetailPabcList) {
					if (StringUtils.isNotBlank(bankAccountDetailPabc.receiptUrl)) {
						result.add(bankAccountDetailPabc.receiptUrl);
					}
				}
				break;
			}
			case BCM : {
				List<BankAccountDetailBcm> bankAccountDetailBcmList = bankAccountDetailBcmDao.findByAccountBookIdAndPeriod(accountBookId, period);
				for (BankAccountDetailBcm bankAccountDetailBcm : bankAccountDetailBcmList) {
					if (StringUtils.isNotBlank(bankAccountDetailBcm.receiptUrl)) {
						result.add(bankAccountDetailBcm.receiptUrl);
					}
				}
				break;
			}
			default: {
				BankTask bankTask = bankTaskService.findByAccountBookIdAndPeriodAndBankType(accountBookId, period, type);
				if (bankTask != null && StringUtils.isNotBlank(bankTask.receiptResultUrl)) {
					result.add(bankTask.receiptResultUrl);
				}
			}
		}
		return result;
	}

	/**
	 * 打印回单
	 */
	public void printReceipt(String idStrs, HttpServletResponse response, String version, String token) {
		accountService.checkTokenAndVersion(version, token);
		String[] idStrList = idStrs.split(",");
		List<InputStream> files = new ArrayList<>();
		for (String idStr : idStrList) {
			Long id = Long.parseLong(idStr);
			Journal journal = journalDao.findOne(id);
			if (journal == null || StringUtils.isBlank(journal.receiptUrl)) {
				continue;
			}
			ResponseEntity<byte[]> data = restTemplate.getForEntity(journal.receiptUrl, byte[].class);
			InputStream inputStream = new ByteArrayInputStream(Objects.requireNonNull(data.getBody()));
			files.add(inputStream);
		}
		response.reset();
		response.setContentType(MediaTypes.APPLICATION_PDF_UTF_8);
		response.setHeader("Content-Disposition", "attachment;filename=" + new String(("回单.pdf").getBytes(), StandardCharsets.ISO_8859_1));
		try {
			PdfUtils.mergePdf(files, response.getOutputStream());
		} catch (IOException e) {
			throw new ServiceException("IO错误", ErrorCode.BAD_REQUEST);
		}
	}


	/**
	 * 导出平安银行对账单excel
	 */
	public MyWorkbook getBankAccountDetailPabcWorkbook(Long accountBookId, String period) {
		List<BankAccountDetailPabc> pabcList = bankAccountDetailPabcDao.findByAccountBookIdAndPeriod(accountBookId, period);
		AccountBook book = accountBookDao.findAccountBookById(accountBookId);
		MyWorkbook workbook = new MyWorkbook();
		// 创建工作表
		workbook.createSheet("平安银行对账单" , new int[]{2745, 2237, 2237, 2237, 4280, 4280, 4280, 3768, 3768, 3768});
		// 合并单元格
		workbook.mergeCell("A1,J1 A2,F2");
		// 添加标题
		workbook.addTitle("A1", "平安银行");
		// 添加副标题
		workbook.addLeftSubTitle("A2", "编制单位：" + book.bookName);
		// 添加表头
		workbook.addHeaders("A3", new String[]{"交易日期", "账号", "借", "贷", "用户余额", "对方账户", "对方账户名称", "交易流水号", "摘要", "用途"});
		for (int i = 0; i < pabcList.size(); i++) {
			int rowIndex = i + 4;
			BankAccountDetailPabc pabc = pabcList.get(i);
			BigDecimal jfBalance = BigDecimal.ZERO;
			BigDecimal dfBalance = BigDecimal.ZERO;

			// 账号，对方账户，对方户名
			String account;
			String openingAccount;
			String oppositeAccountName;
			// 借方是D，贷方是C
			if (StringUtils.equals(pabc.DcFlag, "D")) {
				jfBalance = getDecimalValue(pabc.TranAmount);
				account = pabc.OutAcctNo;
				openingAccount = pabc.InAcctNo;
				oppositeAccountName = pabc.InAcctName;
			} else {
				dfBalance = getDecimalValue(pabc.TranAmount);
				account = pabc.InAcctNo;
				openingAccount = pabc.OutAcctNo;
				oppositeAccountName = pabc.OutAcctName;
			}
			workbook.addCenterData("A" + rowIndex, pabc.AcctDate);
			workbook.addCenterData("B" + rowIndex, account);
			workbook.addBigDecimalData("C" + rowIndex, jfBalance);
			workbook.addBigDecimalData("D" + rowIndex, dfBalance);

			workbook.addBigDecimalData("E" + rowIndex, pabc.AcctBalance);
			workbook.addCenterData("F" + rowIndex, openingAccount);
			workbook.addCenterData("G" + rowIndex, oppositeAccountName);
			workbook.addCenterData("H" + rowIndex, pabc.HostTrace);
			workbook.addCenterData("I" + rowIndex, pabc.AbstractStr_Desc);
			workbook.addCenterData("J" + rowIndex, pabc.Purpose);
		}
		return workbook;
	}


	/**
	 * 平安银行接口导入
	 */
	public ResponseDto importPabcJournal(DBCache cache, String period, String accountBookIdStr, String openingBank, String exchangeRate) {
		List<BankAccountDetailPabc> dataList = bankAccountDetailPabcDao.findByAccountBookIdAndPeriod(Long.parseLong(accountBookIdStr), period);
		List<JournalDto> journalDtoList = new ArrayList<>();
		for (BankAccountDetailPabc data : dataList) {
			JournalDto journalDto = new JournalDto();
			journalDto.accountBookId = accountBookIdStr;
			journalDto.period = period;
			journalDto.journalDate = data.AcctDate;
			journalDto.summary = data.AbstractStr_Desc;
			journalDto.receiptNo = data.HostTrace + "#" + data.AbstractStr;
			journalDto.receiptUrl = data.receiptUrl;

			BigDecimal amount = getDecimalValue(data.TranAmount);
			if (StringUtils.equals(data.DcFlag, "D")) {
				journalDto.pay = DECIMAL_FORMAT_2.format(amount);
				journalDto.oppositeAccountName = data.InAcctName;
			}
			else {
				journalDto.income = DECIMAL_FORMAT_2.format(amount);
				journalDto.oppositeAccountName = data.OutAcctName;
			}
			journalDto.incomePayType = getIncomePayType(cache, journalDto, null, null);
			journalDto.journalType = "0";
			if (StringUtils.isNotBlank(openingBank)) {
				journalDto.openingBank = openingBank;
			}
			if (StringUtils.isNotBlank(exchangeRate)) {
				journalDto.exchangeRate = exchangeRate;
			}
			journalDtoList.add(journalDto);
		}
		int countJournal = importJournal(cache, journalDtoList);
		String message = "本次成功导入" + countJournal + "条日记账。";
		return new ResponseDto(200, "导入成功！", message);
	}

	/**
	 * 导出交通银行对账单excel
	 */
	public MyWorkbook getBankAccountDetailBcmWorkbook(Long accountBookId, String period) {
		List<BankAccountDetailBcm> bcmList = bankAccountDetailBcmDao.findByAccountBookIdAndPeriod(accountBookId, period);
		AccountBook book = accountBookDao.findAccountBookById(accountBookId);
		MyWorkbook workbook = new MyWorkbook();
		// 创建工作表
		workbook.createSheet("交通银行对账单" , new int[]{2745, 2745, 2745, 2745, 2745, 2745, 3628, 3628, 3628, 3628, 2745, 3628, 3768, 3768, 5500, 4280});
		// 合并单元格
		workbook.mergeCell("A2,P2 B4,E4 B5,E5 G5,I5");
		// 添加标题
		workbook.addTitle("A2", "交通银行对账单");
		// 添加副标题
		workbook.addLeftSubTitle("A4", "开户机构：");
		workbook.addLeftSubTitle("B4", "");
		workbook.addLeftSubTitle("F4", "币种：");
		workbook.addLeftSubTitle("G4", "人民币");
		workbook.addLeftSubTitle("H4", "年份：");
		workbook.addLeftSubTitle("I4", PeriodUtils.getYear(period));
		workbook.addLeftSubTitle("J4", "月份：");
		workbook.addLeftSubTitle("K4", StringUtils.substring(period, 4));
		workbook.addLeftSubTitle("A5", "账号：");
		workbook.addLeftSubTitle("B5", bcmList.get(0).acctNo);
		workbook.addLeftSubTitle("F5", "户名：");
		workbook.addLeftSubTitle("G5", book.bookName);
		// 添加表头
		workbook.addHeaders("A7", new String[]{"序号", "会计日期", "交易日期", "交易名称", "凭证种类", "凭证号码", "借方发生额", "贷方发生额", "余额", "卡号", "交易地点", "对方账号", "对方户名", "对方行名", "摘要", "流水号"});
		for (int i = 0; i < bcmList.size(); i++) {
			int rowIndex = i + 8;
			BankAccountDetailBcm bcm = bcmList.get(i);
			BigDecimal jfBalance = BigDecimal.ZERO;
			BigDecimal dfBalance = BigDecimal.ZERO;
			if (StringUtils.equals(bcm.incmExpdtFlg, "D")) {
				jfBalance = getDecimalValue(bcm.txnAmt);
			} else {
				dfBalance = getDecimalValue(bcm.txnAmt);
			}
			workbook.addCenterData("A" + rowIndex, i+1);
			workbook.addCenterData("B" + rowIndex, bcm.txnDt);
			workbook.addCenterData("C" + rowIndex, bcm.txnDt);
			workbook.addCenterData("D" + rowIndex, "");
			workbook.addCenterData("E" + rowIndex, bcm.bvCode);
			workbook.addCenterData("F" + rowIndex, bcm.bvNo);
			workbook.addBigDecimalData("G" + rowIndex, jfBalance);
			workbook.addBigDecimalData("H" + rowIndex, dfBalance);
			workbook.addBigDecimalData("I" + rowIndex, bcm.acctBal);
			workbook.addCenterData("J" + rowIndex, bcm.cntrpBankNo);
			workbook.addCenterData("K" + rowIndex, bcm.cntrpAddr);
			workbook.addCenterData("L" + rowIndex, bcm.cntrpAcctNo);
			workbook.addCenterData("M" + rowIndex, bcm.cntrpAcctNme);
			workbook.addCenterData("N" + rowIndex, bcm.cntrpBankNme);
			workbook.addCenterData("O" + rowIndex, bcm.remrk);
			workbook.addCenterData("P" + rowIndex, bcm.jrnlNo);
		}
		return workbook;
	}


	/**
	 * 交通银行接口导入
	 */
	public ResponseDto importBcmJournal(DBCache cache, String period, String accountBookIdStr, String openingBank, String exchangeRate) {
		List<BankAccountDetailBcm> dataList = bankAccountDetailBcmDao.findByAccountBookIdAndPeriod(Long.parseLong(accountBookIdStr), period);
		List<JournalDto> journalDtoList = new ArrayList<>();
		for (BankAccountDetailBcm data : dataList) {
			JournalDto journalDto = new JournalDto();
			journalDto.accountBookId = accountBookIdStr;
			journalDto.period = period;
			journalDto.journalDate = data.txnDt;
			journalDto.summary = data.remrk;
			journalDto.receiptNo = data.jrnlNo+"#"+data.jrnlSeqNo;
			journalDto.receiptUrl = data.receiptUrl;

			BigDecimal amount = getDecimalValue(data.txnAmt);
			if (StringUtils.equals(data.incmExpdtFlg, "D")) {
				journalDto.pay = DECIMAL_FORMAT_2.format(amount);
			}
			else {
				journalDto.income = DECIMAL_FORMAT_2.format(amount);
			}
			journalDto.oppositeAccountName = data.cntrpAcctNme;
			journalDto.incomePayType = getIncomePayType(cache, journalDto, null, null);
			journalDto.journalType = "0";
			if (StringUtils.isNotBlank(openingBank)) {
				journalDto.openingBank = openingBank;
			}
			if (StringUtils.isNotBlank(exchangeRate)) {
				journalDto.exchangeRate = exchangeRate;
			}
			journalDtoList.add(journalDto);
		}
		int countJournal = importJournal(cache, journalDtoList);
		String message = "本次成功导入" + countJournal + "条日记账。";
		return new ResponseDto(200, "导入成功！", message);
	}

	public ResponseDto importChinaBankReceipt(MultipartHttpServletRequest request) {
		MultipartFile file = request.getFile("file");
		String bankType = request.getParameter("bankType");
		String accountBookIdStr = request.getParameter("accountBookId");
		String endPeriod = request.getParameter("endPeriod");
		String openingBank = request.getParameter("openingBank");
		String account = request.getParameter("account");
		String exchangeRate = request.getParameter("exchangeRate");
		return importChinaBankReceipt(file, null, bankType, accountBookIdStr, endPeriod, openingBank, exchangeRate, account);
	}

    /**
     * 导入回单
     */
    public ResponseDto importChinaBankReceipt(MultipartFile file, String fileUrl, String bankTypeStr, String accountBookIdStr, String endPeriod, String openingBank, String exchangeRate, String account) {
        InputStream inputStream;
		String fileName;
    	if (StringUtils.isNotBlank(fileUrl)) {
            ResponseEntity<byte[]> data = restTemplate.getForEntity(fileUrl, byte[].class);
            inputStream = new ByteArrayInputStream(Objects.requireNonNull(data.getBody()));
            fileName = fileUrl.substring(fileUrl.lastIndexOf('/') + 1);
        } else {
			if (file == null) {
				return new ResponseDto(1, "找不到文件", null);
			}
			try {
				inputStream = file.getInputStream();
			} catch (IOException e) {
				return new ResponseDto(1, "找不到文件", null);
			}
			fileName = file.getOriginalFilename();
		}
        ArrayList<ReceiptDto> receipts = new ArrayList<>();
		int bankType = Integer.parseInt(bankTypeStr);
		switch (bankType) {
			case BOC : {
				receipts = transferPdfToExcel(fileName, inputStream, account);
				break;
			}
			case CCB : {
				receipts = transferCCBPdfToExcel(fileName, inputStream, account);
				break;
			}
			case ABC : {
				receipts = transferABCPdfToExcel(fileName, inputStream, account);
				break;
			}
			case CITIC : {
				//是个压缩文件
				try{
					ZipInputStream zipInputStream = new ZipInputStream(inputStream);
					ZipEntry entry;
					while ((entry = zipInputStream.getNextEntry()) != null) {
						if (!entry.isDirectory()) {
							byte[] data = getByte(zipInputStream);
							InputStream ins = new ByteArrayInputStream(data);
                            ByteArrayOutputStream temp = PoiUtils.cloneInputStream(ins);
                            InputStream stream1 = new ByteArrayInputStream(temp.toByteArray());
                            ReceiptDto receiptDto = transferCITICPdfToExcel(stream1, account);
                            // 上传oss
                            InputStream stream2 = new ByteArrayInputStream(temp.toByteArray());
                            receiptDto.receiptUrl = uploadReceiptToOss(entry.getName(), stream2);
                            receipts.add(receiptDto);
						}
					}
					zipInputStream.closeEntry();
					zipInputStream.close();
				}catch(Exception e){
                    throw new ServiceException("文件解压失败", ErrorCode.BAD_REQUEST);
				}
                // 根据日期、金额、流水号去重
                Map<String, ReceiptDto> uniqueReceiptDtoMap = new HashMap<>();
                for (ReceiptDto receiptDto : receipts) {
                    String key = receiptDto.tradingDate + "-" + receiptDto.money + "-" + receiptDto.tradeNo;
                    if (!uniqueReceiptDtoMap.containsKey(key)) {
                        uniqueReceiptDtoMap.put(key, receiptDto);
                    }
                }
                receipts.clear();
                for (Map.Entry<String, ReceiptDto> entry : uniqueReceiptDtoMap.entrySet()) {
                    receipts.add(entry.getValue());
                }
                break;
			}
            case CEB: {
				if (StringUtils.endsWith(fileName, ".zip")) {
					try {
						ZipInputStream zipInputStream = new ZipInputStream(inputStream, Charset.forName("gbk"));
						ZipEntry entry;
						while ((entry = zipInputStream.getNextEntry()) != null) {
							if (!entry.isDirectory() && entry.getName().endsWith(".pdf")) {
								ArrayList<ReceiptDto> tempList;
								byte[]  data = getByte(zipInputStream);
								InputStream ins = new ByteArrayInputStream(data);
								tempList = transferCEBPdfToExcel(fileName, ins, account);
								receipts.addAll(tempList);
							}
						}
						zipInputStream.closeEntry();
						zipInputStream.close();
					} catch (Exception e) {
						throw new ServiceException("文件解压失败", ErrorCode.BAD_REQUEST);
					}
				}else{
					receipts = transferCEBPdfToExcel(fileName, inputStream, account);
				}
                break;
            }
			case HZCB: {
                // 直接导入流水表
                BankTask bankTask = bankTaskDao.findByAccountBookIdAndPeriodAndBankType(Long.parseLong(accountBookIdStr), endPeriod, bankType);
                receipts = importHZCBExcel(fileName, inputStream, account, accountBookIdStr, endPeriod, bankTask.receiptResultUrl);
				break;
			}
            case SHBANK: {
                receipts = transferSHBANKPdfToExcel(fileName, inputStream, account);
                break;
            }
            case CMB: {
                receipts = transferCMBPdfToExcel(fileName, inputStream, account);
                break;
            }
            case ICBC: {
                if (StringUtils.endsWith(fileName, ".zip")) {
                    // 是个压缩文件
                    try {
                        ZipInputStream zipInputStream = new ZipInputStream(inputStream, Charset.forName("gbk"));
                        ZipEntry entry;
                        while ((entry = zipInputStream.getNextEntry()) != null) {
                            if (!entry.isDirectory() && entry.getName().endsWith(".pdf")) {
                                byte[] data = getByte(zipInputStream);
                                InputStream ins = new ByteArrayInputStream(data);
                                ByteArrayOutputStream temp = PoiUtils.cloneInputStream(ins);
                                InputStream stream1 = new ByteArrayInputStream(temp.toByteArray());
                                ReceiptDto receiptDto = transferICBCPdfToExcel(fileName, stream1, account);
                                receipts.add(receiptDto);
                            }
                        }
                        zipInputStream.closeEntry();
                        zipInputStream.close();
                    } catch (Exception e) {
                        throw new ServiceException("文件解压失败", ErrorCode.BAD_REQUEST);
                    }
                } else {
                    // pdf导入
					try{
						PDDocument document = Loader.loadPDF(new RandomAccessReadBuffer(inputStream));
						List<PDDocument> split = split(document);
						for (PDDocument pdDocument : split) {
							ByteArrayOutputStream baos = new ByteArrayOutputStream();
							pdDocument.save(baos);
							byte[] byteArray = baos.toByteArray();
							InputStream ins = new ByteArrayInputStream(byteArray);
							ByteArrayOutputStream temp = PoiUtils.cloneInputStream(ins);
							InputStream stream1 = new ByteArrayInputStream(temp.toByteArray());
							ReceiptDto receiptDto = transferICBCPdfToExcel(fileName, stream1, account);
							receipts.add(receiptDto);
						}
					}catch(Exception e){
						throw new ServiceException("文件解析失败", ErrorCode.BAD_REQUEST);
					}
                }
                break;
            }
            case JSB: {
                if (StringUtils.endsWith(fileName, ".zip")) {
                    // 一页三个 压缩文件下面就一个pdf
                    try {
                        ZipInputStream zipInputStream = new ZipInputStream(inputStream);
                        ZipEntry entry;
                        while ((entry = zipInputStream.getNextEntry()) != null) {
                            if (!entry.isDirectory()
                                && (entry.getName().endsWith(".pdf") || entry.getName().endsWith(".PDF"))) {
                                byte[] data = getByte(zipInputStream);
                                InputStream ins = new ByteArrayInputStream(data);
                                receipts = transferJSBPdfToExcel(fileName, ins, account);
                            }
                        }
                        zipInputStream.closeEntry();
                        zipInputStream.close();
                    } catch (Exception e) {
                        throw new ServiceException("文件解压失败", ErrorCode.BAD_REQUEST);
                    }
                } else {
                    receipts = transferJSBPdfToExcel(fileName, inputStream, account);
                }
            }
            case SPDB: {
				if (StringUtils.endsWith(fileName, ".zip")) {
					try {
						ZipInputStream zipInputStream = new ZipInputStream(inputStream, Charset.forName("gbk"));
						ZipEntry entry;
						while ((entry = zipInputStream.getNextEntry()) != null) {
							if (!entry.isDirectory() && entry.getName().endsWith(".pdf")) {
								byte[] data = getByte(zipInputStream);
								InputStream ins = new ByteArrayInputStream(data);
								ArrayList<ReceiptDto> receiptDtos = transferSPDBPdfToExcel(fileName, ins, account);
								receipts.addAll(receiptDtos);
							}
						}
						zipInputStream.closeEntry();
						zipInputStream.close();
					} catch (Exception e) {
						throw new ServiceException("文件解压失败", ErrorCode.BAD_REQUEST);
					}
				}else{
					receipts = transferSPDBPdfToExcel(fileName, inputStream, account);
				}
                break;
            }
            case JSRCB: {
				if (StringUtils.endsWith(fileName, ".zip")) {
					try {
						ZipInputStream zipInputStream = new ZipInputStream(inputStream, Charset.forName("gbk"));
						ZipEntry entry;
						while ((entry = zipInputStream.getNextEntry()) != null) {
							if (!entry.isDirectory() && entry.getName().endsWith(".pdf")) {
								ArrayList<ReceiptDto> tempList;
								byte[] data = getByte(zipInputStream);
								InputStream ins = new ByteArrayInputStream(data);
								tempList = transferJSRCBPdfToExcel(fileName, ins, account);
								receipts.addAll(tempList);
							}
						}
						zipInputStream.closeEntry();
						zipInputStream.close();
					} catch (Exception e) {
						throw new ServiceException("文件解压失败", ErrorCode.BAD_REQUEST);
					}
				} else {
					receipts = transferJSRCBPdfToExcel(fileName, inputStream, account);
				}
                break;
            }
            case PSBC: {
                receipts = transferPSBCPdfToExcel(fileName, inputStream, account);
                break;
            }
            case CIB: {
                receipts = transferCIBPdfToExcel(fileName, inputStream, account);
                break;
            }
            case HB: {
                receipts = transferHBPdfToExcel(fileName, inputStream, account);
                break;
            }
            case SZB: {
                receipts = transferSZBPdfToExcel(fileName, inputStream, account);
                break;
            }
			case BCM: {
				receipts = transferBCMPdfToExcel(fileName, inputStream, account);
				break;
			}
			case CMBC: {
				receipts = transferCMBCPdfToExcel(fileName, inputStream, account);
				break;
			}
			case CSCB: {
				receipts = transferCSCBPdfToExcel(fileName, inputStream, account);
				break;
			}
			case PAB: {
				receipts = transferPABPdfToExcel(fileName, inputStream, account);
				break;
			}
            case HXB: {
                receipts = transferHXBPdfToExcel(fileName, inputStream, account);
                break;
            }
		}
		if (receipts == null) {
			return new ResponseDto(2, "非法银行类型", null);
		}
		return importBocJournal(endPeriod, accountBookIdStr, openingBank, exchangeRate, receipts);
    }

    /**
     * 删除pdf
     */
    public ResponseDto deleteReceiptUrl(Long id) {
        if (id == null) {
            return new ResponseDto(1, "删除失败，id不存在", null);
        }
        Journal journal = journalDao.findOne(id);
        if (journal == null) {
            return new ResponseDto(2, "删除失败，数据库中并没有该条数据", null);
        }
        journal.receiptUrl = null;
        journalDao.save(journal);
        return new ResponseDto(200, "删除成功", null);
    }

    // 上传回单pdf
    public ResponseDto upload(MultipartHttpServletRequest request) {
        MultipartFile file = request.getFile("file");
        String id = request.getParameter("id");
        if (StringUtils.isEmpty(id)) {
            return new ResponseDto(1, "上传失败，id不存在", null);
        }
        Journal journal = journalDao.findOne(Long.valueOf(id));
        if (journal == null) {
            return new ResponseDto(2, "上传失败，数据库中并没有该条数据", null);
        }
        // 上传oss
        try {
            assert file != null;
            journal.receiptUrl = uploadReceiptToOss(file.getOriginalFilename(), file.getInputStream());
            journalDao.save(journal);
            return new ResponseDto(200, "上传回单成功", journal);
        } catch (Exception e) {
            return new ResponseDto(3, "上传回单失败", null);
        }
    }

    /**
     * 上传回单到oss
     */
    public String uploadReceiptToOss(String fileName, InputStream ins) {
        String receiptUrl;
        try {
            String objectName = OSSUtils.uploadIcbcPdf(fileName, ins);
            receiptUrl = BASE_FILE_URL + objectName;
        } catch (Exception e) {
            throw new ServiceException("获取文件失败", ErrorCode.BAD_REQUEST);
        }
        return receiptUrl;
    }

    /**
     * 中国银行回单导入 一页一个、一页两个
     */
    public ArrayList<ReceiptDto> transferPdfToExcel(String fileName, InputStream inputStream, String account) {
        try {
            PDDocument document = Loader.loadPDF(new RandomAccessReadBuffer(inputStream));
            List<PDDocument> documents = split(document);
            // 根据分割的单个获取文件内容
            ArrayList<ReceiptDto> receipts = new ArrayList<>();
            for (int i = 0; i < documents.size(); i++) {
                // 获取数据集合
                PDFTextStripper textStripper = new PDFTextStripper();
                PDDocument pdDocument = documents.get(i);
                String content = textStripper.getText(pdDocument);
                if (StringUtils.contains(content, INFO_NONE)) {
					throw new ServiceException("回单内容为空", ErrorCode.BAD_REQUEST);
                }
                int count = 0;
                for (String variety : BOC_VARIETY) {
                    if (StringUtils.contains(content, variety)) {
                        int countContent = StringUtils.countMatches(content, variety);
                        if (countContent > 1) {
                            count += countContent;
                        } else {
                            count++;
                        }
                    }
                }
                String[] receiptsSplit = new String[3];
                List<InputStream> inputStreams = null;
                if (count != 1) {
                    // 拆分成单个回单
                    receiptsSplit = content.split(BOC_SPLIT_FLAG);
                    inputStreams = pdfTransferImage(pdDocument, 2);
                } else {
                    receiptsSplit[0] = content;
                }
                String separator = System.lineSeparator();
                for (int j = 0; j < receiptsSplit.length; j++) {
                    String contentSplit = receiptsSplit[j];
                    if (StringUtils.isEmpty(contentSplit) || StringUtils.isEmpty(contentSplit.replace(separator, "").trim())) {
                        continue;
                    }
                    ReceiptDto receiptDto = new ReceiptDto();
                    // 上传oss
                    if (count == 2) {
                        receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + (j + 1) + ".pdf", inputStreams.get(j));
                    } else {
                        ByteArrayOutputStream baos = new ByteArrayOutputStream();
                        pdDocument.save(baos);
                        byte[] byteArray = baos.toByteArray();
                        InputStream ins = new ByteArrayInputStream(byteArray);
                        receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + (j + 1) + ".pdf", ins);
                    }
                    String[] receiptDetails = contentSplit.split(separator);
					for (int i1 = 0; i1 < receiptDetails.length; i1++)  {
						String temp = receiptDetails[i1];
                        // 国际结算贷记通知
                        if (StringUtils.contains(content, BOC_TYPE_FLAG)) {
                            if (StringUtils.contains(temp, BOC_DATE)) {
                                receiptDto.payAccount = StringUtils.substringBetween(temp, BOC_CLIENT_NO, BOC_DATE).trim();
                                receiptDto.tradingDate = StringUtils.substringAfter(temp, BOC_DATE).replace(separator, "").trim();
                                receiptDto.tradingDate = getDateStr(receiptDto.tradingDate);
                            }
                            if (StringUtils.contains(temp, BOC_ACCEPTER)) {
                                receiptDto.acceptName = StringUtils.substringBefore(temp, BOC_ACCEPTER).trim();
                            }
                            if (StringUtils.contains(temp, BOC_ACCEPTER_ACCOUNT)) {
                                receiptDto.acceptAccount = StringUtils.substringAfter(temp, BOC_ACCEPTER_ACCOUNT).replace(separator, "").trim();
                            }
                            if (StringUtils.contains(temp, BOC_PAYER)) {
                                receiptDto.payName = StringUtils.substringAfter(temp, BOC_PAYER).replace(separator, "").trim();
                            }
                            if (StringUtils.contains(temp, SHBANK_CONTENT) && StringUtils.contains(temp, BOC_MONEY)) {
                                receiptDto.money = StringUtils.substringBetween(temp, SHBANK_CONTENT, BOC_MONEY).trim();
                            }
                            if (StringUtils.contains(temp, ABC_RECEIPT_NO)) {
                                receiptDto.tradeNo = StringUtils.substringBetween(temp, ABC_RECEIPT_NO, " ").trim();
                            }
                        } else {
                            // 银行账号
                            if (StringUtils.contains(temp, "收款人账号") && StringUtils.contains(temp, "付款人账号")) {
                                // 考虑位置调换
                                String str = StringUtils.substringBefore(temp, " ");
                                if (StringUtils.contains(str, "收款人账号")) {
                                    receiptDto.acceptAccount = StringUtils.remove(str, "收款人账号：");
                                    receiptDto.payAccount =
                                        StringUtils.substringAfter(temp, "付款人账号：").replace("\r", "");
                                }
                                if (StringUtils.contains(str, "付款人账号")) {
                                    receiptDto.payAccount = StringUtils.remove(str, "付款人账号：");
                                    receiptDto.acceptAccount =
                                        StringUtils.substringAfter(temp, "收款人账号：").replace("\r", "");
                                }
                            } else if (StringUtils.contains(temp, "收款人账号") && !StringUtils.contains(temp, "原")) {
                                receiptDto.acceptAccount = StringUtils.remove(temp, "收款人账号：").replace("\r", "");
                            } else if (StringUtils.contains(temp, "付款人账号")) {
                                receiptDto.payAccount = StringUtils.remove(temp, "付款人账号：").replace("\r", "");
                            }
                            if (StringUtils.contains(temp, "付款人名称") && StringUtils.contains(temp, RECEIVER_NAME)) {
                                if (StringUtils.contains(contentSplit, BOC_NAME_FLAG) || StringUtils.contains(contentSplit, BOC_NAME_FLAG_FORTH)) {
									String flag = StringUtils.substringBetween(contentSplit,BCM_PAY_ACCOUNT,BCM_PAY_NAME);
									if(StringUtils.contains(flag, BOC_NAME_FLAG_TWO+separator+"司")){
										receiptDto.payName = StringUtils.substringBefore(temp, BCM_ACCEPT_NAME);
										receiptDto.acceptName = receiptDetails[i1-2].replace(separator, "")+receiptDetails[i1-1].replace(separator, "");
										if(receiptDto.payName.isEmpty()){
											receiptDto.payName = receiptDetails[i1-2].replace(separator, "")+receiptDetails[i1-1].replace(separator, "");
											receiptDto.acceptName = receiptDetails[i1-4].replace(separator, "")+receiptDetails[i1-3].replace(separator, "");
										}
									}else{
										receiptDto.payName = StringUtils.substringBetween(temp, " ", RECEIVER_NAME);
										receiptDto.acceptName = StringUtils.substringBefore(temp, " ");
									}
								}else if(StringUtils.contains(contentSplit, BOC_NAME_FLAG_THREE)|| StringUtils.contains(contentSplit, BOC_NAME_FLAG_FIVE)){
									String flag = StringUtils.substringBetween(contentSplit,BCM_ACCEPT_ACCOUNT,BCM_ACCEPT_NAME);
									if(StringUtils.contains(flag, BOC_NAME_FLAG_TWO+separator+"司")){
										receiptDto.acceptName = StringUtils.substringBefore(temp, BCM_PAY_NAME);
										receiptDto.payName = receiptDetails[i1-2].replace(separator, "")+receiptDetails[i1-1].replace(separator, "");
									}else{
										receiptDto.acceptName = StringUtils.substringBetween(temp, " ", PAY_NAME);
										receiptDto.payName = StringUtils.substringBefore(temp, " ");
									}
								}
                            } else if (StringUtils.contains(temp, "付款人名称") && !StringUtils.contains(temp, "收款人名称")&& !StringUtils.contains(temp, "原")) {
                                receiptDto.payName = StringUtils.remove(temp, "付款人名称：");
                            } else if (!StringUtils.contains(temp, "付款人名称") && StringUtils.contains(temp, "收款人名称") && !StringUtils.contains(temp, "原")) {
                                receiptDto.acceptName = StringUtils.remove(temp, "收款人名称：");
                            }
                            if (StringUtils.contains(receiptDto.acceptName, "\r")) {
                                receiptDto.acceptName = receiptDto.acceptName.replace("\r", "").trim();
                            }
                            if (StringUtils.contains(receiptDto.payName, "\r")) {
                                receiptDto.payName = receiptDto.payName.replace("\r", "").trim();
                            }
                            if (StringUtils.contains(temp, MONEY) && StringUtils.contains(temp, MONEY_FLAG)) {
                                receiptDto.money = StringUtils.remove(StringUtils.substringAfter(temp, "："), "CNY").replace("\r", "");
                            }
							if (StringUtils.contains(temp, MONEY_FLAG_FORTH)) {
								receiptDto.money = StringUtils.substringAfter(temp, MONEY_FLAG_FORTH).replace("\r", "");
							}
                            if (StringUtils.contains(temp, BCM_MONEY) && StringUtils.contains(temp, MONEY_FLAG_TWO)
                                && !StringUtils.contains(temp, MONEY_FLAG_THREE) && !StringUtils.contains(temp, BOC_MONEY_FLAG)&& !StringUtils.contains(temp, BOC_MONEY_FLAG_TWO)) {
                                receiptDto.money = StringUtils.substringAfter(temp, MONEY_FLAG_TWO).replace(separator, "").trim();
                            }
                            if (StringUtils.contains(temp, USER_WAY)) {
                                receiptDto.userWay = StringUtils.remove(temp, "用途：");
                                if (StringUtils.contains(receiptDto.userWay, "\r")) {
                                    receiptDto.userWay = receiptDto.userWay.replace("\r", "").trim();
                                }
                            }
                            if (StringUtils.contains(temp, POST_SCRIPT)) {
                                receiptDto.postScript = StringUtils.remove(temp, "附言：");
                                if (StringUtils.contains(receiptDto.postScript, "\r")) {
                                    receiptDto.postScript = receiptDto.postScript.replace("\r", "").trim();
                                }
                            }
                            if (StringUtils.contains(temp, SUMMARY)) {
                                receiptDto.summary = StringUtils.remove(temp, "备注：");
                                if (StringUtils.contains(receiptDto.summary, "\r")) {
                                    receiptDto.summary = receiptDto.summary.replace("\r", "").trim();
                                }
                            }
                            if (StringUtils.contains(temp, FEE_NAME)) {
                                receiptDto.feeName = StringUtils.remove(temp, "费用名称：");
                            }
                            if (!StringUtils.isEmpty(receiptDto.feeName)) {
                                receiptDto.digest = receiptDto.feeName;
                            } else if (!StringUtils.isEmpty(receiptDto.summary)) {
                                receiptDto.digest = receiptDto.summary;
                            } else if (!StringUtils.isEmpty(receiptDto.userWay)) {
                                receiptDto.digest = receiptDto.userWay;
                            } else if (!StringUtils.isEmpty(receiptDto.postScript)) {
                                receiptDto.digest = receiptDto.postScript;
                            }
                            // 交易流水号
                            if (StringUtils.contains(temp, TRADE_NO) && StringUtils.contains(temp, TRADE_NO_FLAG)) {
                                receiptDto.tradeNo = StringUtils
                                    .remove(StringUtils.substringBetween(temp, "交易流水号：", TRADE_NO_FLAG).trim(), "-");
                            }
                        }
                    }
					if(StringUtils.contains(receiptDto.payName, "_")){
						receiptDto.payName = receiptDto.payName.replace("_", "-");
					}
                    // 对方户名
                    if (StringUtils.equals(account, receiptDto.acceptAccount)) {
                        receiptDto.oppositeAccountName = receiptDto.payName;
                    } else if (StringUtils.equals(account, receiptDto.payAccount)) {
                        receiptDto.oppositeAccountName = receiptDto.acceptName;
                        receiptDto.money = "-" + receiptDto.money;
                    }
                    // 交易日期
                    Pattern pattern = Pattern.compile("日期：" + PERIOD_CHAR);
                    Matcher matcher = pattern.matcher(content);
                    while (matcher.find()) {
                        try {
                            String period = StringUtils.remove(matcher.group(), "日期：").trim();
                            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy年MM月dd日");
                            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyyMMdd");
                            Date date = inputFormat.parse(period);
                            receiptDto.tradingDate = outputFormat.format(date);
                        } catch (Exception e) {
                            throw new ServiceException("日期格式错误", ErrorCode.BAD_REQUEST);
                        }
                    }
                    receipts.add(receiptDto);
                }
            }
			document.close();
            inputStream.close();
            return receipts;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage(), ErrorCode.BAD_REQUEST);
        }
    }

    /**
     * 一个pdf文件按页分割成多个pdf文件，一页一个回单情况
     */
    public List<PDDocument> split(PDDocument document) {
        try {
            Splitter splitter = new Splitter();
            splitter.setStartPage(1);
            splitter.setEndPage(document.getNumberOfPages());
            return splitter.split(document);
        } catch (Exception e) {
            throw new ServiceException("pdf分割失败！", ErrorCode.BAD_REQUEST);
        }
    }

    /**
     * 中国银行接口导入
     */
    public ResponseDto importBocJournal(String period, String accountBookIdStr, String openingBank, String exchangeRate, ArrayList<ReceiptDto> receipts) {
        long accountBookId = Long.parseLong(accountBookIdStr);
        DBCache cache = new DBCache();
        cache.accountBook = accountBookDao.findOne(accountBookId);
        cache.vrs = voucherRecordDao.findByAccountBookIdOrderByIdDesc(accountBookId);
        cache.subjects = subjectDao.findByAccountBookId(accountBookId);
        cache.dimItems = dimItemDao.findByAccountBookId(accountBookId);
        cache.dimCustomers = dimCustomerDao.findByAccountBookId(accountBookId);
        cache.dimProviders = dimProviderDao.findByAccountBookId(accountBookId);
        cache.dimDepartments = dimDepartmentDao.findByAccountBookId(accountBookId);
        cache.dimEmployees = dimEmployeeDao.findByAccountBookId(accountBookId);
        cache.dimInventories = dimInventoryDao.findByAccountBookId(accountBookId);
        cache.voucherTemplates = voucherTemplateDao.findByAccountBookId(accountBookId);
        cache.relations = subjectDimRelationDao.findByAccountBookId(accountBookId);
        cache.journals = journalDao.findByAccountBookIdOrderByIdDesc(accountBookId);
        cache.cashFlowItems = cashFlowItemDao.findByAccountBookId(accountBookId);
        cache.vrs = voucherRecordDao.findByAccountBookIdOrderByIdDesc(accountBookId);
        cache.systemSettings = systemSettingDao.findByAccountBookId(accountBookId);
        List<JournalDto> journalDtoList = new ArrayList<>();
        for (ReceiptDto receiptDto : receipts) {
            JournalDto journalDto = new JournalDto();
            journalDto.accountBookId = accountBookIdStr;
            journalDto.period = period;
            journalDto.journalDate = receiptDto.tradingDate;
            journalDto.oppositeAccountName = receiptDto.oppositeAccountName;
            journalDto.summary = receiptDto.digest;
            journalDto.receiptNo = receiptDto.tradeNo;
            journalDto.receiptUrl = receiptDto.receiptUrl;
            if (StringUtils.contains(receiptDto.money, "\r")) {
                receiptDto.money = receiptDto.money.replace("\r", "");
            }
            boolean isTwo = false;
            if (StringUtils.contains(receiptDto.money, "--")) {
                isTwo = true;
                receiptDto.money = receiptDto.money.replace("--", "-");
            }
            BigDecimal amount = getDecimalValue(receiptDto.money);
            if (StringUtils.contains(receiptDto.money, "-")) {
                journalDto.pay = DECIMAL_FORMAT_2.format(amount);
                if (StringUtils.contains(journalDto.pay, "-") && !isTwo) {
                    journalDto.pay = journalDto.pay.replace("-", "");
                }
            } else {
                journalDto.income = DECIMAL_FORMAT_2.format(amount);
                if (StringUtils.contains(journalDto.income, "-")) {
                    journalDto.income = journalDto.income.replace("-", "");
                }
            }
            journalDto.incomePayType = getIncomePayType(cache, journalDto, null, null);
            journalDto.journalType = "0";
            if (StringUtils.isNotBlank(openingBank)) {
                journalDto.openingBank = openingBank;
            }
			if (StringUtils.isNotBlank(exchangeRate)) {
				journalDto.exchangeRate = exchangeRate;
			}
            journalDtoList.add(journalDto);
        }
        int countJournal = importJournal(cache, journalDtoList);
        String message = "本次成功导入" + countJournal + "条日记账。";
        return new ResponseDto(200, "导入成功！", message);
    }

	/**
	 * 中国建设银行 pdf转Excel 默认一页三个
	 */
	public ArrayList<ReceiptDto> transferCCBPdfToExcel(String fileName, InputStream inputStream, String account) {
		try {
			PDDocument document = Loader.loadPDF(new RandomAccessReadBuffer(inputStream));
			List<PDDocument> documents = split(document);
			// 根据分割的单个获取文件内容
			ArrayList<ReceiptDto> receiptListAll = new ArrayList<>();
			String lineSeparator = System.lineSeparator();
			for (PDDocument pdDocument : documents) {
				// 一个文档三个回单，分割成单独的三个文件流
				PDFTextStripper textStripper = new PDFTextStripper();
				String content = textStripper.getText(pdDocument);
				if(content.isEmpty()||StringUtils.equals(content, lineSeparator)){
					continue;
				}
                int count = StringUtils.countMatches(content, CCB_FLAG);
                List<InputStream> inputStreams = null;
                InputStream ins = null;
                if (count != 1 && count != 0) {
                    inputStreams = pdfTransferImage(pdDocument, 3);
                } else {
                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    pdDocument.save(baos);
                    byte[] byteArray = baos.toByteArray();
                    ins = new ByteArrayInputStream(byteArray);
                }
                String[] receiptTemps = new String[4];
                if (count != 0) {
                    receiptTemps = content.split(CCB_FLAG);
                } else {
                    receiptTemps[0] = content;
                }
				ArrayList<ReceiptDto> receiptList = new ArrayList<>();
                for (int j = 0; j < receiptTemps.length; j++) {
					ReceiptDto receiptDto = new ReceiptDto();
					String receipt = receiptTemps[j];
                    if (receipt == null || !StringUtils.contains(receipt, CCB_NO)) {
                        continue;
                    }
					// 上传oss
                    if (count != 1 && count != 0) {
						if(j!= 3){
							receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + j + ".pdf", inputStreams.get(j));
						}
                    } else {
                        receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + (j + 1) + ".pdf", ins);
                    }
					String[] receiptItems = receipt.split("\n");
                    boolean isOut = false;
					boolean isIn = false;
                    for (int i = 0; i < receiptItems.length; i++) {
                        String receiptItem = receiptItems[i];
                        if (StringUtils.contains(receipt, CCB_FLAG_TWO)) {
                            receiptDto.digest = CCB_FLAG_DIGEST;
                            if (StringUtils.contains(receiptItem, CCB_ZH_DATE)) {
                                receiptDto.tradingDate =
                                    StringUtils.substringAfter(StringUtils.substringAfter(receiptItem, CCB_ZH_DATE),
                                        CCB_ZH_DATE_TWO).replace(lineSeparator, "").replace(" ", "").trim();
                            }
                            if (StringUtils.contains(receiptItem, CCB_PAY_NAME)) {
                                receiptDto.payName =
                                    StringUtils.substringBetween(receiptItem, CCB_PAY_NAME, " ").trim();
                            }
                            if (StringUtils.contains(receiptItem, JSB_PAYER_ACCOUNT_TWO)) {
                                receiptDto.payAccount =
                                    StringUtils.substringBetween(receiptItem, JSB_PAYER_ACCOUNT_TWO, " ").trim();
                            }
                            if (StringUtils.contains(receiptItem, CCB_FLAG_MONEY)) {
                                receiptDto.money =
                                    StringUtils.substringBetween(receiptItem, CCB_FLAG_MONEY, " ").trim();
                                if (StringUtils.equals(receiptDto.payAccount, account)) {
                                    receiptDto.money = "-" + receiptDto.money;
                                }
                                receiptDto.tradeNo = StringUtils.substringAfter(receiptItem, CCB_NO_TWO)
                                    .replace(lineSeparator, "").trim();
                            }
                        } else {
                            if (!StringUtils.contains(receipt,
                                lineSeparator + "付" + lineSeparator + "款" + lineSeparator + "人")) {
                                // 业务类型 手续费
                                if (StringUtils.contains(receiptItem, "对公")
                                    || StringUtils.contains(receiptItem, "年费")) {
                                    isOut = true;
                                }
                                if (StringUtils.contains(receiptItem, CCB_MONEY_FLAG)) {
                                    if (StringUtils.contains(receiptItems[i + 1], "手续费")
                                        || StringUtils.contains(receiptItems[i + 1], "对公")) {
                                        isOut = true;
                                    }
                                    if (StringUtils.contains(receiptItem, CCB_MONEY_FLAG_TWO)
                                        || StringUtils.contains(receiptItem, CCB_MONEY_FLAG_THREE)
                                        || StringUtils.contains(receiptItem, CCB_MONEY_FLAG_FOUR)) {
                                        isOut = true;
                                    }
                                }
                                if (StringUtils.contains(receiptItem, CCB_NAME_FLAG)) {
                                    if (StringUtils.contains(receiptItems[i + 1], "利息")) {
                                        isIn = true;
                                    }
                                }
                                // 户名 账号 没有收款人和付款人这种回单
                                if (StringUtils.contains(receiptItem, CCB_HM)) {
                                    receiptDto.accountName =
                                        StringUtils.substringBetween(receiptItem, CCB_HM, CCB_ZH).trim();
                                    receiptDto.account =
                                        StringUtils.substringAfter(receiptItem, CCB_ZH).replace("\r", "").trim();
                                }
                                if (StringUtils.contains(receiptItem, CCB_PAY_NAME)) {
                                    receiptDto.payName = StringUtils.substringBetween(receiptItem, CCB_PAY_NAME, " ");
                                }
                                if (StringUtils.contains(receiptItem, CCB_PAY_ACCOUNT)) {
                                    receiptDto.payAccount =
                                        StringUtils.substringBetween(receiptItem, CCB_PAY_ACCOUNT, " ").trim();
                                }
                                // 交易金额
                                if (StringUtils.contains(receiptItem, CCB_MONEY)) {
                                    receiptDto.money =
                                        StringUtils.substringAfter(receiptItem, CCB_MONEY).replace("\r", "");
                                    if (receiptDto.money.length() > 10) {
                                        receiptDto.money = StringUtils.substringBefore(receiptDto.money, " ");
                                        if (StringUtils.equals(account, receiptDto.payAccount)) {
                                            receiptDto.money = "-" + receiptDto.money;
                                        }
                                    }
                                }
                                // 交易流水号
                                if (StringUtils.contains(receiptItem, CCB_NO_TWO)) {
                                    receiptDto.tradeNo =
                                        StringUtils.substringAfter(receiptItem, CCB_NO_TWO).replace("\r", "");
                                } else if (StringUtils.contains(receiptItem, CCB_NO)
                                    && StringUtils.contains(receiptItem, CCB_DATE)) {
                                    receiptDto.tradeNo = StringUtils.substringBetween(receiptItem, CCB_NO, CCB_DATE);
                                } else if (StringUtils.contains(receiptItem, CCB_NO)
                                    && !StringUtils.contains(receiptItem, CCB_DATE)
                                    && !StringUtils.contains(receiptItem, "缴款")) {
                                    receiptDto.tradeNo = StringUtils.substringAfter(receiptItem, CCB_NO)
                                        .replace(lineSeparator, "").trim();
                                }
                            } else {
                                // 收款人名称 付款人名称
                                if (StringUtils.contains(receiptItem, CCB_RECEIPT_NAME)
                                    && StringUtils.contains(receiptItem, "收")) {
                                    receiptDto.payName =
                                        StringUtils.substringBetween(receiptItem, CCB_RECEIPT_NAME, "收").trim();
                                } else if (StringUtils.contains(receiptItem, CCB_RECEIPT_NAME)) {
                                    receiptDto.acceptName =
                                        StringUtils.substringAfter(receiptItem, CCB_RECEIPT_NAME).trim();
                                }
								if (StringUtils.contains(receiptItem, SZB_NAME) && StringUtils.contains(receiptItems[i-3], "付")) {
									receiptDto.payName = StringUtils.substringAfter(receiptItem, SZB_NAME).replace(lineSeparator,"").trim();
								} else if (StringUtils.contains(receiptItem, SZB_NAME)&& StringUtils.contains(receiptItems[i-3], "收")) {
									receiptDto.acceptName = StringUtils.substringAfter(receiptItem, SZB_NAME).trim();
								}
                                // 付款人账号 收款人账号 对方户名
                                if (StringUtils.contains(receiptItem, CCB_ACCEPT_NAME)) {
                                    int endIndex = receiptItem.lastIndexOf(CCB_ACCEPT_NAME);
                                    int firstIndex = receiptItem.indexOf(CCB_ACCEPT_NAME);
                                    receiptDto.payAccount =
                                        receiptItem.substring(firstIndex, endIndex).replace(CCB_ACCEPT_NAME, "").trim();
                                    receiptDto.acceptAccount = receiptItem.substring(endIndex)
                                        .replace(CCB_ACCEPT_NAME, "").trim().replace("\r", "");
                                    if (StringUtils.equals(account, receiptDto.payAccount)) {
                                        receiptDto.oppositeAccountName = receiptDto.acceptName;
                                    } else {
                                        receiptDto.oppositeAccountName = receiptDto.payName;
                                    }
                                }
								if (StringUtils.contains(receiptItem, SZB_ACCOUNT)) {
									int endIndex = receiptItem.lastIndexOf(SZB_ACCOUNT);
									int firstIndex = receiptItem.indexOf(SZB_ACCOUNT);
									receiptDto.payAccount = receiptItem.substring(firstIndex, endIndex).replace(SZB_ACCOUNT, "").trim();
									receiptDto.acceptAccount = receiptItem.substring(endIndex).replace(SZB_ACCOUNT, "").trim().replace("\r", "");
									if (StringUtils.equals(account, receiptDto.payAccount)) {
										receiptDto.oppositeAccountName = receiptDto.acceptName;
									} else {
										receiptDto.oppositeAccountName = receiptDto.payName;
									}
								}
                                // 交易金额
                                if (StringUtils.contains(receiptItem, CCB_MONEY)
                                    && StringUtils.contains(receiptItem, CCB_MONEY_FLAG_FIVE)||StringUtils.contains(receiptItem, SZB_MONEY)) {
                                    receiptDto.money = StringUtils.substringAfter(receiptItem, CCB_MONEY).replace("\r", "");
                                    if (StringUtils.equals(account, receiptDto.payAccount)) {
                                        receiptDto.money = "-" + receiptDto.money;
                                    }
                                }
                                if (StringUtils.contains(receiptItem, CCB_MONEY_TWO)
                                    && StringUtils.contains(receiptItem, CCB_MONEY_FLAG_FIVE)
                                    && !StringUtils.contains(receiptItem, CCB_MONEY)) {
                                    receiptDto.money = StringUtils.substringAfter(receiptItem, CCB_MONEY_FLAG_FIVE).replace("\r", "");
                                    if (StringUtils.equals(account, receiptDto.payAccount)) {
                                        receiptDto.money = "-" + receiptDto.money;
                                    }
                                }
                                // 交易流水号
                                if (StringUtils.contains(receiptItem, CCB_NO)
                                    && StringUtils.contains(receiptItem, CCB_BB)
                                    && StringUtils.contains(receiptItem, CCB_DATE)) {
                                    receiptDto.tradeNo =
                                        StringUtils.substringBetween(receiptItem, CCB_NO, CCB_DATE).trim();
                                }
                                if (StringUtils.contains(receiptItem, CCB_NO)
                                    && StringUtils.contains(receiptItem, CCB_BB)
                                    && !StringUtils.contains(receiptItem, CCB_DATE)) {
                                    receiptDto.tradeNo = StringUtils.substringAfter(receiptItem, CCB_NO)
                                        .replace(lineSeparator, "").trim();
                                }
                            }
                            // 交易日期
                            if (StringUtils.contains(receiptItem, CCB_DATE) && StringUtils.contains(receiptItem, "币")) {
                                receiptDto.tradingDate =
                                    StringUtils.substringBetween(receiptItem, "日", "币").replace(" ", "");
                            } else if (StringUtils.contains(receiptItem, CCB_ZH_DATE)) {
                                receiptDto.tradingDate = StringUtils.substringAfter(receiptItem, CCB_DATE)
                                    .replace("\r", "").replace(" ", "");
                            } else if (StringUtils.contains(receiptItem, CCB_NO)
                                && StringUtils.contains(receiptItem, CCB_BB)) {
                                receiptDto.tradingDate = StringUtils.substringBetween(receiptItem, " ", CCB_NO).trim();
                                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy年MM月dd日");
                                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyyMMdd");
                                Date date = inputFormat.parse(receiptDto.tradingDate);
                                receiptDto.tradingDate = outputFormat.format(date);
                            }
                            // 摘要
                            if (StringUtils.contains(receiptItem, CCB_DIGEST)) {
                                receiptDto.digest = StringUtils.remove(receiptItem, CCB_DIGEST).replace("\r", "");
                            }
                            // 用途
                            if (StringUtils.contains(receiptItem, CCB_USE_WAY)) {
                                int lastSpaceIndex = StringUtils.lastIndexOf(receiptItem, " ");
                                receiptDto.userWay = receiptItem.substring(lastSpaceIndex).replace("\r", "");
                            }
                            if (StringUtils.isEmpty(receiptDto.digest)) {
                                receiptDto.digest = receiptDto.userWay;
                            }
                            // 附言
                            if (StringUtils.contains(receiptItem, CCB_FY)) {
                                receiptDto.postScript = StringUtils.remove(receiptItem, CCB_FY).replace("\r", "");
                            }
                            // 备注
                            if (StringUtils.contains(receiptItem, CCB_SUMMARY)) {
                                receiptDto.summary = StringUtils.remove(receiptItem, CCB_SUMMARY).replace("\r", "");
                            }
						}
					}
                    if (isOut) {
                        receiptDto.payAccount = receiptDto.account;
                        receiptDto.payName = receiptDto.accountName;
                        if (StringUtils.equals(account, receiptDto.payAccount)) {
                            receiptDto.money = "-" + receiptDto.money;
                        }
                    }
					if(isIn){
						receiptDto.acceptAccount = receiptDto.account;
						receiptDto.acceptName = receiptDto.accountName;
					}
					receiptList.add(receiptDto);
				}
				receiptListAll.addAll(receiptList);
			}
			document.close();
			inputStream.close();
			return receiptListAll;
		} catch (Exception e) {
            e.printStackTrace();
			throw new ServiceException("pdf转换Excel错误", ErrorCode.BAD_REQUEST);
		}
	}


	/**
     * 中国农业银行pdf转excel 一页三个回单
     */
	public ArrayList<ReceiptDto> transferABCPdfToExcel(String fileName, InputStream inputStream, String account) {
		try {
			PDDocument document = Loader.loadPDF(new RandomAccessReadBuffer(inputStream));
			List<PDDocument> documents = split(document);
            // 根据分割的单个获取文件内容
            ArrayList<ReceiptDto> receiptListAll = new ArrayList<>();
			for (PDDocument pdDocument : documents) {
                // 一个文档三个回单，分割成单独的三个文件流
				PDFTextStripper textStripper = new PDFTextStripper();
				String content = textStripper.getText(pdDocument);
                List<InputStream> inputStreams = pdfTransferImage(pdDocument, 3);
                String[] receiptTemps = content.split(ABC_FLAG);
                ArrayList<ReceiptDto> receiptList = new ArrayList<>();
                String separator = System.lineSeparator();
                for (int j = 1; j < receiptTemps.length; j++) {
                    ReceiptDto receiptDto = new ReceiptDto();
                    String receipt = receiptTemps[j];
                    // 处理户名
                    if (StringUtils.contains(receipt, ABC_HM)) {
                        int firstIndex = receipt.indexOf(ABC_HM);
						int secondIndex = receipt.indexOf(ABC_HM, firstIndex + 1);
                        int flagIndex = receipt.indexOf("开户行");
                        receiptDto.payName = receipt.substring(firstIndex + ABC_HM.length(), secondIndex).replace(separator, "").trim();
                        receiptDto.acceptName = receipt.substring(secondIndex + ABC_HM.length(), flagIndex).replace(separator, "").trim();
                        if (StringUtils.equals(PSBC_PAYER, receiptDto.acceptName)) {
                            receiptDto.acceptName = "";
                        }
						if (StringUtils.contains(receiptDto.acceptName, PSBC_PAYER)) {
							receiptDto.acceptName = receiptDto.acceptName.replace(PSBC_PAYER,"");
						}
                    }
                    // 上传oss
					receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + j + ".pdf", inputStreams.get(j-1));
                    String[] receiptItems = receipt.split("\n");
					for (int i = 0; i < receiptItems.length; i++)  {
						String receiptItem = receiptItems[i];
                        if (StringUtils.contains(receiptItem, ABC_RECEIPT_NO) ) {
							if(StringUtils.contains(receiptItems[i+1], "账号")){
								// 付款人账号
								int lastzhIndex = receiptItems[i+1].lastIndexOf(ABC_ZH);
								receiptDto.payAccount = StringUtils.remove(StringUtils.substring(receiptItems[i+1], 0, lastzhIndex), ABC_ZH).trim();
								// 收款人账号
								receiptDto.acceptAccount = StringUtils.remove(StringUtils.substring(receiptItems[i+1], lastzhIndex, receiptItem.length()), ABC_ZH).trim();
							}
							if(StringUtils.contains(receiptItems[i+1], PSBC_PAYER)){
								receiptDto.payAccount = StringUtils.substringAfter(receiptItems[i+2], ABC_ZH).replace(separator, "").trim();
								receiptDto.acceptAccount = StringUtils.substringAfter(receiptItems[i+4], ABC_ZH).replace(separator, "").trim();
							}
						}
                        // 交易金额
                        if (StringUtils.contains(receiptItem, ABC_MONEY_X)) {
                            receiptDto.money = StringUtils.substringBetween(receiptItem, ABC_MONEY_X, ABC_MONEY_D).trim();
                            if (StringUtils.equals(receiptDto.payAccount, account)) {
                                receiptDto.money = "-" + receiptDto.money;
                            }
                        }
                        // 摘要
                        if (StringUtils.contains(receiptItem, ABC_DIGEST)) {
                            receiptDto.digest = StringUtils.substringBetween(receiptItem, ABC_DIGEST, ABC_DIGEST_HELP).trim();
                        }
                        // 交易日期
                        if (StringUtils.contains(receiptItem, ABC_DATE)) {
                            receiptDto.tradingDate = StringUtils.substringAfter(receiptItem, ABC_DATE).replace("\r", "").trim();
                        }
                        // 交易流水号
                        if (StringUtils.contains(receiptItem, ABC_RECEIPT_NO)) {
                            receiptDto.tradeNo = StringUtils.substringBetween(receiptItem, ABC_RECEIPT_NO, " ").trim();
                        }
                    }
                    if (StringUtils.equals(receiptDto.payAccount, account)) {
                        receiptDto.oppositeAccountName = receiptDto.acceptName;
                    } else {
                        receiptDto.oppositeAccountName = receiptDto.payName;
                    }
                    receiptList.add(receiptDto);
                }
                receiptListAll.addAll(receiptList);
			}
			document.close();
            inputStream.close();
            return receiptListAll;
		} catch (Exception e) {
			throw new ServiceException("pdf转换Excel错误", ErrorCode.BAD_REQUEST);
		}
	}

    /**
     * 银企互联导入流水:所有银行都导入回单
     */
    public ResponseDto importYqhlJournal(String accountBookIdStr, String period, Integer type, String openingBank, String exchangeRate) {
        BankTask bankTask = bankTaskDao.findByAccountBookIdAndPeriodAndBankType(Long.parseLong(accountBookIdStr), period, type);
            // 导入回单
            if (type != HZCB) {
                return importChinaBankReceipt(null, bankTask.receiptResultUrl, String.valueOf(type), accountBookIdStr,
                    period, openingBank, exchangeRate, bankTask.account);
            } else {
                return importChinaBankReceipt(null, bankTask.journalResultUrl, String.valueOf(type), accountBookIdStr,
                    period, openingBank, exchangeRate, bankTask.account);
            }
        }

	/**
	 * 匹配中信银行格式 整体是个压缩包，一个pdf文件就一个回单的情况
	 */
    public ReceiptDto transferCITICPdfToExcel(InputStream inputStream, String account) {
		try{
			PDDocument document = Loader.loadPDF(new RandomAccessReadBuffer(inputStream));
			PDFTextStripper pdfTextStripper = new PDFTextStripper();
			String content = pdfTextStripper.getText(document);
			String separator = System.lineSeparator();
            String[] splits = content.split(separator);
            ReceiptDto receiptDto = new ReceiptDto();
            for (String split : splits) {
                if (!StringUtils.contains(content, CITIC_FLAG)) {
                    if (StringUtils.contains(split, CITIC_RECEIPT_DATE)) {
                        receiptDto.tradingDate = StringUtils.substringAfter(split, CITIC_RECEIPT_DATE).replace("\r", "").trim();
                    }
                    if (StringUtils.contains(split, CITIC_HM) && StringUtils.contains(split, CITIC_HM_FLAGT)) {
                        receiptDto.accountName = StringUtils.substringBetween(split, CITIC_HM, CITIC_HM_FLAGT).trim();
                    }else if(StringUtils.contains(split, CITIC_HM) && StringUtils.contains(split, CITIC_HM_FLAG)){
						receiptDto.accountName = StringUtils.substringBetween(split, CITIC_HM, CITIC_HM_FLAG).trim();
					}
                    if (StringUtils.contains(split, CITIC_ACCOUNT)) {
                        receiptDto.account = StringUtils.substringBetween(split, CITIC_ACCOUNT, " ").trim();
                    }
                    if (StringUtils.contains(split, CITIC_MONEY)) {
                        receiptDto.money = StringUtils.substringAfter(split, CITIC_MONEY).replace("\r", "");
                        if (StringUtils.contains(content, CITIC_FEE_RECEIPT) || StringUtils.contains(content, CITIC_MONEY_FLAG)) {
                            receiptDto.money = "-" + receiptDto.money;
                        }
                    }
                    if (StringUtils.contains(split, CITIC_SUMMARY)) {
                        receiptDto.digest = StringUtils.substringAfter(split, " ").replace("\r", "").trim();
                    }
                    if (StringUtils.contains(content, CITIC_FEE_RECEIPT)) {
                        receiptDto.payName = receiptDto.accountName;
                        receiptDto.payAccount = receiptDto.account;
                        receiptDto.digest = "银行收费";
                    } else if (StringUtils.contains(content, CITIC_CK)) {
                        receiptDto.acceptName = receiptDto.accountName;
                        receiptDto.acceptAccount = receiptDto.account;
                    }
                } else {
                    if (StringUtils.contains(content, "付" + separator + "款" + separator + "人" + separator + "名称")) {
                        receiptDto.payName = StringUtils.substringBetween(content, "付" + separator + "款" + separator + "人" + separator + "名称", "收" + separator + "款" + separator + "人" + separator + "名称").replace("\r\n", "").trim();
                        receiptDto.acceptName = StringUtils.substringBetween(content, "收" + separator + "款" + separator + "人" + separator + "名称 ", separator);
                    }
                    if (StringUtils.contains(split, CITIC_ACCOUNT)) {
                        int lastIndex = split.lastIndexOf(CITIC_ACCOUNT);
                        receiptDto.payAccount = StringUtils.remove(StringUtils.substring(split, 0, lastIndex), CITIC_ACCOUNT).trim();
                        receiptDto.acceptAccount = StringUtils.remove(StringUtils.substring(split, lastIndex), CITIC_ACCOUNT).trim();
                    }
                    if (StringUtils.equals(account, receiptDto.payAccount)) {
                        receiptDto.oppositeAccountName = receiptDto.acceptName;
                    } else if (StringUtils.equals(account, receiptDto.acceptAccount)) {
                        receiptDto.oppositeAccountName = receiptDto.payName;
                    }
                    if (StringUtils.contains(split, CITIC_RECEIPT_DATE)) {
                        receiptDto.tradingDate = StringUtils.substringBetween(split, CITIC_RECEIPT_DATE, " ");
                        receiptDto.tradingDate = getDateStr(receiptDto.tradingDate);
                    }
                    if (StringUtils.contains(split, CITIC_MONEY)) {
                        receiptDto.money = StringUtils.substringAfter(split, CITIC_MONEY).replace("\r", "");
                        if (StringUtils.equals(account, receiptDto.payAccount)) {
                            receiptDto.money = "-" + receiptDto.money;
                        }
                    }
                    if (StringUtils.contains(split, CITIC_DIGEST)) {
                        receiptDto.digest = StringUtils.substringAfter(split, CITIC_DIGEST).replace("\r", "");
                    }
                }
                if (StringUtils.contains(split, CITIC_NO)) {
                    receiptDto.tradeNo = StringUtils.substringBetween(split, CITIC_NO, " ");
                }
            }
            return receiptDto;
		}catch(Exception e){
			throw new ServiceException("格式转换错误", ErrorCode.BAD_REQUEST);
		}
	}

    /**
     * 光大银行 多个zip压缩包，单个文件夹下一个pdf,一个pdf中一页一个
     */
    public ArrayList<ReceiptDto> transferCEBPdfToExcel(String fileName, InputStream inputStream, String account) {
        try {
			PDDocument document = Loader.loadPDF(new RandomAccessReadBuffer(inputStream));
            List<PDDocument> documents = split(document);
            ArrayList<ReceiptDto> receiptDtos = new ArrayList<>();
            for (PDDocument pdDocument : documents) {
                PDFTextStripper pdfTextStripper = new PDFTextStripper();
                String content = pdfTextStripper.getText(pdDocument);
                boolean isOut = StringUtils.contains(content, BEC_MONEY_FLAG);
                String separator = System.lineSeparator();
                String[] splits = content.split(separator);
                ReceiptDto receiptDto = new ReceiptDto();
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                pdDocument.save(baos);
                byte[] byteArray = baos.toByteArray();
                InputStream ins = new ByteArrayInputStream(byteArray);
                receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + ".pdf", ins);
                if (StringUtils.contains(content, "付" + separator + "款" + separator + "人" + separator + "收" + separator + "款" + separator + "人账号")) {
                    String temp = StringUtils.substringBetween(content, BEC_DATE, BEC_HM);
                    if (StringUtils.contains(temp, " ")) {
                        int seperatorIndex = findXOccurrenceIndex(temp, separator, 1);
                        int spaceIndex = temp.indexOf(" ");
                        receiptDto.payName = temp.substring(seperatorIndex, spaceIndex).replace(separator, "").trim();
                        receiptDto.acceptName = temp.substring(spaceIndex).replace(separator, "").trim();
                    } else {
                        int firstIndex = findXOccurrenceIndex(temp, separator, 1);
                        int thirdOccurrenceIndex = findXOccurrenceIndex(temp, separator, 3);
                        if (thirdOccurrenceIndex != -1) {
                            receiptDto.payName = temp.substring(firstIndex, thirdOccurrenceIndex).replace(separator, "").trim();
                            receiptDto.acceptName = temp.substring(thirdOccurrenceIndex).replace(separator, "").trim();
                        } else {
                            int secondOccurrenceIndex = findXOccurrenceIndex(temp, separator, 2);
                            receiptDto.payName = temp.substring(firstIndex, secondOccurrenceIndex).replace(separator, "").trim();
                        }
                    }
                }
                for (int i = 0; i < splits.length; i++) {
                    String split = splits[i];
                    if (!StringUtils.contains(content, "付" + separator + "款" + separator + "人" + separator + "收" + separator + "款" + separator + "人账号")) {
                        if (StringUtils.contains(split, BEC_RECEIPT_NO)) {
                            receiptDto.tradeNo = StringUtils.substringBetween(split, BEC_RECEIPT_NO, " ").trim();
                            receiptDto.tradingDate = StringUtils.substringAfter(split, BEC_RECEIPT_DATE).replace("\r", "").trim();
                        }
                        if (StringUtils.contains(split, BEC_ZH) && StringUtils.contains(split, BEC_MONEY)) {
                            receiptDto.account = StringUtils.substringBetween(split, BEC_ZH, " ").trim();
                            receiptDto.money = StringUtils.substringAfter(split, BEC_MONEY).replace("\r", "").trim();
                            if (isOut) {
                                receiptDto.money = "-" + receiptDto.money;
                                receiptDto.payAccount = receiptDto.account;
                            } else {
                                receiptDto.acceptAccount = receiptDto.account;
                            }
                        }
                        if (StringUtils.contains(split, BEC_HM_FLAG)) {
                            receiptDto.accountName = StringUtils.substringAfter(split, BEC_HM_FLAG).replace("\r", "").trim();
                            if (isOut) {
                                receiptDto.payName = receiptDto.accountName;
                            } else {
                                receiptDto.acceptName = receiptDto.accountName;
                            }
                        }
                    } else {
                        if (StringUtils.contains(split, BEC_TRADE_NO)) {
                            receiptDto.tradeNo = StringUtils.substringBetween(split, BEC_TRADE_NO, " ").trim();
                            receiptDto.tradingDate = StringUtils.substringAfter(split, BEC_DATE).replace("\r", "").trim();
                        }
                        if (StringUtils.contains(split, BEC_ACCOUNT)) {
                            receiptDto.payAccount = StringUtils.substringBetween(split, BEC_ACCOUNT, " ").trim();
                            int index = split.lastIndexOf(CITIC_ACCOUNT);
                            receiptDto.acceptAccount = StringUtils.remove(StringUtils.substring(split, index), CITIC_ACCOUNT).replace("\r", "").trim();

                        }
                        if (StringUtils.equals(receiptDto.acceptAccount, account)) {
                            receiptDto.oppositeAccountName = receiptDto.payName;
                        } else if (StringUtils.equals(receiptDto.payAccount, account)) {
                            receiptDto.oppositeAccountName = receiptDto.acceptName;
                        }
                        if (StringUtils.contains(split, BEC_MONEY_FLAG_TWO)) {
                            receiptDto.money = StringUtils.substringAfter(split, BEC_MONEY_FLAG_TWO).replace("\r", "").trim();
                            if (StringUtils.equals(account, receiptDto.payAccount)) {
                                receiptDto.money = "-" + receiptDto.money;
                            }
                        }
                        if (StringUtils.contains(split, USER_WAY)) {
                            receiptDto.digest = splits[i - 1];
                        }
                    }
                }
				receiptDtos.add(receiptDto);
            }
			return receiptDtos;
        } catch (Exception e) {
			e.printStackTrace();
            throw new ServiceException("pdf转Excel失败", ErrorCode.BAD_REQUEST);
        }
    }

    /**
     * 找一个字符串在另一个字符串中第n次出现的位置
     */
    public int findXOccurrenceIndex(String str, String target, int n) {
        int count = 0;
        int index = -1;
        while (count < n) {
            index = str.indexOf(target, index + 1);
            if (index == -1) {
                return -1;
            }
            count++;
        }
        return index;
    }

	/**
	 * 杭州银行
	 */
    public ArrayList<ReceiptDto> importHZCBExcel(String fileName, InputStream inputStream, String account,
        String accountBookId, String endPeriod, String receiptUrl) {
        Workbook workbook = getWorkbook(fileName, inputStream);
        // 获得第一个表单
        Sheet sheet = workbook.getSheetAt(0);
        // 支出金额列
        Cell payMoney = getByValue(sheet, HEADER_PAY);
        if (payMoney == null) {
            throw new ServiceException("表格格式异常", ErrorCode.BAD_REQUEST);
        }
        Row headRow = payMoney.getRow();
        // 银行流水号列
        int tradeNoColumn = getColumnNumByValue(headRow, HEADER_TRADE_NO);
        // 己方银行户名列
        int payNameColumn = getColumnNumByValue(headRow, HEADER_ACCOUNT_NAME);
        // 己方银行账号列
        int payAccountColumn = getColumnNumByValue(headRow, HEADER_PAY_ACCOUNT);
        // 对手方别名列
        int acceptNameColumn = getColumnNumByValue(headRow, HEADER_OPPOSITE_ACCOUNT_NAME);
        // 对手方银行账号
        int acceptAccountColumn = getColumnNumByValue(headRow, HEADER_ACCEPT_ACCOUNT);
        // 支出金额（元）
        int payColumn = getColumnNumByValue(headRow, HEADER_PAY);
        // 收入金额（元）
        int acceptColumn = getColumnNumByValue(headRow, HEADER_INCOME);
        // 交易时间
        int timeColumn = getColumnNumByValue(headRow, HEADER_DATE);
        // 摘要
        int summaryColumn = getColumnNumByValue(headRow, HEADER_SUMMARY);
        Cell startCell = getDownData(sheet, payMoney.getRowIndex(), payMoney.getColumnIndex());
        if (startCell == null) {
            logger.info("没有数据行");
            throw new ServiceException("没有数据行", ErrorCode.BAD_REQUEST);
        }
        int startRow = startCell.getRowIndex();
        // 数据的结束行是最后一行（有合计行的去除合计行）
        int endRow = sheet.getLastRowNum();
        DBCache cache = new DBCache();
        cache.accountBook = accountBookDao.findOne(Long.valueOf(accountBookId));
        if (cache.accountBook.closePeriod != null && endPeriod.compareTo(cache.accountBook.closePeriod) <= 0) {
            logger.info("已结账的期间无法导入日记账!");
            throw new ServiceException("已结账的期间无法导入日记账!", ErrorCode.BAD_REQUEST);
        }
        ArrayList<ReceiptDto> result = new ArrayList<>();
        for (int i = startRow; i < endRow; i++) {
            Row row = sheet.getRow(i);
            ReceiptDto receiptDto = new ReceiptDto();
            receiptDto.tradeNo = row.getCell(tradeNoColumn).getStringCellValue();
            receiptDto.accountName = row.getCell(payNameColumn).getStringCellValue();
            receiptDto.account = row.getCell(payAccountColumn).getStringCellValue();
            receiptDto.oppositeAccountName = row.getCell(acceptNameColumn).getStringCellValue();
            receiptDto.oppositeAccount = row.getCell(acceptAccountColumn).getStringCellValue();
            if (row.getCell(payColumn).getNumericCellValue() == 0.00) {
                receiptDto.money = String.valueOf(row.getCell(acceptColumn).getNumericCellValue());
                receiptDto.payName = receiptDto.oppositeAccountName;
                receiptDto.acceptName = receiptDto.accountName;
                receiptDto.acceptAccount = account;
                receiptDto.payAccount = receiptDto.oppositeAccount;
            } else if (row.getCell(acceptColumn).getNumericCellValue() == 0.00) {
                receiptDto.money = "-" + row.getCell(payColumn).getNumericCellValue();
                receiptDto.payName = receiptDto.accountName;
                receiptDto.acceptName = receiptDto.oppositeAccountName;
                receiptDto.acceptAccount = receiptDto.oppositeAccount;
                receiptDto.payAccount = account;
            }
            receiptDto.tradingDate = row.getCell(timeColumn).getStringCellValue();
            receiptDto.tradingDate = getDateStr(receiptDto.tradingDate);
            receiptDto.digest = row.getCell(summaryColumn).getStringCellValue();
            result.add(receiptDto);
        }
        Collections.reverse(result);
        // 流水和回单对应
        InputStream receiptIns = null;
        if (StringUtils.isNotBlank(receiptUrl)) {
            ResponseEntity<byte[]> data = restTemplate.getForEntity(receiptUrl, byte[].class);
            receiptIns = new ByteArrayInputStream(Objects.requireNonNull(data.getBody()));
        }
        if (receiptIns == null) {
            throw new ServiceException("回单下载失败", ErrorCode.BAD_REQUEST);
        }
        try {
            ZipInputStream zipInputStream = new ZipInputStream(receiptIns);
            ArrayList<String> urls = new ArrayList<>();
            ZipEntry entry;
            while ((entry = zipInputStream.getNextEntry()) != null) {
                if (!entry.isDirectory()) {
                    byte[] data = getByte(zipInputStream);
                    InputStream ins = new ByteArrayInputStream(data);
					PDDocument pdDocument = Loader.loadPDF(new RandomAccessReadBuffer(ins));
                    List<PDDocument> splits = split(pdDocument);
                    for (PDDocument pDDocument : splits) {
						ByteArrayOutputStream baos = new ByteArrayOutputStream();
                        pDDocument.save(baos);
                        byte[] byteArray = baos.toByteArray();
                        ins = new ByteArrayInputStream(byteArray);
                        String url = uploadReceiptToOss(entry.getName(), ins);
                        urls.add(url);
                    }
                }
            }
            zipInputStream.closeEntry();
            zipInputStream.close();
			for (int i = 0; i < result.size(); i++) {
				result.get(i).receiptUrl = urls.get(i);
			}
        } catch (Exception e) {
            throw new ServiceException("文件解压失败", ErrorCode.BAD_REQUEST);
        }
        return result;
	}

    /**
     * 上海银行回单导入，一页一个
     */
    public ArrayList<ReceiptDto> transferSHBANKPdfToExcel(String fileName, InputStream inputStream, String account) {
        try {
			PDDocument document = Loader.loadPDF(new RandomAccessReadBuffer(inputStream));
            List<PDDocument> documents = split(document);
            ArrayList<ReceiptDto> receiptDtos = new ArrayList<>();
            for (PDDocument pdDocument : documents) {
                PDFTextStripper pdfTextStripper = new PDFTextStripper();
                String content = pdfTextStripper.getText(pdDocument);
                if (!StringUtils.contains(content, SHBANK_TYPE)) {
                    continue;
                }
                String separator = System.lineSeparator();
                String[] splits = content.split(separator);
                ReceiptDto receiptDto = new ReceiptDto();
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                pdDocument.save(baos);
                byte[] byteArray = baos.toByteArray();
                InputStream ins = new ByteArrayInputStream(byteArray);
                receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + ".pdf", ins);
                if (StringUtils.contains(content, SHBANK_PAYNAME) && StringUtils.contains(content, SHBANK_ACCEPTNAME)) {
					int start = content.indexOf(SHBANK_CONTENT_FLAG);
					int end = content.lastIndexOf(SHBANK_CONTENT);
					String temp = content.substring(start+1, end);
                    int firstIndex = findXOccurrenceIndex(temp, separator, 1);
                    int secondIndex = findXOccurrenceIndex(temp, separator, 2);
                    int thirdIndex = findXOccurrenceIndex(temp, separator, 3);
                    int forthIndex = findXOccurrenceIndex(temp, separator, 4);
                    receiptDto.tradingDate = StringUtils.substring(temp, firstIndex, secondIndex).replace(separator, "").trim();
                    receiptDto.payName = StringUtils.substringBefore(StringUtils.substring(temp, secondIndex, thirdIndex), " ").replace(separator, "").trim();
                    receiptDto.acceptName = StringUtils.substringAfter(StringUtils.substring(temp, secondIndex, thirdIndex), " ").replace(separator, "").trim();
                    String tempTwo = StringUtils.substring(temp, thirdIndex, forthIndex).replace(separator, "").trim();
                    if (StringUtils.contains(tempTwo, account)) {
                        receiptDto.account = StringUtils.remove(tempTwo, account);
                        int accountOneIndex = findXOccurrenceIndex(tempTwo, receiptDto.account, 1);
                        int accountTwoIndex = findXOccurrenceIndex(tempTwo, account, 1);
                        if (accountOneIndex > accountTwoIndex) {
                            receiptDto.acceptAccount = account;
                            receiptDto.payAccount = receiptDto.account;
                        } else {
                            receiptDto.acceptAccount = receiptDto.account;
                            receiptDto.payAccount = account;
                        }
                    }
                    for (int i = 0; i < splits.length - 1; i++) {
                        temp = splits[i];
                        if (StringUtils.contains(temp, SHBANK_CONTENT)) {
                            receiptDto.tradeNo = splits[i + 2];
                        }
                        if (StringUtils.contains(temp, SHBANK_MONEY)) {
                            int spaceIndex = temp.lastIndexOf(" ");
                            receiptDto.money = temp.substring(spaceIndex).replace(separator, "").trim();
                            if (StringUtils.equals(receiptDto.payAccount, account)) {
                                receiptDto.money = "-" + receiptDto.money;
                            }
                        }
                        if (StringUtils.contains(temp, SHBANK_DIGEST)) {
                            if (!StringUtils.contains(splits[i + 1], SHBANK_DIGEST_FLAG)) {
                                receiptDto.digest = splits[i + 1].replace(separator, "").trim();
                            }
                        }
                    }
                } else {
                    for (int i = 0; i < splits.length; i++) {
                        String split = splits[i];
                        if (StringUtils.contains(split, SHBANK_CONTENT) && !StringUtils.contains(content, SHBANK_FLAG_TWO)) {
                            receiptDto.tradingDate = StringUtils.substringBefore(splits[i - 1], " ").replace(separator, "").trim();
                            receiptDto.tradeNo = StringUtils.substringAfter(splits[i - 1], " ").replace(separator, "").trim();
                            if (!StringUtils.contains(content, SHBANK_PAYNAME) && StringUtils.contains(content, SHBANK_FLAG)) {
                                receiptDto.digest = SHBANK_FLAG;
                                receiptDto.acceptName = splits[i + 1].replace(separator, "").trim();
                                receiptDto.acceptAccount = splits[i + 2].replace(separator, "").trim();
                                receiptDto.money = StringUtils.substringAfter(splits[i + 4], " ").replace(separator, "").trim();
                            }

                        } else if (StringUtils.contains(content, SHBANK_FLAG_TWO)) {
                            if (StringUtils.contains(split, SHBANK_RECEIPT_NO)) {
                                receiptDto.tradeNo = StringUtils.substringAfter(split, SHBANK_RECEIPT_NO).replace(separator, "").trim();
                            }
							if(StringUtils.contains(split, SHBANK_MONEY_TWO)){
								receiptDto.money="-"+StringUtils.substringAfter(split, SHBANK_MONEY_TWO).replace(separator, "").trim();
							}
                            if (StringUtils.contains(split, SHBANK_ACCOUNT)) {
                                receiptDto.payAccount = StringUtils.substringAfter(split, SHBANK_ACCOUNT).replace(separator, "").trim();
                            }
							Pattern pattern = Pattern.compile("\\d{4}年\\d{2}月\\d{2}日");
							Matcher matcher = pattern.matcher(split);
							while (matcher.find()) {
								receiptDto.tradingDate=split;
								receiptDto.payName = splits[i + 1].replace(separator, "").trim();
								receiptDto.digest = splits[i + 2].replace(separator, "").trim();
							}
                        }
                    }
                }
                if (StringUtils.equals(receiptDto.acceptAccount, account)) {
                    receiptDto.oppositeAccountName = receiptDto.payName;
                } else if (StringUtils.equals(receiptDto.payAccount, account)) {
                    receiptDto.oppositeAccountName = receiptDto.acceptName;
                }
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy年MM月dd日");
                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyyMMdd");
                Date date = inputFormat.parse(receiptDto.tradingDate);
                receiptDto.tradingDate = outputFormat.format(date);
                receiptDtos.add(receiptDto);
            }
            return receiptDtos;
        } catch (Exception e) {
            throw new ServiceException("pdf转Excel失败", ErrorCode.BAD_REQUEST);
        }
    }

    /**
     * 招商银行 一页一个
     */
    public ArrayList<ReceiptDto> transferCMBPdfToExcel(String fileName, InputStream inputStream, String account) {
        try {
			PDDocument document = Loader.loadPDF(new RandomAccessReadBuffer(inputStream));
            List<PDDocument> documents = split(document);
            ArrayList<ReceiptDto> receiptDtos = new ArrayList<>();
            for (PDDocument pdDocument : documents) {
                PDFTextStripper pdfTextStripper = new PDFTextStripper();
                String content = pdfTextStripper.getText(pdDocument);
				String separator = System.lineSeparator();
				if(StringUtils.contains(content,CMB_FLAG_TWO)||StringUtils.isEmpty(content.replace(separator,"").trim())){
					continue;
				}
                String[] splits = content.split(separator);
                ReceiptDto receiptDto = new ReceiptDto();
				ByteArrayOutputStream baos = new ByteArrayOutputStream();
				pdDocument.save(baos);
				byte[] byteArray = baos.toByteArray();
				InputStream ins = new ByteArrayInputStream(byteArray);
				receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + ".pdf", ins);
                for (String split : splits) {
                    if (StringUtils.contains(split, CMB_DATE)) {
                        receiptDto.tradingDate = StringUtils.substringBetween(split, CMB_DATE, CMB_DIGEST).trim();
                    }
                    if (StringUtils.contains(split, CMB_DATE_FLAG)) {
                        receiptDto.tradingDate = StringUtils.substringBetween(split, CMB_DATE_FLAG, CMB_DIGEST).trim();
                    }
                    if (StringUtils.contains(split, CMB_DIGEST)) {
                        receiptDto.digest = StringUtils.substringAfter(split, CMB_DIGEST).replace(separator, "").trim();
                    }
                    if (StringUtils.contains(split, CMB_NO)) {
                        receiptDto.tradeNo = StringUtils.substringAfter(split, CMB_NO).replace(separator, "").trim();
                    }
                    if (StringUtils.contains(split, CMB_PAY_ACCOUNT)) {
                        receiptDto.payAccount = StringUtils.substringBetween(split, CMB_PAY_ACCOUNT, CMB_PAYER).replace("：","").trim();
                        receiptDto.payName = StringUtils.substringAfter(split, CMB_PAYER).replace(separator, "").trim();
                    }
                    if (StringUtils.contains(split, CMB_ACCEPT_ACCOUNT)) {
                        receiptDto.acceptAccount = StringUtils.substringBetween(split, CMB_ACCEPT_ACCOUNT, " ").trim();
                        receiptDto.acceptName = StringUtils.substringAfter(split, CMB_ACCEPTER).replace(separator, "").trim();
                    }
                    if (StringUtils.contains(split, CMB_ACCOUNT)) {
                        receiptDto.account = StringUtils.substringBetween(split, CMB_ACCOUNT, CMB_NAME).trim();
                        receiptDto.accountName = StringUtils.substringAfter(split, CMB_NAME).replace(separator, "").trim();
                        if (StringUtils.contains(content, CMB_FLAG) && StringUtils.equals(receiptDto.account, account)) {
                            receiptDto.payAccount = receiptDto.account;
                            receiptDto.payName = receiptDto.accountName;
                        }
                    }
                    if (StringUtils.contains(split, CMB_MONEY) && StringUtils.contains(split, CMB_MONEY_TWO)) {
                        receiptDto.money = StringUtils.substringAfter(split, CMB_MONEY_TWO).replace(separator, "").trim();
                    }
                    if (StringUtils.contains(split, CMB_MONEY_FLAG)) {
                        receiptDto.money = StringUtils.substringAfter(split, CMB_MONEY_FLAG).replace(separator, "").trim();
                    }
                }
                if (StringUtils.contains(receiptDto.payName, "_")) {
                    receiptDto.payName = receiptDto.payName.replace("_", "-");
                }
                if (StringUtils.equals(receiptDto.payAccount, account)) {
                    receiptDto.money = "-" + receiptDto.money;
                }
                if (receiptDto.tradingDate != null) {
                    SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy年MM月dd日");
                    SimpleDateFormat outputFormat = new SimpleDateFormat("yyyyMMdd");
                    Date date = inputFormat.parse(receiptDto.tradingDate);
                    receiptDto.tradingDate = outputFormat.format(date);
                }
                if (receiptDto.payName != null && receiptDto.acceptName != null) {
                    if (StringUtils.equals(receiptDto.payAccount, account)) {
                        receiptDto.oppositeAccountName = receiptDto.acceptName;
                    } else if (StringUtils.equals(receiptDto.acceptAccount, account)) {
                        receiptDto.oppositeAccountName = receiptDto.payName;
                    }
                }
                receiptDtos.add(receiptDto);
            }
            return receiptDtos;
        } catch (Exception e) {
            throw new ServiceException("pdf转Excel失败", ErrorCode.BAD_REQUEST);
        }
    }

    /**
     * 中国工商银行回单导入，压缩包，一个文件一个回单，一页一个
     */
    public ReceiptDto transferICBCPdfToExcel(String fileName, InputStream inputStream, String account) {
        try {
			PDDocument document = Loader.loadPDF(new RandomAccessReadBuffer(inputStream));
			List<InputStream> imageIns = parsePdfToPng(document);
			PDFTextStripper pdfTextStripper = new PDFTextStripper();
            String content = pdfTextStripper.getText(document);
            System.out.println(content);
            System.out.println("***********");
            String separator = System.lineSeparator();
            String[] splits = content.split(separator);
            ReceiptDto receiptDto = new ReceiptDto();
			receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + ".pdf", imageIns.get(0));
			for (int i = 0; i < splits.length; i++) {
                String split = splits[i];
                if (StringUtils.contains(content, ICBC_BUSINESS_INDEX)) {
                    if (StringUtils.contains(content, ICBC_ACCEPT_ACCOUNT_FLAG)) {
                        if (StringUtils.contains(split, ICBC_FLAG)) {
                            receiptDto.acceptAccount = splits[i + 4].replace(separator, "").trim();
                            receiptDto.tradingDate = splits[i + 5].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, ICBC_ACCEPT_ACCOUNT_FLAG)) {
                            receiptDto.payAccount = splits[i - 3].replace(separator, "").trim();
                            receiptDto.acceptName = splits[i + 1].replace(separator, "").trim();
                            receiptDto.payName = splits[i + 3].replace(separator, "").trim();
                            if (StringUtils.contains(receiptDto.acceptName, ICBC_FLAG_ACCEPT_NAME_TWO)) {
                                receiptDto.acceptName = splits[splits.length - 3].replace(separator, "").trim();
                                receiptDto.payName = "";
                            }
                        }
                    }
                    if (StringUtils.contains(content, ICBC_PAY_ACCOUNT_FLAG)) {
                        if (StringUtils.contains(split, ICBC_FLAG)) {
                            receiptDto.payAccount = splits[i + 4].replace(separator, "").trim();
                            receiptDto.tradingDate = splits[i + 5].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, ICBC_PAY_ACCOUNT_FLAG)) {
                            if (StringUtils.contains(splits[i + 1], ICBC_RECEIVE_FEE)) {
                                receiptDto.payName = splits[splits.length - 1].replace(separator, "").trim();
                            } else {
                                receiptDto.acceptAccount = splits[i - 8].replace(separator, "").replace(" ", "").trim();
                                receiptDto.acceptName = splits[splits.length - 4].replace(separator, "").trim();
                                if (StringUtils.contains(receiptDto.acceptName, ICBC_FLAG_ACCEPT_NAME)) {
                                    receiptDto.acceptName = "";
                                }
                                receiptDto.payName = splits[splits.length - 2].replace(separator, "").trim();
                            }
                        }
                    }
                    if (StringUtils.contains(split, ICBC_PAY_ACCOUNT_FLAG) || StringUtils.contains(split, ICBC_ACCEPT_ACCOUNT_FLAG)) {
                        if (!StringUtils.contains(splits[i - 6], ".") && !StringUtils.contains(splits[i - 6], ",")) {
                            receiptDto.money = splits[i - 7].replace(separator, "").trim();
                            receiptDto.digest = splits[i - 8].replace(separator, "").trim();
                        } else {
                            receiptDto.money = splits[i - 6].replace(separator, "").trim();
                            receiptDto.digest = splits[i - 7].replace(separator, "").trim();
                        }
                        receiptDto.tradeNo = splits[i - 4].replace(separator, "").trim();
                    }
                } else {
                    if (StringUtils.contains(split, ICBC_DATE)) {
                        receiptDto.tradingDate = StringUtils.substringAfter(split, ICBC_DATE).replace(separator, "").trim();
                    }
                    if (StringUtils.contains(split, SHBANK_RECEIPT_NO)) {
                        receiptDto.tradeNo = StringUtils.substringAfter(split, " ").replace(separator, "").trim();
                    }
                    if (StringUtils.contains(split, ICBC_PAYER) && StringUtils.contains(split, ICBC_PAYER_ACCOUNT)) {
                        receiptDto.payName = StringUtils.substringBetween(split, ICBC_PAYER, ICBC_PAYER_ACCOUNT).trim();
						if(StringUtils.contains(receiptDto.payName, "_")){
							receiptDto.payName =  receiptDto.payName.replace("_","-");
						}
                        receiptDto.payAccount = StringUtils.substringAfter(split, ICBC_PAYER_ACCOUNT).replace(separator, "").trim();
                    }
                    if (StringUtils.contains(split, ICBC_ACCEPTER) && StringUtils.contains(split, ICBC_ACCEPTER_ACCOUNT)) {
                        receiptDto.acceptName = StringUtils.substringBetween(split, ICBC_ACCEPTER, ICBC_ACCEPTER_ACCOUNT).trim();
                        receiptDto.acceptAccount = StringUtils.substringAfter(split, ICBC_ACCEPTER_ACCOUNT).replace(separator, "").trim();
                    }
                    if (StringUtils.contains(split, MONEY) && StringUtils.contains(split, ICBC_MONEY)) {
                        receiptDto.money = StringUtils.substringAfter(split, ICBC_MONEY).replace(separator, "").trim();
                    }
                    if (StringUtils.contains(split, ICBC_DIGEST)) {
                        receiptDto.digest = StringUtils.substringAfter(split, ICBC_DIGEST).replace(separator, "").trim();
                    }
                }
            }
			if(StringUtils.contains(receiptDto.tradingDate,"-")){
				receiptDto.tradingDate = getDateStr(receiptDto.tradingDate);
			}
            if (StringUtils.equals(receiptDto.payAccount, account)) {
                receiptDto.oppositeAccountName = receiptDto.acceptName;
                receiptDto.money = "-" + receiptDto.money;
            } else {
                receiptDto.oppositeAccountName = receiptDto.payName;
            }
            return receiptDto;
        } catch (Exception e) {
            throw new ServiceException("格式转换错误", ErrorCode.BAD_REQUEST);
        }
    }

    /**
     * 江苏银行 一页三个、一页两个、一页一个
     */
    public ArrayList<ReceiptDto> transferJSBPdfToExcel(String fileName, InputStream inputStream, String account) {
        try {
			PDDocument document = Loader.loadPDF(new RandomAccessReadBuffer(inputStream));
            List<PDDocument> documents = split(document);
            ArrayList<ReceiptDto> receiptDtos = new ArrayList<>();
            int count = 0;
            for (PDDocument pdDocument : documents) {
                PDFTextStripper pdfTextStripper = new PDFTextStripper();
                String content = pdfTextStripper.getText(pdDocument);
                int maxCount = count;
                count = StringUtils.countMatches(content, JSB_FLAG) + StringUtils.countMatches(content, JSB_FLAG_TWO);
                if (maxCount < count) {
                    maxCount = count;
                }
                String[] receiptSplits = new String[3];
                List<InputStream> inputStreams = null;
                String separator = System.lineSeparator();
                if (maxCount == 1) {
                    // 一页一个回单
                    receiptSplits[0] = content;
                } else if (maxCount == 2) {
                    // 一页两个
                    receiptSplits = Arrays.stream(content.split(JSB_DATE_FLAG)).filter(s -> s != null && !s.isEmpty() && !StringUtils.equals(s, " ")).toArray(String[]::new);
                    if (StringUtils.contains(content, JSB_FLAG_TWO)) {
                        receiptSplits = receiptSplits[0].split(separator + "江苏银行\\(交易扣款回单\\)" + separator);
                    }
                    inputStreams = pdfTransferImage(pdDocument, 2);
                } else if (maxCount == 3) {
                    // 一页三个
                    receiptSplits = Arrays.stream(content.split(JSB_FLAG)).filter(s -> s != null && !s.isEmpty()).toArray(String[]::new);
                    inputStreams = pdfTransferImage(pdDocument, 3);
                }
                for (int i = 0; i < receiptSplits.length; i++) {
                    String receipt = receiptSplits[i];
                    if (receipt == null) {
                        break;
                    }
                    ReceiptDto receiptDto = new ReceiptDto();
                    if (inputStreams != null && maxCount != 1) {
                        receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + i + ".pdf", inputStreams.get(i));
                    }
                    if (maxCount == 1) {
                        ByteArrayOutputStream baos = new ByteArrayOutputStream();
                        pdDocument.save(baos);
                        byte[] byteArray = baos.toByteArray();
                        InputStream ins = new ByteArrayInputStream(byteArray);
                        receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + ".pdf", ins);
                    }
                    String[] splits = receipt.split(separator);
                    for (int j = 0; j < splits.length; j++) {
                        String split = splits[j];
                        // 一页两个兼容 江苏银行(交易扣款回单)
                        if (StringUtils.contains(split, JSB_ACCOUNT_NAME)) {
                            receiptDto.payName = StringUtils.substringBetween(split, JSB_ACCOUNT_NAME, " ").trim();
                        }
                        if (StringUtils.contains(split, JSB_ACCOUNT_PAY)) {
                            receiptDto.payAccount = StringUtils.substringBetween(split, JSB_ACCOUNT_PAY, " ").trim();
                            receiptDto.acceptAccount = StringUtils.substringBetween(split, JSB_ACCOUNT_ACCEPT, JSB_CLIENT_NO).trim();
                        }
                        if (StringUtils.contains(split, JSB_DATE_TWO)) {
                            receiptDto.tradingDate = StringUtils.substringBefore(splits[j + 1], " ");
                        }
                        if (StringUtils.contains(split, JSB_MONEY_THREE) && StringUtils.contains(split, JSB_MONEY_FORTH)) {
                            receiptDto.money = StringUtils.substringAfter(split, JSB_MONEY_FORTH).replace(separator, "").trim();
                        }
                        // 一页三个
                        if (StringUtils.contains(split, JSB_DATE)) {
                            receiptDto.tradingDate = StringUtils.substringAfter(split, JSB_DATE).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSB_PAYER_NAME)) {
                            receiptDto.payName = StringUtils.substringBetween(split, JSB_PAYER_NAME, " ").trim();
                        }
                        if (StringUtils.contains(split, JSB_ACCEPTER_NAME)) {
                            receiptDto.acceptName = StringUtils.substringAfter(split, JSB_ACCEPTER_NAME).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSB_PAYER_ACCOUNT)) {
                            receiptDto.payAccount = StringUtils.substringBetween(split, JSB_PAYER_ACCOUNT, " ").trim();
                        }
                        if (StringUtils.contains(split, JSB_ACCEPTER_ACCOUNT)) {
                            receiptDto.acceptAccount = StringUtils.substringAfter(split, JSB_ACCEPTER_ACCOUNT).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSB_MONEY)) {
                            receiptDto.money = StringUtils.substringBetween(split, JSB_MONEY, " ").trim();
                        }
                        if (StringUtils.contains(split, JSB_DIGEST)) {
                            receiptDto.digest = StringUtils.substringAfter(split, JSB_DIGEST).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSB_TRADE_NO)) {
                            receiptDto.tradeNo = StringUtils.substringAfter(split, JSB_TRADE_NO).replace(separator, "").trim();
                        }
                        // 一页两个/一个
                        if (StringUtils.contains(split, JSB_DATE_FLAG_TWO)
                            && !StringUtils.contains(split, JSB_DATE_FLAG)) {
                            receiptDto.tradingDate = StringUtils.substringBefore(split, JSB_DATE_FLAG_TWO).trim();
                        }
                        if (StringUtils.contains(split, JSB_DATE_FLAG_TWO)
                            && StringUtils.contains(split, JSB_DATE_FLAG)) {
                            receiptDto.tradingDate = StringUtils.substringBetween(split, JSB_DATE_FLAG, JSB_DATE_FLAG_TWO).trim();
                        }
                        if (StringUtils.contains(split, JSB_PAYER_NAME_TWO)) {
                            receiptDto.payName = StringUtils.substringBetween(split, JSB_PAYER_NAME_TWO, " ").trim();
                        }
                        if (StringUtils.contains(split, JSB_ACCEPTER_NAME_TWO)) {
                            receiptDto.acceptName = StringUtils.substringAfter(split, JSB_ACCEPTER_NAME_TWO).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSB_PAYER_ACCOUNT_TWO)) {
                            receiptDto.payAccount = StringUtils.substringBetween(split, JSB_PAYER_ACCOUNT_TWO, " ").trim();
                        }
                        if (StringUtils.contains(split, JSB_ACCEPTER_ACCOUNT_TWO)) {
                            receiptDto.acceptAccount = StringUtils.substringAfter(split, JSB_ACCEPTER_ACCOUNT_TWO).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSB_MONEY_TWO)) {
                            receiptDto.money = StringUtils.substringBetween(split, JSB_MONEY_TWO, " ").trim();
                        }
                        if (StringUtils.contains(split, JSB_DIGEST_TWO)) {
                            receiptDto.digest = StringUtils.substringAfter(split, JSB_DIGEST_TWO).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSB_TRADE_NO_TWO)) {
                            receiptDto.tradeNo = StringUtils.substringAfter(split, JSB_TRADE_NO_TWO).replace(separator, "").trim();
                        }
                    }
                    if (StringUtils.equals(account, receiptDto.payAccount)) {
                        receiptDto.money = "-" + receiptDto.money;
                        receiptDto.oppositeAccountName = receiptDto.acceptName;
                    } else if (StringUtils.equals(account, receiptDto.acceptAccount)) {
                        receiptDto.oppositeAccountName = receiptDto.payName;
                    }
                    if (receiptDto.tradingDate.matches(PERIOD_CHAR)) {
                        Pattern pattern = Pattern.compile(PERIOD_CHAR);
                        Matcher matcher = pattern.matcher(receiptDto.tradingDate);
                        while (matcher.find()) {
                            try {
                                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy年MM月dd日");
                                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyyMMdd");
                                Date date = inputFormat.parse(matcher.group());
                                receiptDto.tradingDate = outputFormat.format(date);
                            } catch (Exception e) {
                                throw new ServiceException("日期格式错误", ErrorCode.BAD_REQUEST);
                            }
                        }
                    }
                    receiptDtos.add(receiptDto);
                }
            }
            return receiptDtos;
        } catch (Exception e) {
            throw new ServiceException("pdf转Excel失败", ErrorCode.BAD_REQUEST);
        }
    }

    /**
     * 浦发银行 一个压缩包，一个pdf文件一个回单
     */
    public ArrayList<ReceiptDto> transferSPDBPdfToExcel(String fileName, InputStream inputStream, String account) {
        try {
			ArrayList<ReceiptDto> receiptDtos = new ArrayList<>();
            PDDocument document = Loader.loadPDF(new RandomAccessReadBuffer(inputStream));
			List<PDDocument> documents = split(document);
			for (PDDocument pdDocument : documents) {
				PDFTextStripper pdfTextStripper = new PDFTextStripper();
				String content = pdfTextStripper.getText(pdDocument);
				String separator = System.lineSeparator();
				String[] splits = content.split(separator);
				ReceiptDto receiptDto = new ReceiptDto();
				ByteArrayOutputStream baos = new ByteArrayOutputStream();
				pdDocument.save(baos);
				byte[] byteArray = baos.toByteArray();
				InputStream ins = new ByteArrayInputStream(byteArray);
				receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + ".pdf", ins);
				for (int i = 0; i < splits.length; i++) {
					String split = splits[i];
					if (StringUtils.contains(split, PSBC_DATE)) {
						receiptDto.tradingDate = StringUtils.substringBetween(split, PSBC_DATE, " ");
						String regex = "(\\d{4})年(\\d{2})月(\\d{2})日";
						Pattern pattern = Pattern.compile(regex);
						Matcher matcher = pattern.matcher(receiptDto.tradingDate);
						if (matcher.find()) {
							String year = matcher.group(1);
							String month = matcher.group(2);
							String day = matcher.group(3);
							receiptDto.tradingDate = year + month + day;
						} else {
							throw new ServiceException("未找到日期", ErrorCode.BAD_REQUEST);
						}
					}
					if (StringUtils.contains(split, SPDB_NO)) {
						receiptDto.tradeNo = StringUtils.substringBetween(split, SPDB_NO, " ");
					}
					if (StringUtils.contains(split, SPDB_PAYER)) {
						receiptDto.payName = StringUtils.remove(splits[i + 1], SPDB_PAYER_FLAG).replace(separator, "").trim();
					}
					if (StringUtils.contains(split, SPDB_ACCEPTER)) {
						receiptDto.acceptName = StringUtils.remove(splits[i + 1], SPDB_PAYER_FLAG).replace(separator, "").trim();
					}
					if (StringUtils.contains(split, SPDB_ACCOUNT)) {
						receiptDto.payAccount = StringUtils.substringBetween(split, SPDB_ACCOUNT, " ");
						receiptDto.acceptAccount = StringUtils.substringAfterLast(split, SPDB_ACCOUNT).replace(separator, "").trim();
					}
					if (StringUtils.contains(split, SPDB_MONEY)) {
						receiptDto.money = StringUtils.substringAfter(split, SPDB_MONEY).replace("元", "").replace(separator, "").trim();
					}
					if (StringUtils.contains(split, SPDB_DIGEST)) {
						receiptDto.digest = StringUtils.substringBetween(split, SPDB_DIGEST, " ").trim();
					}
				}
				if (StringUtils.equals(account, receiptDto.payAccount)) {
					receiptDto.money = "-" + receiptDto.money;
					receiptDto.oppositeAccountName = receiptDto.acceptName;
				}
				if (StringUtils.equals(account, receiptDto.acceptAccount)) {
					receiptDto.oppositeAccountName = receiptDto.payName;
				}
				receiptDtos.add(receiptDto);
			}
            return receiptDtos;
        } catch (Exception e) {
            throw new ServiceException("pdf转Excel失败", ErrorCode.BAD_REQUEST);
        }
    }

    /**
     * 江苏农村商业银行，压缩包导入，一页一个
     */
    public ArrayList<ReceiptDto> transferJSRCBPdfToExcel(String fileName, InputStream inputStream, String account) {
        try {
            PDDocument document = Loader.loadPDF(new RandomAccessReadBuffer(inputStream));
            List<PDDocument> documents = split(document);
            ArrayList<ReceiptDto> receiptDtos = new ArrayList<>();
            for (PDDocument pdDocument : documents) {
                PDFTextStripper pdfTextStripper = new PDFTextStripper();
                String content = pdfTextStripper.getText(pdDocument);
				System.out.println(content);
				System.out.println("************");
				String separator = System.lineSeparator();
                String[] splits = content.split(separator);
                ReceiptDto receiptDto = new ReceiptDto();
				ByteArrayOutputStream baos = new ByteArrayOutputStream();
				pdDocument.save(baos);
				byte[] byteArray = baos.toByteArray();
				InputStream ins = new ByteArrayInputStream(byteArray);
				receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + ".pdf", ins);
                boolean hasName = false;
                for (int i = 0; i < splits.length; i++) {
                    String split = splits[i];
                    if (StringUtils.contains(content, JSRCB_NO_FLAG)) {
                        if (StringUtils.contains(split, JSRCB_DATE_TWO)) {
                            receiptDto.tradingDate = splits[i + 3].replace(separator, "").trim();
                            receiptDto.accountName = splits[i + 4].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_NO_FLAG)) {
                            if (StringUtils.contains(content, JSRCB_FLAG_NINE)
                                || StringUtils.contains(content, JSRCB_ACCEPTER)
                                || StringUtils.contains(content, JSRCB_FLAG_EIGHT)|| StringUtils.contains(content, JSRCB_ELEVN)|| StringUtils.contains(content, JSRCB_TYPE)) {
                                receiptDto.acceptAccount = splits[i + 1].replace(separator, "").trim();
                            }
                        }
                        if (StringUtils.contains(split, JSRCB_ACCOUNT)) {
                            receiptDto.account = StringUtils.substringAfter(split, JSRCB_ACCOUNT).replace(separator, "").trim();;
                        }
                        if (StringUtils.contains(split, JSRCB_MONEY_TWO)) {
                            receiptDto.money = StringUtils.substringAfter(split, JSRCB_MONEY_TWO).replace(separator, "").trim();
                            if (StringUtils.contains(content, JSRCB_FLAG_NINE)
                                || StringUtils.contains(content, JSRCB_ACCEPTER)
                                || StringUtils.contains(content, JSRCB_FLAG_EIGHT)|| StringUtils.contains(content, JSRCB_ELEVN)|| StringUtils.contains(content, JSRCB_TYPE)) {
                                receiptDto.acceptName = splits[i + 1].replace(separator, "").trim();
                            }
                        }
                        if (StringUtils.contains(split, JSRCB_NO)) {
                            receiptDto.tradeNo = splits[i + 1].replace(separator, "").trim();
                        }
                        receiptDto.digest = JSRCB_DIGEST_TWO;
                    } else if (StringUtils.contains(content, JSRCB_NO_FLAG_THREE)) {
                        if (StringUtils.contains(split, JSRCB_ACCOUNT_NAME)) {
                            receiptDto.tradingDate = splits[i + 1].replace(separator, "").trim();
                            receiptDto.payName = splits[i + 2].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_ACCOUNT_PAY)) {
                            receiptDto.payAccount = StringUtils.substringAfter(splits[i + 1], " ").replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_MONEY_FORTH)) {
                            receiptDto.money = splits[i - 1].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_NAME)) {
                            if (StringUtils.contains(content, JSRCB_FLAG_NINE)
                                || StringUtils.contains(content, JSRCB_ACCEPTER)) {
                                receiptDto.payName = splits[i + 2].replace(separator, "").trim();
                            }
                        }
                        if (StringUtils.contains(split, JSRCB_NO)) {
                            receiptDto.tradeNo = StringUtils.substringAfter(split, " ").replace(separator, "").trim();
                        }
                    } else if (StringUtils.contains(content, JSRCB_NO_FLAG_FORTH)) {
                        if (StringUtils.contains(split, JSRCB_DATE_TWO)) {
                            receiptDto.payName = splits[i + 1].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_DATE_FLAG)) {
                            receiptDto.tradingDate = StringUtils.substringBefore(splits[i + 2], " ").trim();
                            receiptDto.payAccount = StringUtils.substringAfter(splits[i + 2], " ").replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_MONEY_FIFTH)) {
                            receiptDto.money = StringUtils.substringBetween(split, JSRCB_MONEY_FIFTH, " ").trim();
                        }
                    } else if (StringUtils.contains(content, JSRCB_NO_FLAG_TWO)) {
                        if (StringUtils.contains(content, JSRCB_REPEAT)) {
                            receiptDto.isManyPage = true;
                        }
                        if (StringUtils.contains(split, JSRCB_DATE_TWO)) {
                            receiptDto.tradingDate = splits[i + 3].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_NO_FLAG_TWO)) {
                            receiptDto.payName = splits[i + 1].replace(separator, "").trim();
                            Pattern textRegex = Pattern.compile("[\\u4e00-\\u9fa5]+");
                            Pattern numberRegex = Pattern.compile("\\d+");
                            Matcher textMatcher = textRegex.matcher(receiptDto.payName);
                            Matcher numberMatcher = numberRegex.matcher(receiptDto.payName);
                            StringBuilder textPart = new StringBuilder();
                            while (textMatcher.find()) {
                                textPart.append(textMatcher.group());
                            }
                            StringBuilder numberPart = new StringBuilder();
                            while (numberMatcher.find()) {
                                numberPart.append(numberMatcher.group());
                            }
                            receiptDto.payName = textPart.toString();
                            receiptDto.payAccount = numberPart.toString();
                        }
                        if (StringUtils.contains(split, JSRCB_NO)) {
                            receiptDto.tradeNo = StringUtils.substringAfter(split, " ").replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_MONEY_FLAG_FORTH)) {
                            receiptDto.money = StringUtils.substringAfter(split, JSRCB_MONEY_FLAG_FORTH).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_DIGEST_THREE)) {
                            receiptDto.digest = StringUtils.substringBefore(splits[i + 1], " ");
                        }
                    } else if (StringUtils.contains(content, JSRCB_RECEIPT_TYPE)) {
                        if (!StringUtils.contains(content, JSRCB_ACCEPTER) && !StringUtils.contains(content, JSRCB_FLAG_EIGHT)&& !StringUtils.contains(content, JSRCB_FLAG_NINE)) {
                            if (StringUtils.contains(split, JSRCB_DATE)) {
                                receiptDto.tradingDate =
                                    StringUtils.substringAfter(split, JSRCB_DATE).replace(separator, "").trim();
                            }
                            if (StringUtils.contains(split, JSRCB_RECEIPT_TYPE)) {
                                receiptDto.tradeNo = splits[i + 1].replace(separator, "").trim();
                                receiptDto.money = splits[i + 2].replace(separator, "").trim();
                                Pattern numberRegex = Pattern.compile("\\d{1,3}(,\\d{3})*(\\.\\d+)?|\\d+(\\.\\d+)?");
                                Matcher numberMatcher = numberRegex.matcher(receiptDto.money);
                                StringBuilder numberPart = new StringBuilder();
                                while (numberMatcher.find()) {
                                    numberPart.append(numberMatcher.group());
                                }
                                receiptDto.money = numberPart.toString();
                            }
                            if (StringUtils.contains(split, JSRCB_DIGEST_FORTH)) {
                                receiptDto.digest =
                                    StringUtils.substringAfter(split, JSRCB_DIGEST_FORTH).replace(separator, "").trim();
                            }
                            if (StringUtils.contains(split, JSRCB_ACCEPT_ACCOUNT)) {
                                receiptDto.acceptAccount = StringUtils.substringAfter(split, JSRCB_ACCEPT_ACCOUNT)
                                    .replace(separator, "").trim();
                            }
                            if (StringUtils.contains(split, JSRCB_ACCEPT_NAME)) {
                                receiptDto.acceptName = StringUtils.substringBefore(split, JSRCB_ACCEPT_NAME).trim();
                            }
                        } else {
                            if (StringUtils.contains(split, JSRCB_DATE_TWO)) {
                                receiptDto.tradingDate = splits[i + 2].replace(separator, "").trim();
                            }
							if (StringUtils.contains(split, JSRCB_ACCEPTER)|| StringUtils.contains(split, JSRCB_FLAG_EIGHT)||StringUtils.contains(split, JSRCB_FLAG_NINE)) {
								receiptDto.acceptAccount = splits[i + 1].replace(separator, "").trim();
							}
                            if (StringUtils.contains(split, JSRCB_MONEY_THREE)) {
                                receiptDto.money =
                                    StringUtils.substringBetween(split, JSRCB_MONEY_THREE, CCB_MONEY).trim();
                            }
                            if (StringUtils.contains(split, JSRCB_TRADE_NO)) {
                                receiptDto.tradeNo =
                                    StringUtils.substringAfter(split, JSRCB_TRADE_NO).replace(separator, "").trim();
                            }
                            if (StringUtils.contains(split, JSRCB_NAME_TWO)) {
                                receiptDto.acceptName = StringUtils.substringBetween(split, " ", JSRCB_NAME_TWO).trim();
                            }
                            if (StringUtils.contains(split, JSRCB_DIGEST_FORTH)) {
                                receiptDto.digest = StringUtils.substringBefore(split, JSRCB_DIGEST_FORTH).trim();
                            }
                        }
                    } else if (StringUtils.contains(content, JSRCB_RECEIPT_TYPE_TWO)) {
                        if (StringUtils.contains(split, JSRCB_DATE_TWO)) {
                            receiptDto.acceptAccount = splits[i - 1].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_RECEIPT_TYPE_TWO)) {
                            receiptDto.tradingDate = StringUtils.substringBefore(splits[i + 1], " ").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_MONEY_SIXTH)) {
                            receiptDto.money = StringUtils.substringAfter(split, " ").replace(separator, "").trim();
                            receiptDto.money = receiptDto.money.replaceAll("[^0-9.,]", "").replaceAll(",", "");
                        }
                        if (StringUtils.contains(split, JSRCB_NO)) {
                            receiptDto.tradeNo = splits[i + 1].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_ACCEPTER_TWO)) {
                            receiptDto.acceptName = splits[i + 1].replace(separator, "").trim();
                        }
                    } else if(StringUtils.contains(content, JSRCB_RECEIPT_TYPE_CHARGE)){
						if(StringUtils.contains(split, JSRCB_DATE_TWO)){
							receiptDto.payName = splits[i + 1].replace(separator, "").trim();
                        }
						if(StringUtils.contains(split, JSRCB_PAYER)){
							receiptDto.tradingDate = splits[i -1].replace(separator, "").trim();
							receiptDto.payAccount = splits[i +1].replace(separator, "").trim();
						}
						if(StringUtils.contains(split, JSRCB_PAYER_ACCOUNT)){
							receiptDto.acceptAccount = StringUtils.substringAfter(split,JSRCB_PAYER_ACCOUNT).replace(separator, "").trim();
						}
						if(StringUtils.contains(split, JSRCB_MONEY_CHARGE)){
							receiptDto.money = StringUtils.substringAfter(split,JSRCB_MONEY_CHARGE).replace(separator, "").trim();
						}
                        if(StringUtils.contains(split, JSRCB_NO_THREE)){
                            receiptDto.tradeNo = StringUtils.substringAfter(split,JSRCB_NO_THREE).replace(separator, "").trim();
                        }
						if(StringUtils.contains(split, JSRCB_ACCEPTER_CHARGE)){
                            receiptDto.acceptName = splits[i +1].replace(separator, "").trim();
						}
                    } else if (StringUtils.contains(content, JSRCB_RECEIPT_TYPE_THREE)) {
                        if (StringUtils.contains(split, JSRCB_PAYER)) {
                            receiptDto.payName = splits[i + 1].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_PAYER_ACCOUNT)) {
                            receiptDto.payAccount = splits[i + 1].replace(separator, "").trim();
                            receiptDto.acceptAccount = splits[i + 2].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_DATE)) {
                            receiptDto.money = splits[i - 1].replace(separator, "").trim();
                            receiptDto.tradingDate =
                                StringUtils.substringAfter(split, JSRCB_DATE).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_NO_THREE)) {
                            receiptDto.tradeNo =
                                StringUtils.substringAfter(split, JSRCB_NO_THREE).replace(separator, "").trim();;
                        }
                        if (StringUtils.contains(split, JSRCB_ACCEPT)) {
                            receiptDto.acceptName = splits[i + 1].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_MONEY)) {
                            receiptDto.digest = splits[i + 1].replace(separator, "").trim();
                        }
                    } else if (StringUtils.contains(content, JSRCB_RECEIPT_TYPE_FORTH)
                        && !StringUtils.contains(content, JSRCB_RECEIPT_FIVE)
                        && !StringUtils.contains(content, JSRCB_RECEIPT_SIXTH)
                        && !StringUtils.contains(content, JSRCB_RECEIPT_SEVEN)
                        && !StringUtils.contains(content, JSRCB_RECEIPT_EIGHT)
                        && !StringUtils.contains(content, JSRCB_RECEIPT_NINE)) {
                        if (StringUtils.contains(content, JSRCB_FLAG_EIGHT) || StringUtils.contains(content, JSRCB_ACCEPTER)|| StringUtils.contains(content, JSRCB_FLAG_SEVEN)) {
                            if (StringUtils.contains(split, JSRCB_DATE_TWO)) {
                                receiptDto.tradingDate = splits[i + 2].replace(separator, "").trim();
                                receiptDto.payAccount = splits[i + 3].replace(separator, "").trim();
                            }
                            if (StringUtils.contains(split, JSB_ACCEPTER_ACCOUNT_TWO)) {
                                receiptDto.acceptAccount = StringUtils.substringBetween(split, JSB_ACCEPTER_ACCOUNT_TWO, " ").replace(separator, "").trim();
                            }
                            if (StringUtils.contains(split, JSRCB_NO)) {
                                receiptDto.tradeNo = splits[i + 2].replace(separator, "").trim();
                            }
                            if (StringUtils.contains(split, JSRCB_MONEY)) {
                                receiptDto.money = StringUtils.remove(split, JSRCB_MONEY).trim();
								if(StringUtils.contains(receiptDto.money , JSRCB_MONEY_THREE)){
									receiptDto.money = StringUtils.remove(receiptDto.money, JSRCB_MONEY_THREE).trim();
								}
                            }
                            if (StringUtils.contains(split, JSRCB_DIGEST_FORTH)) {
                                receiptDto.digest = StringUtils.substringAfter(split, JSRCB_DIGEST_FORTH).replace(separator, "").trim();
                                receiptDto.payName = splits[i + 1].replace(separator, "").trim();
                                receiptDto.acceptName = splits[i + 2].replace(separator, "").trim();
                            }
                        }
                    } else if (StringUtils.contains(content, JSRCB_FLAG_TEN)) {
                        if (StringUtils.contains(split, JSRCB_PAYER)) {
                            receiptDto.payName = splits[i + 1].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_PAYER_ACCOUNT)) {
                            receiptDto.payAccount = splits[i + 1].replace(separator, "").trim();
                            receiptDto.acceptAccount = splits[i + 2].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_NO)) {
                            receiptDto.tradeNo = splits[i + 1].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, SHBANK_MONEY) && StringUtils.contains(split, JSRCB_MONEY_FIVE)) {
                            receiptDto.money = StringUtils.substringAfter(split,JSRCB_MONEY_FIVE).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_DATE_TWO)) {
                            receiptDto.tradingDate = StringUtils.substringAfter(split, JSRCB_DATE_TWO).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_FLAG_TEN)) {
                            receiptDto.acceptName = splits[i + 1].replace(separator, "").trim();
                            receiptDto.digest = splits[i + 2].replace(separator, "").trim();
                        }
                    } else if (StringUtils.contains(content, JSRCB_FLAG_TYPE)) {
                        receiptDto.acceptAccount = splits[0].replace(separator, "").trim();
                        if (StringUtils.contains(split, JSRCB_FLAG_TYPE)) {
                            receiptDto.tradingDate = StringUtils.substringBefore(split, JSRCB_FLAG_TYPE).trim();
                        }
                        if (StringUtils.contains(split, JSRCB_DIGEST_ONE)) {
                            receiptDto.digest =
                                StringUtils.substringAfter(split, JSRCB_DIGEST_ONE).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_FLAG_PAY_ACCOUNT)) {
                            receiptDto.payAccount = splits[i + 1].replace(separator, "").trim();
                            if (StringUtils.equals(receiptDto.payAccount, JSRCB_ACCEPTER)) {
                                receiptDto.payAccount = "";
                            }
                        }
                        if (StringUtils.contains(split, JSRCB_MONEY_ONE)) {
                            receiptDto.money =
                                StringUtils.substringAfter(split, JSRCB_MONEY_ONE).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, ICBC_PAYER)) {
                            receiptDto.tradeNo = splits[i + 1].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_TRADE_NO_ONE)) {
                            receiptDto.payName = splits[i + 1].replace(separator, "").trim();
                            receiptDto.acceptName = splits[i + 2].replace(separator, "").trim();
                            if (Objects.equals(receiptDto.payAccount, "")) {
                                receiptDto.acceptName = splits[i + 1].replace(separator, "").trim();
                                receiptDto.payName = "";
                            }
                        }
                    } else if (StringUtils.contains(content, JSRCB_FLAG_TYPE_TWO)||StringUtils.contains(content, JSRCB_FLAG_TYPE_THREE)) {
                        if (StringUtils.contains(split, CIB_DATE)) {
                            receiptDto.tradingDate = splits[i + 2].replace(separator, "").trim();
							if(StringUtils.contains(content, JSRCB_FLAG_TYPE_THREE)){
								receiptDto.acceptAccount = splits[i + 3].replace(separator, "").trim();
							}
							if(StringUtils.contains(content, JSRCB_FLAG_TYPE_TWO)){
								receiptDto.payAccount = splits[i + 3].replace(separator, "").trim();
							}
                        }
                        if (StringUtils.contains(split, JSRCB_NO)) {
                            receiptDto.tradeNo = splits[i + 2].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_ACCEPTER)) {
                            receiptDto.payName = splits[i + 1].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_ACCEPT_NAME_FLAG)) {
                            receiptDto.acceptName =
                                StringUtils.substringBetween(split, " ", JSRCB_ACCEPT_NAME_FLAG).trim();
                        }
                        if (StringUtils.contains(split, JSRCB_MONEY_THREE)) {
                            receiptDto.money = StringUtils.substringBetween(split, JSRCB_MONEY_THREE, CCB_MONEY).trim();
                        }
                        if (StringUtils.contains(split, JSRCB_DIGEST_FIFTH)) {
                            receiptDto.digest =
                                StringUtils.substringAfter(split, JSRCB_DIGEST_FIFTH).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_ACCEPT_ACCOUNT_FLAG)) {
							if(StringUtils.contains(content, JSRCB_FLAG_TYPE_TWO)){
								receiptDto.acceptAccount = StringUtils.substringBefore(split, " ").trim();
							}
							if(StringUtils.contains(content, JSRCB_FLAG_TYPE_THREE)){
								receiptDto.payAccount = StringUtils.substringBefore(split, " ").trim();
							}
                        }
                    } else if (StringUtils.contains(content, JSRCB_FLAG_TYPE_ELEVEN)) {
                        if (StringUtils.contains(split, JSRCB_FLAG_TYPE_ELEVEN)) {
                            receiptDto.tradingDate = splits[i - 2].replace(separator, "").trim();
                            receiptDto.payAccount = splits[i + 1].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSB_ACCEPTER_ACCOUNT)) {
                            receiptDto.acceptAccount = StringUtils.substringAfter(split, JSB_ACCEPTER_ACCOUNT).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_NO_THREE)) {
                            receiptDto.tradeNo = StringUtils.substringAfter(split, JSRCB_NO_THREE).replace(separator, "").trim();
                            receiptDto.digest = splits[i - 2].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_TWO_FLAG)) {
                            receiptDto.money = splits[i + 1].replace(separator, "").trim();
                            receiptDto.payName = splits[i + 2].replace(separator, "").trim();
                            receiptDto.acceptName = splits[i + 3].replace(separator, "").trim();
                        }
                    } else {
                        if (StringUtils.contains(split, JSRCB_PAYER)) {
                            if (!StringUtils.equals("交", splits[i + 1].replace(separator, "").trim())
                                && !StringUtils.equals("易", splits[i + 2].replace(separator, "").trim())) {
                                receiptDto.payName = splits[i + 1].replace(separator, "").trim();
                                hasName = true;
                            }
                        }
                        if (StringUtils.contains(split, JSRCB_PAYER_ACCOUNT)) {
                            receiptDto.payAccount = splits[i + 1].replace(separator, "").trim();
                            receiptDto.acceptAccount = splits[i + 2].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_DATE)) {
                            receiptDto.tradingDate = StringUtils.substringAfter(split, JSRCB_DATE).replace(separator, "").trim();
                            if (!hasName && StringUtils.contains(content, JSRCB_FLAG_TWO)) {
                                receiptDto.digest = StringUtils.remove(splits[i + 1], JSRCB_DIGEST_ONE).replace(separator, "").trim();
                            }
                        }
                        if (StringUtils.contains(split, JSRCB_DIGEST)) {
                            receiptDto.digest = StringUtils.substringBetween(split, JSRCB_DIGEST, " ").replace(separator, "").trim();;
                        }
                        if (StringUtils.contains(split, JSRCB_ACCEPTER)
                            || StringUtils.contains(split, JSRCB_ACCEPTER_FLAG)) {
                            if (!StringUtils.contains(splits[i + 2], JSRCB_NO_THREE)) {
                                receiptDto.acceptName = splits[i + 2].replace(separator, "").trim();
                            }
                        }
                        if (StringUtils.contains(split, JSRCB_NO_THREE)) {
                            receiptDto.tradeNo = StringUtils.substringAfter(split, JSRCB_NO_THREE).replace(separator, "").trim();;
                        }
                        if (StringUtils.contains(content, JSRCB_NO_FLAG) && StringUtils.contains(split, JSRCB_NO)) {
                            receiptDto.tradeNo = StringUtils.substringAfter(split, " ").replace(separator, "").trim();;
                        }
                        if (StringUtils.contains(split, JSRCB_MONEY) && StringUtils.contains(split, JSRCB_MONEY_FLAG_TWO)) {
                            int keywordIndex = split.indexOf(JSRCB_MONEY_FLAG_THREE);
                            if (keywordIndex != -1) {
                                String beforeKeyword = split.substring(0, keywordIndex);
                                receiptDto.money = beforeKeyword.replaceAll("[^0-9.,]", "").trim();
                            }
                            if (!hasName && StringUtils.contains(content, JSRCB_FLAG_TWO) && !StringUtils
                                .equals(splits[i + 1].replace(separator, "").trim(), JSRCB_ACCEPT_NAME_TWO)) {
                                receiptDto.payName = splits[i + 1].replace(separator, "").trim();
                                receiptDto.acceptName = splits[i + 2].replace(separator, "").trim();
                            }
                        }
                        if (!StringUtils.contains(split, JSRCB_MONEY)
                            && StringUtils.contains(split, JSRCB_MONEY_FLAG_TWO)) {
                            if (!StringUtils.equals(splits[i + 1].replace(separator, "").trim(), JSRCB_MONEY)) {
                                receiptDto.money = splits[i + 1].replace(separator, "").trim();
                            }
                        }
                        if (StringUtils.contains(split, JSRCB_MONEY_THREE)
                            && !StringUtils.contains(content, JSRCB_FLAG_TWO)
                            && !StringUtils.contains(content, JSRCB_FLAG_FORTH)
                            && !StringUtils.contains(content, JSRCB_FLAG_THREE)) {
                            receiptDto.money = StringUtils.substringBefore(split, JSRCB_MONEY_THREE).trim();
                        }
						if (StringUtils.contains(split, JSRCB_MONEY_THREE) && StringUtils.contains(content, JSRCB_FLAG_THREE)) {
							if(StringUtils.contains(splits[i+1], SHBANK_MONEY)){
								receiptDto.money = StringUtils.substringBefore(split, JSRCB_MONEY_THREE).replace(separator, "").trim();
							}
						}
                        if (StringUtils.contains(split, JSRCB_MONEY)
                            && !StringUtils.contains(split, JSRCB_MONEY_FLAG_TWO)
                            && !StringUtils.contains(content, JSRCB_FLAG)
                            && !StringUtils.contains(content, JSRCB_FLAG_FIFTH)
                            && !StringUtils.contains(content, JSRCB_FLAG_FORTH)) {
                            receiptDto.payName = splits[i + 1].replace(separator, "").trim();
                            receiptDto.acceptName = splits[i + 2].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_MONEY)
                            && !StringUtils.contains(split, JSRCB_MONEY_FLAG_TWO)
                            && StringUtils.contains(content, JSRCB_FLAG)) {
                            if (!StringUtils.equals(JSRCB_ACCEPT_NAME_TWO,
                                splits[i + 1].replace(separator, "").trim())) {
                                receiptDto.acceptName = splits[i - 1].replace(separator, "").trim();
                                receiptDto.digest = splits[i + 1].replace(separator, "").trim();
                            }
                        }
                        if (StringUtils.contains(split, JSRCB_ACCEPT) && !StringUtils.contains(split, "交易")
                            && !StringUtils.equals(JSRCB_ACCEPT_NAME_TWO,
                                splits[i + 1].replace(separator, "").trim())) {
                            receiptDto.acceptName = splits[i + 1].replace(separator, "").trim();
                        }
                        if (StringUtils.contains(split, JSRCB_MONEY_THREE)) {
                            if (StringUtils.contains(content, JSRCB_FLAG_SIXTH)
                                || StringUtils.contains(content, JSRCB_FLAG_SEVEN)
                                || StringUtils.contains(content, JSRCB_FLAG_EIGHT)
                                || StringUtils.contains(content, JSRCB_FLAG_NT)
                                || StringUtils.contains(content, JSRCB_FLAG_NINE)
                                || StringUtils.contains(content, JSRCB_ACCEPTER)
                                || StringUtils.contains(content, JSRCB_TEN)
                                || StringUtils.contains(content, JSRCB_ELEVN)
                                || StringUtils.contains(content, JSRCB_TWLEVE)
                                || StringUtils.contains(content, JSRCB_THIRTEEN)) {
                                receiptDto.money = StringUtils.substringBefore(split, JSRCB_MONEY_THREE).trim();
                                if (StringUtils.contains(receiptDto.money, "_")) {
                                    receiptDto.money = StringUtils.remove(receiptDto.money, "_");
                                }
                            }
                        }
                    }
                }
                if (StringUtils.equals(account, receiptDto.payAccount)) {
                    receiptDto.money = "-" + receiptDto.money;
                    receiptDto.oppositeAccountName = receiptDto.acceptName;
                } else {
                    receiptDto.oppositeAccountName = receiptDto.payName;
                }
                receiptDtos.add(receiptDto);
            }
            // 当日期、金额都一样，并且含有 共2页发起渠道日期 的回单，认为是同一笔
            ArrayList<ReceiptDto> noManyPages = new ArrayList<>();
            ArrayList<ReceiptDto> total = new ArrayList<>();
            ArrayList<ReceiptDto> haveManyPages = new ArrayList<>();
            for (ReceiptDto receiptDto : receiptDtos) {
                if (receiptDto.isManyPage) {
                    haveManyPages.add(receiptDto);
                } else {
                    noManyPages.add(receiptDto);
                }
            }
            ArrayList<ReceiptDto> removeResult = new ArrayList<>();
            Set<String> seen = new HashSet<>();
            for (ReceiptDto receiptDto : haveManyPages) {
                String key = receiptDto.tradingDate + "_" + receiptDto.money;
                if (!seen.contains(key)) {
                    seen.add(key);
                    removeResult.add(receiptDto);
                }
            }
            total.addAll(noManyPages);
            total.addAll(removeResult);
            return total;
        } catch (Exception e) {
            throw new ServiceException("pdf转Excel失败", ErrorCode.BAD_REQUEST);
        }
    }

    /**
     * 中国邮政储蓄银行 一页一个
     */
    public ArrayList<ReceiptDto> transferPSBCPdfToExcel(String fileName, InputStream inputStream, String account) {
        try {
            PDDocument document = Loader.loadPDF(new RandomAccessReadBuffer(inputStream));
            List<PDDocument> documents = split(document);
            ArrayList<ReceiptDto> receipts = new ArrayList<>();
            for (PDDocument pdDocument : documents) {
                PDFTextStripper pdfTextStripper = new PDFTextStripper();
                String content = pdfTextStripper.getText(pdDocument);
                String separator = System.lineSeparator();
                String[] splits = content.split(separator);
                ReceiptDto receiptDto = new ReceiptDto();
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                pdDocument.save(baos);
                byte[] byteArray = baos.toByteArray();
                InputStream ins = new ByteArrayInputStream(byteArray);
                receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + ".pdf", ins);
                for (int i = 0; i < splits.length; i++) {
                    String split = splits[i];
                    if (StringUtils.contains(split, PSBC_RECEIPT_NO)) {
                        receiptDto.tradeNo = StringUtils.substringBetween(split, PSBC_RECEIPT_NO, PSBC_RECEIPT_NO_FLAG).trim();
                    }
                    if (StringUtils.contains(split, PSBC_PAYER)) {
                        receiptDto.payName = StringUtils.remove(splits[i + 1], PSBC_NAME_FLAG).replace(separator, "").trim();
                    }
                    if (StringUtils.contains(split, PSBC_ACCEPTER)) {
                        receiptDto.acceptName = StringUtils.remove(splits[i + 1], PSBC_NAME_FLAG).replace(separator, "").trim();
                    }
                    if (StringUtils.contains(split, PSBC_ACCOUNT)) {
                        receiptDto.payAccount = StringUtils.substringBetween(split, PSBC_ACCOUNT, " ").trim();
                        int lastAccountIndex = split.lastIndexOf(PSBC_ACCOUNT_FLAG);
                        receiptDto.acceptAccount = StringUtils.substring(split, lastAccountIndex + 1).replace(separator, "").trim();
                    }
                    if (StringUtils.contains(split, PSBC_MONEY)) {
                        receiptDto.money = splits[i + 2].replace(separator, "").trim();
                    }
                    if (StringUtils.contains(split, PSBC_DIGEST)) {
                        receiptDto.digest = StringUtils.substringAfter(split, PSBC_DIGEST).replace(separator, "").trim();
                    }
                    if (StringUtils.contains(split, PSBC_DATE)) {
                        receiptDto.tradingDate = StringUtils.substringBetween(split, PSBC_DATE, " ").trim();
                    }
                }
                if (StringUtils.contains(receiptDto.tradingDate, "-")) {
                    receiptDto.tradingDate = getDateStr(receiptDto.tradingDate);
                }
                if (StringUtils.equals(account, receiptDto.payAccount)) {
                    receiptDto.money = "-" + receiptDto.money;
                    receiptDto.oppositeAccountName = receiptDto.acceptName;
                } else {
                    receiptDto.oppositeAccountName = receiptDto.payName;
                }
                receipts.add(receiptDto);
            }
            return receipts;
        } catch (Exception e) {
            throw new ServiceException("pdf转Excel失败", ErrorCode.BAD_REQUEST);
        }
    }

    /**
     * 兴业银行 一页一个，一页两个
     */
    public ArrayList<ReceiptDto> transferCIBPdfToExcel(String fileName, InputStream inputStream, String account) {
        try {
            PDDocument document = Loader.loadPDF(new RandomAccessReadBuffer(inputStream));
            List<PDDocument> documents = split(document);
            ArrayList<ReceiptDto> receipts = new ArrayList<>();
            for (PDDocument pdDocument : documents) {
                PDFTextStripper pdfTextStripper = new PDFTextStripper();
                String content = pdfTextStripper.getText(pdDocument);
                int count = 0;
                for (String type : CIB_TYPE) {
                    if (StringUtils.contains(content, type)) {
                        int temp = StringUtils.countMatches(content, type);
                        if (temp > 1) {
                            count += temp;
                        } else {
                            count++;
                        }
                    }
                }
                List<InputStream> inputStreams = null;
                String[] receiptDtos = new String[count];
                if (count != 1) {
                    receiptDtos = content.split("第\\d+次打印");
                    inputStreams = pdfTransferImage(pdDocument, 2);
                } else {
                    receiptDtos[0] = content;
                }
                String separator = System.lineSeparator();
                for (int i = 0; i < receiptDtos.length; i++) {
                    String receipt = receiptDtos[i];
                    if (StringUtils.isEmpty(receipt) || StringUtils.isEmpty(receipt.replace(separator, "").trim())) {
                        continue;
                    }
                    ReceiptDto receiptDto = new ReceiptDto();
                    // 上传oss
                    if (count == 2) {
                        receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + (i + 1) + ".pdf", inputStreams.get(i));
                    } else {
                        ByteArrayOutputStream baos = new ByteArrayOutputStream();
                        pdDocument.save(baos);
                        byte[] byteArray = baos.toByteArray();
                        InputStream ins = new ByteArrayInputStream(byteArray);
                        receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + (i + 1) + ".pdf", ins);
                    }
                    String[] details = receipt.split(separator);
                    for (String detail : details) {
                        if (StringUtils.contains(detail, CIB_DATE)) {
                            receiptDto.tradingDate = StringUtils.substringAfter(detail, CIB_DATE).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(detail, CIB_DATE_TWO)) {
                            receiptDto.tradingDate = StringUtils.substringBetween(detail, CIB_DATE_TWO, " ").trim();
                        }
                        if (StringUtils.contains(detail, CIB_PAY_ACCOUNT)) {
                            receiptDto.payAccount = StringUtils.substringBetween(detail, CIB_PAY_ACCOUNT, CIB_ACCEPT_ACCOUNT).trim();
                            receiptDto.acceptAccount = StringUtils.substringAfter(detail, CIB_ACCEPT_ACCOUNT).trim();
                        }
                        if (StringUtils.contains(detail, CIB_PAY_ACCOUNT_TWO)) {
                            receiptDto.payAccount = StringUtils.substringBetween(detail, CIB_PAY_ACCOUNT_TWO, " ").trim();
                        }
                        if (StringUtils.contains(detail, CIB_PAYER)) {
                            receiptDto.payName = StringUtils.substringAfter(detail, CIB_PAYER).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(detail, CIB_PAYER_TWO)) {
                            receiptDto.payName = StringUtils.substringAfter(detail, CIB_PAYER_TWO).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(detail, CIB_ACCEPTER)) {
                            receiptDto.acceptName = StringUtils.substringAfter(detail, CIB_ACCEPTER).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(detail, CIB_MONEY) && StringUtils.contains(detail, CIB_MONEY_FLAG)) {
                            if (StringUtils.contains(receipt, CIB_MONEY_TWO)) {
                                receiptDto.money = StringUtils.substringBetween(detail, CIB_MONEY_FLAG, " ").trim();
                            } else {
                                receiptDto.money = StringUtils.substringAfter(detail, CIB_MONEY_FLAG).replace(separator, "").trim();
                            }
                        }
                        if (StringUtils.contains(detail, CIB_DIGEST)) {
                            receiptDto.digest = StringUtils.substringBetween(detail, CIB_DIGEST, CIB_DIGEST_FLAG).trim();
                        }
                        if (StringUtils.contains(detail, CIB_DIGEST) && StringUtils.contains(detail, CIB_NO)) {
                            String no = StringUtils.substringAfter(detail, CIB_NO).replace(separator, "").trim();
                            if (!no.isEmpty()) {
                                receiptDto.isHaveNo = true;
                            }
                        }
                        if (StringUtils.contains(detail, SHBANK_RECEIPT_NO)) {
                            receiptDto.tradeNo = StringUtils.substringAfter(detail, SHBANK_RECEIPT_NO).trim();
                        }
                    }
                    if (StringUtils.equals(account, receiptDto.payAccount)) {
                        receiptDto.money = "-" + receiptDto.money;
                        receiptDto.oppositeAccountName = receiptDto.acceptName;
                    } else {
                        receiptDto.oppositeAccountName = receiptDto.payName;
                    }
                    receipts.add(receiptDto);
                }
            }
            // 当日期、金额相同时，如果一个有序号，一个没有序号，就去掉那个没有序号的
			Map<String, ReceiptDto> map = new HashMap<>();
			for (ReceiptDto receipt : receipts) {
				String key = receipt.tradingDate + "_" + receipt.money;
				ReceiptDto existing = map.get(key);
				if (existing == null) {
					map.put(key, receipt);
				} else {
					// 如果已存在，检查isHaveNo
					if (receipt.isHaveNo && !existing.isHaveNo) {
						map.put(key, receipt);
					}
				}
			}
			return new ArrayList<>(map.values());
        } catch (Exception e) {
            throw new ServiceException("pdf转Excel失败", ErrorCode.BAD_REQUEST);
        }
    }

    /**
     * 邯郸银行
     */
    public ArrayList<ReceiptDto> transferHBPdfToExcel(String fileName, InputStream inputStream, String account) {
        try {
            PDDocument document = Loader.loadPDF(new RandomAccessReadBuffer(inputStream));
            List<PDDocument> documents = split(document);
            for (PDDocument pdDocument : documents) {
                PDFTextStripper pdfTextStripper = new PDFTextStripper();
                String content = pdfTextStripper.getText(pdDocument);
                System.out.println(content);
                System.out.println("11111111111111111111");
                String separator = System.lineSeparator();
                String[] splits = content.split(separator);
            }
            return null;
        } catch (Exception e) {
            throw new ServiceException("pdf转Excel失败", ErrorCode.BAD_REQUEST);
		}
    }

    /**
     * 苏州银行 pdf,一页一个
     */
    public ArrayList<ReceiptDto> transferSZBPdfToExcel(String fileName, InputStream inputStream, String account) {
        try {
            PDDocument document = Loader.loadPDF(new RandomAccessReadBuffer(inputStream));
            List<PDDocument> documents = split(document);
            ArrayList<ReceiptDto> receipts = new ArrayList<>();
            for (PDDocument pdDocument : documents) {
                PDFTextStripper pdfTextStripper = new PDFTextStripper();
                String text = pdfTextStripper.getText(pdDocument);
                String separator = System.lineSeparator();
                String[] splits = text.split(separator);
                ReceiptDto receiptDto = new ReceiptDto();
				ByteArrayOutputStream baos = new ByteArrayOutputStream();
				pdDocument.save(baos);
				byte[] byteArray = baos.toByteArray();
				InputStream ins = new ByteArrayInputStream(byteArray);
				receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + ".pdf", ins);
				for (int i = 0; i < splits.length; i++) {
                    String split = splits[i];
                    if (StringUtils.contains(split, ABC_RECEIPT_NO)) {
                        receiptDto.tradeNo = StringUtils.substringBetween(split, ABC_RECEIPT_NO, " ");
                    }
                    if (StringUtils.contains(split, CCB_NO)) {
                        Pattern pattern = Pattern.compile(PERIOD_CHAR);
                        Matcher matcher = pattern.matcher(split);
                        if (matcher.find()) {
							String originalDate = matcher.group();
							SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy年MM月dd日");
							SimpleDateFormat outputFormat = new SimpleDateFormat("yyyyMMdd");
							Date date = inputFormat.parse(originalDate);
							receiptDto.tradingDate = outputFormat.format(date);
                        } else {
                            throw new ServiceException("未找到日期", ErrorCode.BAD_REQUEST);
                        }
                    }
                    if (StringUtils.contains(split, SZB_NAME)) {
                        if (StringUtils.contains(splits[i - 1], "付")) {
                            receiptDto.payName = StringUtils.substringAfter(split, SZB_NAME).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(splits[i - 1], "收")) {
                            receiptDto.acceptName = StringUtils.substringAfter(split, SZB_NAME).replace(separator, "").trim();
                        }
                    }
                    if (StringUtils.contains(split, CCB_HM)) {
                        receiptDto.accountName = StringUtils.substringBetween(split, CCB_HM, " ").trim();
                        receiptDto.account = StringUtils.substringAfter(split, CCB_ZH).replace(separator, "").trim();
                    }
                    if (StringUtils.contains(split, SZB_ACCOUNT)) {
                        int first = split.indexOf(SZB_ACCOUNT);
                        int end = split.lastIndexOf(SZB_ACCOUNT);
                        receiptDto.payAccount = split.substring(first + SZB_ACCOUNT.length(), end).trim();
                        receiptDto.acceptAccount = split.substring(end + SZB_ACCOUNT.length()).replace(separator, "").trim();
                    }
                    if (StringUtils.contains(split, SZB_MONEY)) {
                        receiptDto.money = StringUtils.substringAfter(split, SZB_MONEY).replace(separator, "").trim();
                    }
                    if (StringUtils.contains(split, SZB_MONEY_TWO) && StringUtils.contains(split, CCB_MONEY)) {
                        receiptDto.money = StringUtils.substringAfter(split, CCB_MONEY).replace(separator, "").trim();
                    }
                    if (StringUtils.contains(split, JSRCB_DIGEST_FORTH)) {
                        receiptDto.digest = StringUtils.substringAfter(split, JSRCB_DIGEST_FORTH).replace(separator, "").trim();
						if (StringUtils.equals(receiptDto.digest, "手续费")||StringUtils.equals(receiptDto.digest, "工本费") || StringUtils.equals(receiptDto.digest, "归还利息")){
							receiptDto.payName = receiptDto.accountName;
							receiptDto.payAccount = receiptDto.account;
						}
					}
                }
                if (StringUtils.equals(account, receiptDto.payAccount)) {
                    receiptDto.money = "-" + receiptDto.money;
                    receiptDto.oppositeAccountName = receiptDto.acceptName;
                } else {
                    receiptDto.oppositeAccountName = receiptDto.payName;
                }
                receipts.add(receiptDto);
            }
            return receipts;
        } catch (Exception e) {
            throw new ServiceException("pdf转Excel失败", ErrorCode.BAD_REQUEST);
        }
    }

	/**
	 * 交通银行
	 */
	public ArrayList<ReceiptDto> transferBCMPdfToExcel(String fileName, InputStream inputStream, String account) {
		try{
			PDDocument document = Loader.loadPDF(new RandomAccessReadBuffer(inputStream));
			List<PDDocument> documents = split(document);
			ArrayList<ReceiptDto> receipts = new ArrayList<>();
			for (PDDocument pdDocument : documents) {
				PDFTextStripper pdfTextStripper = new PDFTextStripper();
				String text = pdfTextStripper.getText(pdDocument);
				String separator = System.lineSeparator();
				if(text.isEmpty()||StringUtils.equals(text,separator)||!StringUtils.contains(text,BCM_DATE)){
					continue;
				}
				String[] splits = text.split(separator);
				ReceiptDto receiptDto = new ReceiptDto();
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                pdDocument.save(baos);
                byte[] byteArray = baos.toByteArray();
                InputStream ins = new ByteArrayInputStream(byteArray);
                receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + ".pdf", ins);
                boolean isDate = false;
				for (int i = 0; i < splits.length; i++) {
                    String split = splits[i];
                    if (StringUtils.contains(split, BCM_DATE)&&!StringUtils.contains(splits[i + 1], BCM_DATE_TWO)) {
                        receiptDto.tradingDate = StringUtils.substringBefore(splits[i + 1], " ").trim();
                        receiptDto.tradingDate =
                            receiptDto.tradingDate.substring(0, receiptDto.tradingDate.length() - 1);
                    }
					if (StringUtils.contains(split, BCM_DATE)&& StringUtils.contains(splits[i + 1], BCM_DATE_TWO)) {
						isDate = true;
					}
                    if (StringUtils.contains(split, ABC_RECEIPT_NO)) {
                        receiptDto.tradeNo = StringUtils.substringAfter(split, ABC_RECEIPT_NO).replace(separator, "").trim();
						if(StringUtils.contains(receiptDto.tradeNo,PSBC_RECEIPT_NO_FLAG)){
							receiptDto.tradeNo = StringUtils.substringBefore(receiptDto.tradeNo," ");
						}
						if(isDate){
							receiptDto.tradingDate = StringUtils.substringBefore(splits[i + 1], " ").trim();
							receiptDto.tradingDate = receiptDto.tradingDate.substring(0, receiptDto.tradingDate.length() - 1);
						}
                    }
                    if (StringUtils.contains(split, BCM_PAY_ACCOUNT)) {
                        receiptDto.payAccount = StringUtils.substringBefore(split, BCM_PAY_ACCOUNT).trim();
                    }
                    if (StringUtils.contains(split, BCM_PAY_NAME)) {
                        receiptDto.payName = StringUtils.substringBefore(split, BCM_PAY_NAME).trim();
                    }
                    if (StringUtils.contains(split, BCM_ACCEPT_ACCOUNT)) {
                        receiptDto.acceptAccount = StringUtils.substringBefore(split, BCM_ACCEPT_ACCOUNT).trim();
                    }
                    if (StringUtils.contains(split, BCM_ACCEPT_NAME)) {
                        receiptDto.acceptName =
                            StringUtils.substringAfter(split, BCM_ACCEPT_NAME).replace(separator, "").trim();
                    }
                    if (StringUtils.contains(split, BCM_MONEY)) {
                        receiptDto.money = StringUtils.substringBetween(split, " ", BCM_MONEY).trim();
                    }
                    if (StringUtils.contains(split, CCB_DIGEST)) {
                        receiptDto.digest = StringUtils.substringBefore(split, CCB_DIGEST).trim();
                    }
                }
                if (StringUtils.equals(account, receiptDto.payAccount)) {
                    receiptDto.money = "-" + receiptDto.money;
                    receiptDto.oppositeAccountName = receiptDto.acceptName;
                } else {
                    receiptDto.oppositeAccountName = receiptDto.payName;
                }
                receipts.add(receiptDto);
			}
            return receipts;
		}catch(Exception e){
			throw new ServiceException("pdf转Excel失败", ErrorCode.BAD_REQUEST);
		}
	}

	/**
	 * 民生银行 pdf导入，一页一个
	 */

	public  ArrayList<ReceiptDto> transferCMBCPdfToExcel(String fileName, InputStream inputStream, String account) {
		try{
			PDDocument pdDocument = Loader.loadPDF(new RandomAccessReadBuffer(inputStream));
			List<PDDocument> pddDocuments = split(pdDocument);
			String separator = System.lineSeparator();
            ArrayList<ReceiptDto> receipts = new ArrayList<>();
			for (PDDocument pddDocument : pddDocuments) {
				PDFTextStripper pdfTextStripper = new PDFTextStripper();
                String text = pdfTextStripper.getText(pddDocument);
                ReceiptDto receiptDto = new ReceiptDto();
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                pddDocument.save(baos);
                byte[] byteArray = baos.toByteArray();
                InputStream ins = new ByteArrayInputStream(byteArray);
                receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + ".pdf", ins);
                String[] splits = text.split(separator);
                for (int i = 0; i < splits.length; i++) {
                    String split = splits[i];
                    if (StringUtils.contains(split, ABC_ZH)) {
                        receiptDto.payName = splits[i + 1].replace(separator, "").trim();
                        receiptDto.payAccount = splits[i + 2].replace(separator, "").trim();
                        if (StringUtils.contains(receiptDto.payName, CMBC_PAY_NAME_FLAG)) {
                            receiptDto.payName = "";
                            receiptDto.payAccount = "";
                        }
						if (StringUtils.contains(receiptDto.payAccount, CMBC_PAY_NAME_FLAG_TWO)) {
							receiptDto.payName = receiptDto.payName+CMBC_PAY_NAME_FLAG_TWO;
							receiptDto.payAccount = splits[i + 3].replace(separator, "").trim();;
						}
                        break;
                    }
                }
                for (int i = 0; i < splits.length; i++) {
                    String split = splits[i];
                    if (StringUtils.contains(split, CMBC_MONEY)) {
                        receiptDto.acceptName = splits[i - 1].replace(separator, "").trim();
                        receiptDto.acceptAccount = splits[i - 2].replace(separator, "").trim();
                        receiptDto.money = StringUtils.substringBetween(split, CMBC_MONEY, " ").trim();
                    }
                    if (StringUtils.contains(split, CMBC_DIGEST) && StringUtils.contains(split, CMBC_DIGEST_FLAG)) {
                        receiptDto.digest = StringUtils.substringBetween(split, CMBC_DIGEST, CMBC_DIGEST_FLAG).trim();
                    }
                    if (StringUtils.contains(split, CMBC_DIGEST_FLAG)) {
                        receiptDto.tradingDate = StringUtils.substringBetween(split, CMBC_DIGEST_FLAG, " ").trim();
                        receiptDto.tradingDate = getDateStr(receiptDto.tradingDate);
                    }
                    if (StringUtils.contains(split, TRADE_NO)) {
                        receiptDto.tradeNo = StringUtils.substringAfter(split, TRADE_NO).trim();
                    }
                }
                if (StringUtils.equals(account, receiptDto.payAccount)) {
                    receiptDto.money = "-" + receiptDto.money;
                    receiptDto.oppositeAccountName = receiptDto.acceptName;
                } else {
                    receiptDto.oppositeAccountName = receiptDto.payName;
                }
                receipts.add(receiptDto);
			}
            return receipts;
		}catch(Exception e){
			throw new ServiceException("PDF转化失败",ErrorCode.BAD_REQUEST);
		}
	}

	/**
	 * 长沙银行 pdf导入，一页三个
	 */
	public ArrayList<ReceiptDto> transferCSCBPdfToExcel(String fileName, InputStream inputStream, String account) {
		try{
			PDDocument pdDocument = Loader.loadPDF(new RandomAccessReadBuffer(inputStream));
			List<PDDocument> pdDocuments = split(pdDocument);
			String separator = System.lineSeparator();
			ArrayList<ReceiptDto> receiptDtos = new ArrayList<>();
			for (PDDocument document : pdDocuments) {
                List<InputStream> inputStreams = pdfTransferImage(document, 3);
				PDFTextStripper pdfTextStripper = new PDFTextStripper();
				String content = pdfTextStripper.getText(document);
				String[] splits = content.split("专用回单");
                for (int i = 0; i < splits.length; i++) {
                    String receipt = splits[i];
                    if (!StringUtils.contains(receipt, JSB_DATE)) {
                        continue;
                    }
					String[] rows = receipt.split(separator);
					ReceiptDto receiptDto = new ReceiptDto();
                    receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + (i - 1) + ".pdf", inputStreams.get(i - 1));
					for (String row : rows) {
						if(StringUtils.contains(row, JSB_DATE)){
							receiptDto.tradingDate = StringUtils.substringBetween(row, JSB_DATE, " ").trim();
						}
						if(StringUtils.contains(row, JSB_ACCEPTER_ACCOUNT) && StringUtils.contains(row, JSB_ACCEPTER_NAME)){
                            receiptDto.acceptAccount = StringUtils.substringBetween(row, JSB_ACCEPTER_ACCOUNT, " ").trim();
							receiptDto.acceptName = StringUtils.substringAfter(row, JSB_ACCEPTER_NAME).replace(separator,"").trim();
                            receiptDto.digest = "入息";
						}
						if(StringUtils.contains(row, CIB_PAY_ACCOUNT_TWO) && StringUtils.contains(row, JSB_ACCEPTER_ACCOUNT)){
							receiptDto.payAccount = StringUtils.substringBetween(row, CIB_PAY_ACCOUNT_TWO, " ").trim();
                            receiptDto.acceptAccount = StringUtils.substringAfter(row, JSB_ACCEPTER_ACCOUNT).replace(separator, "").trim();
						}
						if(StringUtils.contains(row, CIB_PAYER_TWO) && StringUtils.contains(row, CSCB_ACCEPTER)){
							receiptDto.payName = StringUtils.substringBetween(row, CIB_PAYER_TWO, " ").trim();
							receiptDto.acceptName = StringUtils.substringAfter(row, CSCB_ACCEPTER).replace(separator,"").trim();
						}
						if(StringUtils.contains(row, JSRCB_DIGEST_FIFTH)){
							receiptDto.digest =StringUtils.substringAfter(row, JSRCB_DIGEST_FIFTH).replace(separator,"").trim();
						}
						if(StringUtils.contains(row, CSCB_MONEY)){
							receiptDto.money =StringUtils.substringAfter(row, CSCB_MONEY).replace(separator,"").trim();
						}
						if(StringUtils.contains(row, CSCB_MONEY_TWO)){
							receiptDto.money =StringUtils.substringAfter(row, CSCB_MONEY_TWO).replace(separator,"").trim();
						}
						if(StringUtils.contains(row, CSCB_NO)){
							receiptDto.tradeNo = StringUtils.substringBetween(row, CSCB_NO, " ").trim();
						}
					}
					if (StringUtils.equals(account, receiptDto.payAccount)) {
						receiptDto.money = "-" + receiptDto.money;
						receiptDto.oppositeAccountName = receiptDto.acceptName;
					} else {
						receiptDto.oppositeAccountName = receiptDto.payName;
					}
					receiptDtos.add(receiptDto);
				}
			}
			return receiptDtos;
		}catch(Exception e){
			throw new ServiceException("PDF转化失败",ErrorCode.BAD_REQUEST);
		}
	}
	/**
	 * 平安 pdf导入，一页三个
	 */
	public ArrayList<ReceiptDto> transferPABPdfToExcel(String fileName, InputStream inputStream, String account) {
		try{
			PDDocument pdDocument = Loader.loadPDF(new RandomAccessReadBuffer(inputStream));
			List<PDDocument> pdDocuments = split(pdDocument);
			String separator = System.lineSeparator();
			ArrayList<ReceiptDto> receiptDtos = new ArrayList<>();
			for (PDDocument document : pdDocuments) {
				List<InputStream> inputStreams = pdfTransferImage(document, 3);
				PDFTextStripper pdfTextStripper = new PDFTextStripper();
				String content = pdfTextStripper.getText(document);
                String[] receipts = content.split(PAB_FLAG);
                for (int i = 1; i < receipts.length; i++) {
                    ReceiptDto receiptDto = new ReceiptDto();
                    String receipt = receipts[i];
					if(!StringUtils.contains(receipt, BEC_DATE)){
						continue;
					}
					receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + (i - 1) + ".pdf", inputStreams.get(i - 1));
                    String[] lines = receipt.split(separator);
                    for (String line : lines) {
                        if (StringUtils.contains(line, BEC_DATE) && StringUtils.contains(line, PAB_DATE)) {
                            receiptDto.tradingDate = StringUtils.substringBetween(line, BEC_DATE, PAB_DATE).trim();
                            receiptDto.tradeNo = StringUtils.substringBetween(line, PAB_DATE, " ").trim();
                        }
                        if (StringUtils.contains(line, SHBANK_PAYNAME) && StringUtils.contains(line, SHBANK_ACCEPTNAME)) {
                            receiptDto.payName = StringUtils.substringBetween(line, SHBANK_PAYNAME, SHBANK_ACCEPTNAME).trim();
                            receiptDto.acceptName = StringUtils.substringAfter(line, SHBANK_ACCEPTNAME).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(line, ICBC_PAYER_ACCOUNT) && StringUtils.contains(line, ICBC_ACCEPTER_ACCOUNT)) {
                            receiptDto.payAccount = StringUtils.substringBetween(line, ICBC_PAYER_ACCOUNT, ICBC_ACCEPTER_ACCOUNT).trim();
                            receiptDto.acceptAccount = StringUtils.substringAfter(line, ICBC_ACCEPTER_ACCOUNT).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(line, BCM_PAY_NAME)
                            && StringUtils.contains(line, BCM_PAY_ACCOUNT)) {
                            // 收费回单(付款通知) 回单凭证
                            receiptDto.payName = StringUtils.substringBetween(line, BCM_PAY_NAME, BCM_PAY_ACCOUNT).trim();
                            receiptDto.payAccount = StringUtils.substringAfter(line, BCM_PAY_ACCOUNT).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(line, PAB_MONEY)) {
                            receiptDto.money = StringUtils.substringAfter(line, PAB_MONEY).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(line, JSRCB_DIGEST_ONE)) {
                            receiptDto.digest = StringUtils.substringAfter(line, JSRCB_DIGEST_ONE).replace(separator, "").trim();
                        }
                    }
					if (StringUtils.contains(receiptDto.tradingDate, "-")) {
						receiptDto.tradingDate = getDateStr(receiptDto.tradingDate);
					}
					if (StringUtils.equals(account, receiptDto.payAccount)) {
						receiptDto.money = "-" + receiptDto.money;
						receiptDto.oppositeAccountName = receiptDto.acceptName;
					} else {
						receiptDto.oppositeAccountName = receiptDto.payName;
					}
					receiptDtos.add(receiptDto);
                }
			}
			return receiptDtos;
		}catch(Exception e){
			throw new ServiceException("PDF转化失败",ErrorCode.BAD_REQUEST);
		}
	}

    /**
     * 华夏银行 一页三个 一页两个
     */
    public ArrayList<ReceiptDto> transferHXBPdfToExcel(String fileName, InputStream inputStream, String account) {
        try {
            PDDocument pdDocument = Loader.loadPDF(new RandomAccessReadBuffer(inputStream));
            List<PDDocument> pdDocuments = split(pdDocument);
            String separator = System.lineSeparator();
            ArrayList<ReceiptDto> receiptDtos = new ArrayList<>();
            for (PDDocument document : pdDocuments) {
				List<InputStream> inputStreams = pdfTransferImage(document, 3);
                PDFTextStripper pdfTextStripper = new PDFTextStripper();
                String content = pdfTextStripper.getText(document);
                String[] receipts = content.split(CITIC_FLAG);
				for (int i = 1; i < receipts.length; i++) {
					String receipt = receipts[i];
					if(receipt.isEmpty()){
						continue;
					}
                    ReceiptDto receiptDto = new ReceiptDto();
					receiptDto.receiptUrl = uploadReceiptToOss(StringUtils.substringBefore(fileName, ".pdf") + (i - 1) + ".pdf", inputStreams.get(i - 1));
                    String[] lines = receipt.split(separator);
                    for (String line : lines) {
                        if (StringUtils.contains(line, HXB_DATE)) {
                            receiptDto.tradingDate = StringUtils.substringAfter(line, HXB_DATE).replace(separator, "").trim();
                        }
                        if (StringUtils.contains(receipt, HXB_FLAG)) {
                            if (StringUtils.contains(receipt, HXB_MONEY_FLAG)) {
                                if (StringUtils.contains(line, HXB_ACCOUNT) && StringUtils.contains(line, SHBANK_ACCOUNT)) {
                                    receiptDto.acceptAccount = StringUtils.substringAfter(line, SHBANK_ACCOUNT).replace(separator, "").trim();
                                }
                                if (StringUtils.contains(line, HXB_OTHER_ACCOUNT)) {
                                    receiptDto.payAccount = StringUtils.substringAfter(line, HXB_OTHER_ACCOUNT).replace(separator, "").trim();
                                    receiptDto.acceptName = StringUtils.substringBetween(line, HXB_NAME, HXB_OTHER_ACCOUNT).replace(separator, "").trim();
                                }
                                if (StringUtils.contains(line, HXB_OTHER_NAME)) {
                                    receiptDto.payName = StringUtils.substringBetween(line, HXB_OTHER_NAME, " ").trim();
                                }
                            } else if (StringUtils.contains(receipt, HXB_MONEY_FLAG_TWO)) {
                                if (StringUtils.contains(line, HXB_ACCOUNT) && StringUtils.contains(line, SHBANK_ACCOUNT)) {
                                    receiptDto.payAccount = StringUtils.substringAfter(line, SHBANK_ACCOUNT).replace(separator, "").trim();
                                }
                                if (StringUtils.contains(line, HXB_OTHER_ACCOUNT)) {
                                    receiptDto.acceptAccount = StringUtils.substringAfter(line, HXB_OTHER_ACCOUNT).replace(separator, "").trim();
                                    receiptDto.payName = StringUtils.substringBetween(line, HXB_NAME, HXB_OTHER_ACCOUNT).replace(separator, "").trim();
                                }
                                if (StringUtils.contains(line, HXB_OTHER_NAME)) {
                                    receiptDto.acceptName = StringUtils.substringBetween(line, HXB_OTHER_NAME, " ").trim();
                                }
                            }
                            if (StringUtils.contains(line, HXB_MONEY_TWO)) {
                                receiptDto.money = StringUtils.substringAfter(line, HXB_MONEY_TWO).replace(separator, " ").trim();
                            }
                            if (StringUtils.contains(line, HXB_NO_TWO)) {
                                receiptDto.tradeNo = StringUtils.substringAfter(line, HXB_NO).replace(separator, "").replace("!", "").trim();
                            }
                            if (StringUtils.contains(line, ICBC_DIGEST)) {
                                receiptDto.digest = StringUtils.substringAfter(line, ICBC_DIGEST).replace(separator, " ").trim();
                            }
                        } else if (StringUtils.contains(receipt, HXB_FLAG_TWO)) {
                            if (StringUtils.contains(line, HXB_ACCEPT_NAME_TWO) && StringUtils.contains(line, SHBANK_ACCOUNT)) {
                                receiptDto.acceptName = StringUtils.substringBetween(line, HXB_ACCEPT_NAME_TWO, SHBANK_ACCOUNT).trim();
                                receiptDto.acceptAccount = StringUtils.substringAfter(line, SHBANK_ACCOUNT).replace(separator, "").trim();
                            }
                            if (StringUtils.contains(line, HXB_DIGEST_TWO)) {
                                receiptDto.digest = StringUtils.substringBetween(line, HXB_DIGEST_TWO, " ").trim();
                            }
                            if (StringUtils.contains(line, HXB_MONEY_THREE)) {
                                receiptDto.money = StringUtils.substringBetween(line, HXB_MONEY_THREE, " ").trim();
                            }
                            if (StringUtils.contains(line, JSRCB_NO_THREE)) {
                                receiptDto.tradeNo = StringUtils.substringAfter(line, JSRCB_NO_THREE).replace(separator, "").trim();
                            }
                        } else if (StringUtils.contains(receipt, HXB_FLAG_THREE)) {
                            if (StringUtils.contains(line, HXB_PAY_ACCOUNT)) {
                                receiptDto.payAccount = StringUtils.substringBetween(line, HXB_PAY_ACCOUNT, " ").trim();
                            }
                            if (StringUtils.contains(line, HXB_PAY_NAME)
                                && StringUtils.contains(line, JSB_ACCEPTER_ACCOUNT)) {
                                receiptDto.payName = StringUtils.substringBetween(line, HXB_PAY_NAME, JSB_ACCEPTER_ACCOUNT).trim();
                                receiptDto.acceptAccount = StringUtils.substringAfter(line, JSB_ACCEPTER_ACCOUNT).replace(separator, "").trim();
                            }
                            if (StringUtils.contains(line, JSB_ACCEPTER_NAME)) {
                                receiptDto.acceptName = StringUtils.substringAfter(line, JSB_ACCEPTER_NAME).replace(separator, "").trim();
                            }
                            if (StringUtils.contains(line, HXB_MONEY)) {
                                receiptDto.money = StringUtils.substringAfter(line, HXB_MONEY).replace(separator, " ").trim();
                            }
                            if (StringUtils.contains(line, ICBC_DIGEST)) {
                                receiptDto.digest = StringUtils.substringAfter(line, ICBC_DIGEST).replace(separator, " ").trim();
                            }
                            if (StringUtils.contains(line, JSRCB_TRADE_NO_ONE)) {
                                receiptDto.tradeNo = StringUtils.substringAfter(line, JSRCB_TRADE_NO_ONE).replace(separator, "").trim();
                            }
                        } else {
                            if (StringUtils.contains(line, ICBC_PAYER)) {
                                receiptDto.payName = StringUtils.substringAfter(line, ICBC_PAYER).replace(separator, "").trim();
                            }
                            if (StringUtils.contains(line, JSB_PAYER_ACCOUNT)) {
                                receiptDto.payAccount = StringUtils.substringBetween(line, JSB_PAYER_ACCOUNT, " ").trim();
                            }
                            if (StringUtils.contains(line, HXB_ACCEPT_NAME)) {
                                receiptDto.acceptName = StringUtils.substringBetween(line, HXB_ACCEPT_NAME, HXB_MONEY).trim();
                                receiptDto.money = StringUtils.substringAfter(line, HXB_MONEY).replace(separator, "").trim();
                            }
                            if (StringUtils.contains(line, CSCB_NO)) {
                                receiptDto.tradeNo = StringUtils.substringBetween(line, CSCB_NO, " ").trim();
                            }
                        }
                    }
                    if (StringUtils.contains(receiptDto.tradingDate, "-")) {
                        receiptDto.tradingDate = getDateStr(receiptDto.tradingDate);
                    }
                    if (StringUtils.equals(account, receiptDto.payAccount)) {
                        receiptDto.money = "-" + receiptDto.money;
                        receiptDto.oppositeAccountName = receiptDto.acceptName;
                    } else {
                        receiptDto.oppositeAccountName = receiptDto.payName;
                    }
                    receiptDtos.add(receiptDto);
                }
            }
            return receiptDtos;
        } catch (Exception e) {
            throw new ServiceException("PDF转化失败", ErrorCode.BAD_REQUEST);
        }
    }
}
