package com.hongfund.efi.service;

import com.hongfund.efi.domain.*;
import com.hongfund.efi.dto.QxyResponseDto;
import com.hongfund.efi.dto.ResponseDto;
import com.hongfund.efi.profile.TaxRecordArea;
import com.hongfund.efi.repository.*;
import com.hongfund.efi.utils.IpUtils;
import com.hongfund.efi.utils.qixiangyun.QixiangyunUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;

import java.util.Date;

import static com.hongfund.efi.profile.TaxRecordArea.getByProvinceAndBookName;

/**
 * 企享云cookie
 */
@Service
@Transactional
public class QxyCookieService {

    @Autowired
    private AccountBookDao accountBookDao;
    @Autowired
    private AccountBookInfoDao accountBookInfoDao;
    @Autowired
    private QxyLogDao qxyLogDao;
    @Autowired
    private QxyOrderService qxyOrderService;
    @Autowired
    private RpaCookieStateDao rpaCookieStateDao;
    @Autowired
    private TaxReportDao taxReportDao;

    /**
     * 检查是否可以快速登录
     */
    public ResponseDto check(Long accountBookId, Long taxReportId, RpaCookieTask rpaCookieTask, HttpServletRequest request) {
        AccountBookInfo accountBookInfo;
        if (taxReportId != null) {
            TaxReport taxReport = taxReportDao.findTaxReportById(taxReportId);
            accountBookInfo = taxReport.createBook().accountBookInfo;
        }
        else if (rpaCookieTask == null || rpaCookieTask.source == 0) {
            AccountBook accountBook = accountBookDao.findAccountBookById(accountBookId);
            if (accountBook == null) {
                return new ResponseDto(2, "找不到公司", null);
            }
            accountBookInfo = accountBookInfoDao.findByAccountBookId(accountBookId);
            accountBookInfo.accountBookName = accountBook.bookName;
            if (!qxyOrderService.isOpen(accountBook.accounterNo, accountBookInfo.socialCreditCode)) {
                return new ResponseDto(4, "未开通该功能，请联系管理员开通", null);
            }
        }
        else {
            accountBookInfo = rpaCookieTask.parseInfo();
        }
        TaxRecordArea taxRecordArea = getByProvinceAndBookName(accountBookInfo.province, accountBookInfo.accountBookName);
        if (taxRecordArea == null) {
            return new ResponseDto(3, "找不到区域", null);
        }
        RpaCookieState rpaCookieState = rpaCookieStateDao.findBySocialCreditCodeAndLoginWayAndOpenState(accountBookInfo.nationalNameFinal, accountBookInfo.loginWay, 1);
        Date gmtCreate = new Date();
        QxyResponseDto responseDto = QixiangyunUtils.cacheCookie(accountBookInfo, taxRecordArea);
        if (responseDto.isSuccess()) {
            responseDto.data = true;
            responseDto.message = "true";
        }
        else {
            responseDto.data = false;
        }
        Date gmtModified = new Date();
        saveLog(gmtCreate, gmtModified, responseDto, accountBookInfo, request, "cache", taxRecordArea.areaId);
        Boolean isOk = (Boolean) responseDto.data;
        if (isOk == null) {
            isOk = false;
        }
        if (!isOk) {
            Date gmtCreate2 = new Date();
            responseDto = QixiangyunUtils.checkAppCookie(accountBookInfo, taxRecordArea);
            Date gmtModified2 = new Date();
            saveLog(gmtCreate2, gmtModified2, responseDto, accountBookInfo, request, "check", taxRecordArea.areaId);
            if (StringUtils.equals(responseDto.message, "可以快速登录")) {
                isOk = true;
            }
        }
        if (rpaCookieState != null) {
            rpaCookieState.gmtModified = new Date();
            if (isOk) {
                rpaCookieState.state = 1;
            }
            else {
                rpaCookieState.state = 0;
            }
            rpaCookieStateDao.save(rpaCookieState);
        }
        return responseDto;
    }

    /**
     * 登录（快速登录或发短信）
     */
    public ResponseDto login(Long accountBookId, Long taxReportId, RpaCookieTask rpaCookieTask, HttpServletRequest request) {
        AccountBookInfo accountBookInfo;
        if (taxReportId != null) {
            TaxReport taxReport = taxReportDao.findTaxReportById(taxReportId);
            accountBookInfo = taxReport.createBook().accountBookInfo;
        }
        else if (rpaCookieTask == null || rpaCookieTask.source == 0) {
            AccountBook accountBook = accountBookDao.findAccountBookById(accountBookId);
            if (accountBook == null) {
                return new ResponseDto(2, "找不到公司", null);
            }
            accountBookInfo = accountBookInfoDao.findByAccountBookId(accountBookId);
            accountBookInfo.accountBookName = accountBook.bookName;
            if (!qxyOrderService.isOpen(accountBook.accounterNo, accountBookInfo.socialCreditCode)) {
                return new ResponseDto(4, "未开通该功能，请联系管理员开通", null);
            }
        }
        else {
            accountBookInfo = rpaCookieTask.parseInfo();
        }
        TaxRecordArea taxRecordArea = getByProvinceAndBookName(accountBookInfo.province, accountBookInfo.accountBookName);
        if (taxRecordArea == null) {
            return new ResponseDto(3, "找不到区域", null);
        }
        Date gmtCreate2 = new Date();
        QxyResponseDto responseDto2 = QixiangyunUtils.cacheCookie(accountBookInfo, taxRecordArea);
        Date gmtModified2 = new Date();
        saveLog(gmtCreate2, gmtModified2, responseDto2, accountBookInfo, request, "cacheLogin", taxRecordArea.areaId);
        if (responseDto2.isSuccess()) {
            return responseDto2;
        }

        Date gmtCreate = new Date();
        QxyResponseDto responseDto = QixiangyunUtils.loginCookie(accountBookInfo, taxRecordArea);
        Date gmtModified = new Date();
        saveLog(gmtCreate, gmtModified, responseDto, accountBookInfo, request, "login", taxRecordArea.areaId);
        return responseDto;
    }

    /**
     * 登录（上送短信验证码）
     */
    public ResponseDto pushSms(Long accountBookId, String smsCode, String taskId, RpaCookieTask rpaCookieTask, HttpServletRequest request) {
        AccountBookInfo accountBookInfo;
        TaxRecordArea taxRecordArea;
        if (rpaCookieTask == null || rpaCookieTask.source == 0) {
            AccountBook accountBook = accountBookDao.findAccountBookById(accountBookId);
            if (accountBook == null) {
                return new ResponseDto(2, "找不到公司", null);
            }
            accountBookInfo = accountBookInfoDao.findByAccountBookId(accountBookId);
            taxRecordArea = getByProvinceAndBookName(accountBookInfo.province, accountBook.bookName);
            if (taxRecordArea == null) {
                return new ResponseDto(3, "找不到区域", null);
            }
            if (!qxyOrderService.isOpen(accountBook.accounterNo, accountBookInfo.socialCreditCode)) {
                return new ResponseDto(4, "未开通该功能，请联系管理员开通", null);
            }
        }
        else {
            accountBookInfo = rpaCookieTask.parseInfo();
            taxRecordArea = getByProvinceAndBookName(accountBookInfo.province, rpaCookieTask.accountBookName);
            if (taxRecordArea == null) {
                return new ResponseDto(3, "找不到区域", null);
            }
        }
        Date gmtCreate = new Date();
        QxyResponseDto responseDto = QixiangyunUtils.pushSms(smsCode, taskId, accountBookInfo);
        Date gmtModified = new Date();
        saveLog(gmtCreate, gmtModified, responseDto, accountBookInfo, request, "sms", taxRecordArea.areaId);
        return responseDto;
    }

    public void saveLog(Date gmtCreate, Date gmtModified, QxyResponseDto responseDto, AccountBookInfo accountBookInfo, HttpServletRequest request, String uri, Integer areaId) {
        QxyLog qxyLog = new QxyLog();
        qxyLog.socialCreditCode = accountBookInfo.socialCreditCode;
        qxyLog.socialCreditCodeAgent = accountBookInfo.socialCreditCodeAgent;
        qxyLog.areaId = areaId;
        qxyLog.city = accountBookInfo.city;
        qxyLog.uri = uri;
        qxyLog.ip = IpUtils.getIp(request);
        qxyLog.reqId = responseDto.reqId;
        qxyLog.message = responseDto.message;
        qxyLog.gmtCreate = gmtCreate;
        qxyLog.gmtModified = gmtModified;
        qxyLog.loginWay = accountBookInfo.loginWay;
        if (qxyLog.loginWay == 3) {
            qxyLog.phone = accountBookInfo.taxPayerPhoneAgent;
            qxyLog.nationalName = accountBookInfo.nationalNameAgent;
            qxyLog.nationalPwd = accountBookInfo.nationalPwdAgent;
        }
        else {
            qxyLog.phone = accountBookInfo.taxPayerPhoneNew;
            qxyLog.nationalName = accountBookInfo.nationalNameNew;
            qxyLog.nationalPwd = accountBookInfo.nationalPwdNew;
        }
        qxyLogDao.save(qxyLog);
    }
}
