package com.hongfund.efi.service;

import com.hongfund.efi.domain.AppCompany;
import com.hongfund.efi.domain.AppUser;
import com.hongfund.efi.dto.AppCompanyDto;
import com.hongfund.efi.dto.AppServiceTypeDto;
import com.hongfund.efi.dto.ResponseDto;
import com.hongfund.efi.profile.AppServiceType;
import com.hongfund.efi.repository.AppCompanyDao;
import com.hongfund.efi.repository.AppUserDao;
import com.hongfund.efi.service.exception.ErrorCode;
import com.hongfund.efi.service.exception.ServiceException;
import com.hongfund.efi.utils.BeanMapper;
import com.hongfund.efi.utils.RamPager;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class AppCompanyService {
    private static Logger logger = LoggerFactory.getLogger(AccountBookService.class);

    @Autowired
    private AppCompanyDao appCompanyDao;

    @Autowired
    private AppUserDao appUserDao;

    /**
     * 保存公司信息
     */
    public ResponseDto saveAppCompany(AppCompanyDto appCompanyDto) {
        if (appCompanyDto.userId == null){
            logger.info("没有找到该用户！");
            throw new ServiceException("没有找到该用户！", ErrorCode.BAD_REQUEST);
        }
        if (StringUtils.isBlank(appCompanyDto.name)){
            logger.info("公司名称不能为空！");
            throw new ServiceException("公司名称不能为空！", ErrorCode.BAD_REQUEST);
        }else {
            appCompanyDto.name = appCompanyDto.name.trim();
        }
        if (StringUtils.isBlank(appCompanyDto.taxCode)){
            logger.info("税号不能为空！");
            throw new ServiceException("税号不能为空！", ErrorCode.BAD_REQUEST);
        }
        // 如果是新增的第一个公司，则该用户的当前使用公司为该公司
        List<AppCompany> appCompanyList = appCompanyDao.findByUserId(appCompanyDto.userId);
        appCompanyDto.taxCode = appCompanyDto.taxCode.trim();
        appCompanyDto.addtax = StringUtils.trim(appCompanyDto.addtax);
        appCompanyDto.accountingSystem = StringUtils.trim(appCompanyDto.accountingSystem);
        appCompanyDto.nationalName = StringUtils.trim(appCompanyDto.nationalName);
        appCompanyDto.nationalPwd = StringUtils.trim(appCompanyDto.nationalPwd);
        appCompanyDto.taxPayerPhone = StringUtils.trim(appCompanyDto.taxPayerPhone);
        appCompanyDto.taxAuthorities = StringUtils.trim(appCompanyDto.taxAuthorities);
        appCompanyDto.province = StringUtils.trim(appCompanyDto.province);
        appCompanyDto.city = StringUtils.trim(appCompanyDto.city);
        appCompanyDto.socialCreditCodeAgent = StringUtils.trim(appCompanyDto.socialCreditCodeAgent);
        AppCompany saveAppCompany = appCompanyDao.save(BeanMapper.map(appCompanyDto,AppCompany.class));
        if (appCompanyList.size() == 0) {
            AppUser appUser = appUserDao.findOne(appCompanyDto.userId);
            appUser.companyId = saveAppCompany.id;
            appUserDao.save(appUser);
        }

        return new ResponseDto(200,"保存成功",BeanMapper.map(saveAppCompany,AppCompanyDto.class));
    }


    /**
     * 查询某用户的所有公司信息
     */
    public ResponseDto findAppCompanyList(Long userId, Pageable pageable) {
        if (userId == null){
            logger.info("没有找到该用户！");
            throw new ServiceException("没有找到该用户！", ErrorCode.BAD_REQUEST);
        }
        AppUser appUser = appUserDao.findOne(userId);
        if (appUser == null){
            logger.info("没有找到该用户！");
            throw new ServiceException("没有找到该用户！", ErrorCode.BAD_REQUEST);
        }

        List<AppCompany> appCompanies = appCompanyDao.findByUserId(userId);
        List<AppCompanyDto> appCompanyDtos = BeanMapper.mapList(appCompanies, AppCompanyDto.class);
        for (AppCompanyDto appCompanyDto : appCompanyDtos){
            if (appUser.companyId != null && appUser.companyId.equals(appCompanyDto.id)) {
                appCompanyDto.isDefault = 1;
            }
        }
        RamPager<AppCompanyDto> pager = new RamPager<>(appCompanyDtos, pageable);
        return new ResponseDto(200,"查询成功", pager.pageResult());
    }

    /**
     * 查询指定id的公司信息
     */
    public ResponseDto findAppCompanyById(Long id) {
        AppCompany appCompany = appCompanyDao.findOne(id);
        if (appCompany == null){
            logger.info("无该公司信息！");
            throw new ServiceException("无该公司信息！", ErrorCode.BAD_REQUEST);
        }
        return new ResponseDto(200,"查询成功",BeanMapper.map(appCompany,AppCompanyDto.class));
    }


    /**
     * 删除公司信息
     */
    public ResponseDto deleteAppCompany(Long id) {

        AppCompany appCompany = appCompanyDao.findOne(id);
        if (appCompany != null){
            appCompany.status = 1;
            appCompanyDao.save(appCompany);
        }else {
            logger.info("无该公司信息！");
            throw new ServiceException("无该公司信息！", ErrorCode.BAD_REQUEST);
        }
        return new ResponseDto(200,"删除成功",null);
    }

    /**
     * 查询公司服务类型列表
     */
    public ResponseDto findAppCompanyService() {
        // 返回服务信息列表
        List<AppServiceTypeDto> appServiceTypes = new ArrayList<>();
        for (AppServiceType appServiceType : AppServiceType.values()) {
            AppServiceTypeDto dto = new AppServiceTypeDto(
                appServiceType.typeCode,
                appServiceType.codeValue,
                appServiceType.price
            );
            appServiceTypes.add(dto);
        }
        return new ResponseDto(200, "查询成功", appServiceTypes);
    }
}
