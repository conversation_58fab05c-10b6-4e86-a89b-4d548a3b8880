package com.hongfund.efi.service;

import com.google.gson.JsonSyntaxException;
import com.hongfund.efi.domain.AppCompany;
import com.hongfund.efi.domain.AppOrders;
import com.hongfund.efi.dto.AppOrdersDto;
import com.hongfund.efi.dto.ResponseDto;
import com.hongfund.efi.dto.TaxReportDto;
import com.hongfund.efi.repository.AppCompanyDao;
import com.hongfund.efi.repository.AppOrdersDao;
import com.hongfund.efi.utils.BeanMapper;
import com.hongfund.efi.utils.FormatUtils;
import com.hongfund.efi.utils.RamPager;
import com.hongfund.efi.utils.wxUtitls.*;
import com.hongfund.efi.utils.wxUtitls.WxAppPrepay.CommonPrepayRequest;
import com.hongfund.efi.utils.wxUtitls.WxAppPrepay.DirectAPIv3AppPrepayResponse;
import com.hongfund.efi.utils.wxUtitls.domain.AppPayerInfo;
import com.hongfund.efi.utils.wxUtitls.repository.AppPayerInfoDao;
import com.hongfund.efi.utils.wxUtitls.response.PrepayResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.PublicKey;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.hongfund.efi.profile.AppServiceType.*;
import static com.hongfund.efi.utils.BigDecimalUtils.convertYuanToFen;
import static com.hongfund.efi.utils.Ids.randomNum4;
import static com.hongfund.efi.utils.wxUtitls.PaymentHelper.TRADE_TYPE_JSAPI;

/**
 * 订单业务逻辑服务
 * <AUTHOR>
 */
@Service
@Transactional(readOnly = true, rollbackFor = Exception.class)
public class AppOrdersService {

    private static final Logger logger = LoggerFactory.getLogger(AppOrdersService.class);

    // 订单状态常量
    private static final int ORDER_STATUS_PENDING = 1;  // 待支付
    private static final int ORDER_STATUS_PAID = 2;     // 已支付
    private static final int ORDER_STATUS_CANCELLED = 3; // 已取消

    // 订单超时时间（24小时）
    private static final long ORDER_TIMEOUT_MILLIS = 24 * 60 * 60 * 1000L;

    // 微信小程序支付配置
    private String mchid = "1720085695";
    private String certificateSerialNo = "5A2E6A7285E89EDDB1C4EDCB529A6E02A79F01A1";
    private String privateKey = "-----BEGIN PRIVATE KEY-----\n" +
            "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDNFJuOCAY82lcx\n" +
            "4qTslWTksfxdu0MW6w8DPldIoCn1Zt83+Lkayxl39md1PdbZN7cU48D2KeZOxxSH\n" +
            "hQSHG5SqBnGzF/OCsIl28I65cdbm4b0GDI+MM9/zHVytkJHSXbi/7Q/rJ5cDsjGC\n" +
            "Z/sPoN23EOPpPRFiwNHDIlTqIh5SU1Y4WRgPBNf1cjfN/HBBIGgU1G6MYWTJvcF9\n" +
            "mdx7DNiuyJDt0kJGtQ/GhpRyq6j7aaFFks5hfEiiVxpJTRtiXyBYssoGaNjVJJWY\n" +
            "0RqynPrfF019P3KQCV/RsTl860LdfWehRvKeDxy3etUiydhbs42OKdB3azMGT2Z5\n" +
            "DPiprgPHAgMBAAECggEBAL0ss0lTMZ3UsvY7YXMzY/xqCqfT49EyR4jwasd9pvbO\n" +
            "TD5vWyDSFM5KJ6lIv/VjpajJZugsQlyM2+0e0MxorrwgcaWJBIGbo1JqGN4Lmvcj\n" +
            "aKA52fi0qXKztRgN8dWhypiQb0y2GmG4cF8g18DU3XDPTc2LHYyA3X4pHzdS/bFl\n" +
            "sjL4seqmjVe6Nn9Q7dxjli3WnCi4xSuUQF6TcPj+BBiKNTFz4mBO1o7/79Ne78Ui\n" +
            "csi+zvr/kn1fMGRicJIlUKrnPsjVxnhskP+9/AsTK7i9hehKSJcnS/OjmsaumEeY\n" +
            "tBwYCDtVJ7kqYjN9/KvRPg82UtUXbOYAObE008cJXDECgYEA+X9mZp+pga5KuRJZ\n" +
            "Jb/Fbo7MVyLAOWWnm0ExmDDGztXIcAaASQh7zkxeoP+ixWdyujmwk+2+ce/XgyI2\n" +
            "kfZmzGGu5pRLy5JBmVrWzUt+fV3Tg771ZS9RP8uqiw+DaVb9sA4xl5vpfSE4kZtj\n" +
            "DFl696Q+lgKh0YP6Kf1pKwLxXfUCgYEA0mzdbnit8Ak2SjVrbEIVfZZFtIuhMgMv\n" +
            "DsF1wptuIsDcopG5tKiEHJ6jtWgUrGAHtLM84Di3joBadCsDpXYBrt1LYPODRSYl\n" +
            "Z1Nzm7r8JOISTtae4GQ8BT+PQTTrHnbaHn1O8ey0lpuyFgHZYpnqBFNO/vQnk3AR\n" +
            "iM1BfPT2aUsCgYAvg8y7pJKCiO/gGRdzMfdmm7lcqtiS1cI6tgq84iUqhNo8Q3Cw\n" +
            "9/WkDzVCUgzsrUvTHWN8eJ15U2Tmq2BbIeLn736AmSNqLHtjukIILOV0+KZsg7R2\n" +
            "RbGihaX//79WE2QS4FEff2jTFL2SNVaCppPdj6D/rpaksQ62anVZ5KCU9QKBgH8F\n" +
            "mnFaRd01ImGPd6YlCiYCdVd0Rj0TcO+eVsC/5K7z7vvvrcNhSkageMBD8N7RJTWu\n" +
            "UNwlQKCz2GZxpMTXxFJTYXJH7QDr8v45gPsL17NICLcwlT1tJFIiuQ4qrJd6NVbc\n" +
            "W7BsK+MSX2ErgpcjxAESbQg4SD+XEe6bwKF2mdybAoGBAN4McYx8zUCXz6iaXMg7\n" +
            "pSNeakfkZT5OWD+4bpED1jnTCkloxdcO/Z7c+a67Cq58HBrl1rIU53BKZSLu2Ih7\n" +
            "Ujm3caz6mSrk3jfWcqC2mlqEFx14VSsTQLjTPMKPi8Al1bynjkdWw7TMx6VCD1R0\n" +
            "GzXKuVU93c+BGSCilxh6lafr\n" +
            "-----END PRIVATE KEY-----\n";
    private String wechatPayPublicKeyId = "PUB_KEY_ID_0117200856952025080500382072001202";
    private String wechatPayPublicKey = "-----BEGIN PUBLIC KEY-----\n" +
            "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmhYxrBlEJ1NwsFoo/R/H\n" +
            "HDoSDgtq9GKMYr9t5Y/H0cDYLncD2Jg7KPw8SkMpI0naPJqiCdm4y75JVNE6tQ+s\n" +
            "ZyZUuMvgKL5ssKec9SPPF7sf6wOtxfQr9GX7OGH0exkXdrtogjnGc/4DFmESu6pP\n" +
            "HleEDxNs+/PgecpBiHGvU4IXOwUFpVF01SwtbNNSjV67eyEOjSBedMCHVHvahQc/\n" +
            "piX8XGIlQXQmuEvfOqCq1X0nJlIwyL0hmOOGIJu+jBIauu5Zmh/6SjlaKJ2otVfn\n" +
            "MF0EuPT4hA0tbU/AMMg8eHxniKu+unU0d+p46xg9+gQbety7LQsNiWNeLQiv9CCS\n" +
            "PQIDAQAB\n" +
            "-----END PUBLIC KEY-----\n";
    private String apiV3Key = "K7xP9wQ2rT8vN5mL3dA1sE6cR4bH0jF1";
    private String appid = "wx8a7871a7d7c7ec39";
    private String platformCert = "-----BEGIN CERTIFICATE-----\n" +
            "MIIEKzCCAxOgAwIBAgIUWi5qcoXont2xxO3LUppuAqefAaEwDQYJKoZIhvcNAQEL\n" +
            "BQAwXjELMAkGA1UEBhMCQ04xEzARBgNVBAoTClRlbnBheS5jb20xHTAbBgNVBAsT\n" +
            "FFRlbnBheS5jb20gQ0EgQ2VudGVyMRswGQYDVQQDExJUZW5wYXkuY29tIFJvb3Qg\n" +
            "Q0EwHhcNMjUwODA1MDMzNTUzWhcNMzAwODA0MDMzNTUzWjCBhDETMBEGA1UEAwwK\n" +
            "MTcyMDA4NTY5NTEbMBkGA1UECgwS5b6u5L+h5ZWG5oi357O757ufMTAwLgYDVQQL\n" +
            "DCfml6DplKHkuK3op4bnlYzmmbrog73np5HmioDmnInpmZDlhazlj7gxCzAJBgNV\n" +
            "BAYTAkNOMREwDwYDVQQHDAhTaGVuWmhlbjCCASIwDQYJKoZIhvcNAQEBBQADggEP\n" +
            "ADCCAQoCggEBAM0Um44IBjzaVzHipOyVZOSx/F27QxbrDwM+V0igKfVm3zf4uRrL\n" +
            "GXf2Z3U91tk3txTjwPYp5k7HFIeFBIcblKoGcbMX84KwiXbwjrlx1ubhvQYMj4wz\n" +
            "3/MdXK2QkdJduL/tD+snlwOyMYJn+w+g3bcQ4+k9EWLA0cMiVOoiHlJTVjhZGA8E\n" +
            "1/VyN838cEEgaBTUboxhZMm9wX2Z3HsM2K7IkO3SQka1D8aGlHKrqPtpoUWSzmF8\n" +
            "SKJXGklNG2JfIFiyygZo2NUklZjRGrKc+t8XTX0/cpAJX9GxOXzrQt19Z6FG8p4P\n" +
            "HLd61SLJ2FuzjY4p0HdrMwZPZnkM+KmuA8cCAwEAAaOBuTCBtjAJBgNVHRMEAjAA\n" +
            "MAsGA1UdDwQEAwID+DCBmwYDVR0fBIGTMIGQMIGNoIGKoIGHhoGEaHR0cDovL2V2\n" +
            "Y2EuaXRydXMuY29tLmNuL3B1YmxpYy9pdHJ1c2NybD9DQT0xQkQ0MjIwRTUwREJD\n" +
            "MDRCMDZBRDM5NzU0OTg0NkMwMUMzRThFQkQyJnNnPUhBQ0M0NzFCNjU0MjJFMTJC\n" +
            "MjdBOUQzM0E4N0FEMUNERjU5MjZFMTQwMzcxMA0GCSqGSIb3DQEBCwUAA4IBAQBs\n" +
            "7RfaCxLaZqgu+0i5VGMZMgPs1qoqD3vQJJ3QdTYcQZFUh/QjKgH2zltp81WIjzkE\n" +
            "5EyF/BI5o1RRwMCKMj2qH2bznFT+DUBT/wTq98bz6W7/OmCfSfHw/0k4auR0d3L1\n" +
            "cS25d2FbsKLpGuM9j2h+3iSU7W65pUglvDFxwy5bFBunTWH5kfpArf2K/Apb/yjD\n" +
            "StBtQTNmvHDRnElnvX1cz8W6CeQypIGLT2u2TGqkeuwENMqbb0Btsi+FNTUPDmgO\n" +
            "5gZ+qy7XWMbcNvBLukLJJgGr+JsJpngCKXwFt7MkA6oFCNye6uxkLuL1e58i73Mu\n" +
            "zg9vr3TONtOxdmqVH45R\n" +
            "-----END CERTIFICATE-----\n";

    private String notifyUrl = "https://fc.hongfund.com:8602/api/app/orders/notify";
    @Autowired
    private AppOrdersDao appOrdersDao;

    @Autowired
    private AppOrdersLogService appOrdersLogService;

    @Autowired
    private AppCompanyDao appCompanyDao;

    @Autowired(required = false)
    private RedissonClient redissonClient;

    @Autowired
    private AppPayerInfoDao appPayerInfoDao;


    /**
     * 创建订单
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDto createOrder(AppOrdersDto orderDto) {
        // 查询openid
        AppPayerInfo payer = appPayerInfoDao.findByUserId(orderDto.userId);
        if (payer == null) {
            return new ResponseDto(400, "用户未绑定微信，请先调用获取支付者信息接口", null);
        }
        orderDto.payerOpenid = payer.openId;
        try {
            // 参数验证
            ResponseDto validationResult = validateOrderParams(orderDto);
            if (validationResult != null) {
                return validationResult;
            }

            // 重新支付逻辑
            if (orderDto.id != null) {
                return handleRepayment(orderDto);
            }

            // 创建新订单逻辑
            return handleNewOrder(orderDto);

        } catch (Exception e) {
            logger.error("创建订单异常，用户ID：{}，公司ID：{}，服务ID：{}",
                    orderDto.userId, orderDto.companyId, orderDto.serviceId, e);
            appOrdersLogService.createLog(orderDto.userId, orderDto.id, orderDto.companyId,
                    "创建订单系统异常：" + e.getMessage());
            return new ResponseDto(500, "系统异常，请稍后重试", null);
        }
    }

    /**
     * 验证订单参数
     */
    private ResponseDto validateOrderParams(AppOrdersDto orderDto) {
        if (orderDto.userId == null) {
            return new ResponseDto(400, "用户ID不能为空", null);
        }
        if (orderDto.companyId == null) {
            return new ResponseDto(400, "公司ID不能为空", null);
        }
        if (orderDto.serviceId == null) {
            return new ResponseDto(400, "服务ID不能为空", null);
        }
        if (orderDto.amount == null || orderDto.amount.compareTo(BigDecimal.ZERO) <= 0) {
            return new ResponseDto(400, "订单金额必须大于0", null);
        }

        // 验证支付类型和支付者信息
        ResponseDto payerValidation = validatePayerInfo(orderDto);
        if (payerValidation != null) {
            return payerValidation;
        }

        // 验证微信支付配置
        if (StringUtils.isAnyBlank(mchid, certificateSerialNo, privateKey,
                wechatPayPublicKeyId, wechatPayPublicKey, apiV3Key, appid, notifyUrl)) {
            logger.error("微信支付配置不完整");
            return new ResponseDto(500, "支付配置异常，请联系管理员", null);
        }
        return null;
    }

    /**
     * 验证支付者信息
     */
    private ResponseDto validatePayerInfo(AppOrdersDto orderDto) {
        String validationError = PaymentHelper.validatePaymentParams(orderDto.tradeType, orderDto.payerOpenid);
        if (validationError != null) {
            return new ResponseDto(400, validationError, null);
        }
        return null;
    }

    /**
     * 处理重新支付
     */
    private ResponseDto handleRepayment(AppOrdersDto orderDto) {
        AppOrders existingOrder = appOrdersDao.findOne(orderDto.id);
        if (existingOrder == null) {
            appOrdersLogService.createLog(orderDto.userId, orderDto.id, orderDto.companyId, "订单不存在！");
            return new ResponseDto(400, "订单不存在！", null);
        }

        if (existingOrder.orderStatus != ORDER_STATUS_PENDING) {
            appOrdersLogService.createLog(existingOrder.userId, existingOrder.id,
                    existingOrder.companyId, "非待支付订单，状态：" + existingOrder.orderStatus);
            return new ResponseDto(400, "订单状态不允许重新支付", null);
        }

        try {
            // 使用现有订单信息创建微信预支付订单
            orderDto.orderNo = existingOrder.orderNo;
            orderDto.serviceName = existingOrder.serviceName;

            DirectAPIv3AppPrepayResponse response = createWechatPrepayOrder(orderDto);

            // 返回预支付订单信息
            PrepayResponse prepayResponse = new PrepayResponse();
            prepayResponse.appid = appid;
            prepayResponse.partnerId = mchid;
            prepayResponse.prepayId = response.prepayId;
            prepayResponse.packageValue = response;
            prepayResponse.nonceStr = WXPayUtility.createNonce(32);
            prepayResponse.timestamp = String.valueOf(new Date().getTime());
            prepayResponse.orderId = orderDto.orderNo;
            String prepayId = "prepay_id=" + prepayResponse.prepayId;
            String verifyString = prepayResponse.appid + "\n" + prepayResponse.timestamp + "\n" + prepayResponse.nonceStr + "\n" +
                    prepayId + "\n";
            prepayResponse.paySign = WXPayUtility.sign(verifyString, "SHA256withRSA", WXPayUtility.loadPrivateKeyFromString(privateKey));

            appOrdersLogService.createLog(existingOrder.userId, existingOrder.id,
                    existingOrder.companyId, "重新支付成功，订单号：" + existingOrder.orderNo);
            return new ResponseDto(200, "创建支付订单成功", prepayResponse);

        } catch (WXPayUtility.ApiException e) {
            logger.error("重新支付创建微信预支付订单失败，订单ID：{}，错误：{}", orderDto.id, e.getMessage(), e);
            appOrdersLogService.createLog(existingOrder.userId, existingOrder.id,
                    existingOrder.companyId, "重新支付失败：" + e.getMessage());
            return handleWechatPayException(e);
        }
    }

    /**
     * 处理新订单创建
     */
    private ResponseDto handleNewOrder(AppOrdersDto orderDto) {
        // 检查是否可以创建订单
        if (!canCreateOrder(orderDto)) {
            return new ResponseDto(400, "存在已支付/未支付订单/必须买完风险报告才能买！", null);
        }

        // 生成订单号
        String code = orderDto.serviceId == APP_SERVICE_TYPE_2.typeCode ? "AI" : "TX";
        orderDto.orderNo = code + FormatUtils.DATE_FORMAT_YMDHMS.get().format(new Date()) + randomNum4();
        orderDto.serviceName = getName(orderDto.serviceId.intValue());

        try {
            // 创建微信预支付订单
            DirectAPIv3AppPrepayResponse response = createWechatPrepayOrder(orderDto);

            // 保存订单到数据库
            AppOrders order = new AppOrders();
            order.userId = orderDto.userId;
            order.companyId = orderDto.companyId;
            order.serviceId = orderDto.serviceId;
            order.serviceName = orderDto.serviceName;
            order.amount = orderDto.amount;
            order.orderStatus = ORDER_STATUS_PENDING;
            order.orderNo = orderDto.orderNo;
            // 先保存订单，获取生成的ID
            AppOrders saveOrder = appOrdersDao.save(order);

            // 返回预支付订单信息
            PrepayResponse prepayResponse = new PrepayResponse();
            prepayResponse.appid = appid;
            prepayResponse.partnerId = mchid;
            prepayResponse.prepayId = response.prepayId;
            prepayResponse.packageValue = response;
            prepayResponse.nonceStr = WXPayUtility.createNonce(32);
            prepayResponse.timestamp = String.valueOf(new Date().getTime());
            prepayResponse.orderId = saveOrder.orderNo;
            String prepayId = "prepay_id=" + prepayResponse.prepayId;
            String verifyString = prepayResponse.appid + "\n" + prepayResponse.timestamp + "\n" + prepayResponse.nonceStr + "\n" +
                    prepayId + "\n";
            prepayResponse.paySign = WXPayUtility.sign(verifyString, "SHA256withRSA", WXPayUtility.loadPrivateKeyFromString(privateKey));


            // 使用保存后的订单ID创建日志
            appOrdersLogService.createLog(saveOrder.userId, saveOrder.id, saveOrder.companyId,
                    "创建订单成功，订单号：" + saveOrder.orderNo);
            return new ResponseDto(200, "创建订单成功", prepayResponse);

        } catch (WXPayUtility.ApiException e) {
            logger.error("创建微信预支付订单失败，用户ID：{}，错误：{}", orderDto.userId, e.getMessage(), e);
            appOrdersLogService.createLog(orderDto.userId, null, orderDto.companyId,
                    "创建订单失败：" + e.getMessage());
            return handleWechatPayException(e);
        }
    }

    /**
     * 处理微信支付异常
     */
    private ResponseDto handleWechatPayException(WXPayUtility.ApiException e) {
        int statusCode = e.getStatusCode();
        String errorCode = e.getErrorCode();

        // 根据不同的错误码返回不同的提示
        if (statusCode == 400) {
            if ("PARAM_ERROR".equals(errorCode)) {
                return new ResponseDto(400, "订单参数错误", null);
            } else if ("OUT_TRADE_NO_USED".equals(errorCode)) {
                return new ResponseDto(400, "订单号重复，请重新下单", null);
            }
        } else if (statusCode == 403) {
            return new ResponseDto(403, "商户权限不足", null);
        } else if (statusCode >= 500) {
            return new ResponseDto(500, "微信支付服务暂时不可用，请稍后重试", null);
        }

        return new ResponseDto(400, "支付订单创建失败：" + e.getMessage(), null);
    }

    /**
     * 创建微信支付预订单
     * @param orderDto 订单信息
     * @return 微信支付预订单响应
     * @throws WXPayUtility.ApiException 微信支付API异常
     */
    private DirectAPIv3AppPrepayResponse createWechatPrepayOrder(AppOrdersDto orderDto) throws WXPayUtility.ApiException {
        WxAppPrepay client = new WxAppPrepay(
                mchid,
                certificateSerialNo,
                privateKey,
                wechatPayPublicKeyId,
                wechatPayPublicKey
        );

        orderDto.tradeType = orderDto.tradeType != null ? orderDto.tradeType : TRADE_TYPE_JSAPI;
        // 使用PaymentHelper构建支付请求
        CommonPrepayRequest request = PaymentHelper.buildPaymentRequest(
                appid,
                mchid,
                orderDto.serviceName,
                orderDto.orderNo,
                notifyUrl,
                orderDto.tradeType,
                orderDto.payerOpenid,
                convertYuanToFen(orderDto.amount)
        );

        return client.run(orderDto.tradeType, request);
    }
    /**
     * 检查是否可以创建订单
     * @param orderDto 订单信息
     * @return 是否可以创建订单
     */
    private boolean canCreateOrder(AppOrdersDto orderDto) {
        try {
            Date endDate = new Date();
            Date startDate = DateUtils.addMinutes(new Date(), -30);

            // 检查是否存在未支付的订单
            AppOrders existingOrder = appOrdersDao.findAppOrders(orderDto.userId, orderDto.serviceId, orderDto.companyId, ORDER_STATUS_PENDING, startDate, endDate);
            // 检查是否存在已支付订单
            AppOrders paidOrder = appOrdersDao.findAppOrders(orderDto.userId, orderDto.serviceId, orderDto.companyId, ORDER_STATUS_PAID, startDate, endDate);

            // 所有服务存在未支付订单都不能再创建订单
            if (existingOrder != null) {
                appOrdersLogService.createLog(orderDto.userId, existingOrder.id, orderDto.companyId,
                        "存在未支付订单！订单号：" + existingOrder.orderNo);
                return false;
            }

            // 企业税务风险检测报告每个月只能购买一次
            if (orderDto.serviceId == APP_SERVICE_TYPE_1.typeCode && paidOrder != null) {
                appOrdersLogService.createLog(orderDto.userId, paidOrder.id, orderDto.companyId,
                        "已存在本月已支付订单！订单号：" + paidOrder.orderNo);
                return false;
            }

            // 需求：必须买完风险报告才能买AI报告
            if (APP_SERVICE_TYPE_2.typeCode == orderDto.serviceId){
                // 检查是否存在已支付订单
                AppOrders paidS1 = appOrdersDao.findAppOrders(orderDto.userId, (long) APP_SERVICE_TYPE_1.typeCode, orderDto.companyId, ORDER_STATUS_PAID, startDate, endDate);
                if (paidS1 == null) {
                    appOrdersLogService.createLog(orderDto.userId, null, orderDto.companyId,
                            "必须买完风险报告才能买AI报告");
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            logger.error("检查订单创建权限异常，用户ID：{}，公司ID：{}，服务ID：{}",
                    orderDto.userId, orderDto.companyId, orderDto.serviceId, e);
            appOrdersLogService.createLog(orderDto.userId, orderDto.id, orderDto.companyId,
                    "检查订单创建权限异常" + e.getMessage());
            return false;
        }
    }

    /**
     * 微信支付回调处理
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDto
    wxAppNotify(NotifyUtils.NotifyRequest notify, HttpServletRequest request) {
        String orderNo = null;
        RLock lock = null;

        try {
            // 1. 验证请求头
            ResponseDto headerValidation = validateNotifyHeaders(request);
            if (headerValidation != null) {
                return headerValidation;
            }

            // 2. 验签和解密
            NotifyUtils.TransactionInfo notifyData = verifyAndDecryptNotify(notify, request);
            if (notifyData == null) {
                return new ResponseDto(400, "回调数据验证失败", null);
            }

            // 3. 验证支付状态
            if (!StringUtils.equals("SUCCESS", notifyData.tradeState)) {
                String errorMsg = "支付未成功，状态：" + notifyData.tradeState;
                appOrdersLogService.createLog(null, null, null,
                        notifyData.outTradeNo + "#" + errorMsg);
                return new ResponseDto(400, errorMsg, null);
            }

            // 4. 获取订单号并加锁防重复处理
            orderNo = notifyData.outTradeNo;
            if (StringUtils.isBlank(orderNo)) {
                appOrdersLogService.createLog(null, null, null,
                        "微信交易号：" + notifyData.transactionId + "#回调数据中未包含商户订单号");
                return new ResponseDto(400, "缺少商户订单号", null);
            }

            // 使用分布式锁防止重复处理
            if (redissonClient != null) {
                String lockKey = "payment_callback_" + orderNo;
                lock = redissonClient.getLock(lockKey);
                if (!lock.tryLock(10, TimeUnit.SECONDS)) {
                    logger.warn("获取支付回调锁失败，订单号：{}", orderNo);
                    return new ResponseDto(200, "处理中，请稍后", null);
                }
            }

            // 5. 处理订单支付
            return processOrderPayment(orderNo, notifyData);

        } catch (Exception e) {
            logger.error("微信支付回调处理异常，订单号：{}", orderNo, e);
            appOrdersLogService.createLog(null, null, null, "回调处理异常：" + e.getMessage());
            return new ResponseDto(500, "处理失败", null);
        } finally {
            // 释放锁
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 验证回调请求头
     */
    private ResponseDto validateNotifyHeaders(HttpServletRequest request) {
        String wechatpayTimestamp = request.getHeader("Wechatpay-Timestamp");
        String wechatpayNonce = request.getHeader("Wechatpay-Nonce");
        String wechatpaySignature = request.getHeader("Wechatpay-Signature");
        String wechatpaySerial = request.getHeader("Wechatpay-Serial");

        if (StringUtils.isAnyBlank(wechatpayTimestamp, wechatpayNonce,
                wechatpaySignature, wechatpaySerial)) {
            appOrdersLogService.createLog(null, null, null, "微信回调缺少必要的请求头信息");
            return new ResponseDto(400, "缺少必要的请求头信息", null);
        }

        return null;
    }

    /**
     * 验签和解密回调数据
     */
    private NotifyUtils.TransactionInfo verifyAndDecryptNotify(
            NotifyUtils.NotifyRequest notify, HttpServletRequest request) {
        try {
            String wechatpayTimestamp = request.getHeader("Wechatpay-Timestamp");
            String wechatpayNonce = request.getHeader("Wechatpay-Nonce");
            String wechatpaySignature = request.getHeader("Wechatpay-Signature");
            String wechatpaySerial = request.getHeader("Wechatpay-Serial");

            // 获取完整的请求体JSON字符串
            String requestBody = getRequestBody(request);
            if (StringUtils.isBlank(requestBody)) {
                appOrdersLogService.createLog(null, null, null, "微信回调请求体为空");
                return null;
            }

            // 构建验签字符串 - 根据微信支付回调验签规范
            String verifyString = wechatpayTimestamp + "\n" + wechatpayNonce + "\n" + requestBody + "\n";

            // 记录验签相关信息用于调试
            logger.info("微信回调验签信息 - 时间戳: {}, 随机串: {}, 序列号: {}",
                    wechatpayTimestamp, wechatpayNonce, wechatpaySerial);

            // 获取公钥并验签
            PublicKey publicKey;
            if (wechatpaySerial.startsWith("PUB_KEY_ID_")) {
                publicKey = WXPayUtility.loadPublicKeyFromString(wechatPayPublicKey);
            } else {
                publicKey = WXPayUtility.loadPublicKeyFromString(platformCert);
            }

            boolean verifyResult = WXPayUtility.verify(verifyString, wechatpaySignature,
                    "SHA256withRSA", publicKey);
            if (!verifyResult) {
                appOrdersLogService.createLog(null, null, null,
                        "微信回调验签失败，序列号：" + wechatpaySerial + "，验签字符串长度：" + verifyString.length());
                logger.error("微信回调验签失败，验签字符串: [{}]", verifyString);
                return null;
            }

            // 解密回调数据
            NotifyUtils.ResourceInfo resource = notify.resource;
            AesUtil aesUtil = new AesUtil(apiV3Key.getBytes(StandardCharsets.UTF_8));
            String plaintext = aesUtil.decryptToString(
                    resource.associatedData.getBytes(StandardCharsets.UTF_8),
                    resource.nonce.getBytes(StandardCharsets.UTF_8),
                    resource.ciphertext
            );

            // 解析订单信息
            return WXPayUtility.fromJson(plaintext, NotifyUtils.TransactionInfo.class);

        } catch (GeneralSecurityException e) {
            logger.error("微信回调验签或解密失败", e);
            appOrdersLogService.createLog(null, null, null, "解密或验签失败：" + e.getMessage());
            return null;
        } catch (JsonSyntaxException e) {
            logger.error("微信回调数据解析失败", e);
            appOrdersLogService.createLog(null, null, null, "解析订单信息失败：" + e.getMessage());
            return null;
        } catch (IOException e) {
            logger.error("微信回调数据处理IO异常", e);
            appOrdersLogService.createLog(null, null, null, "回调数据处理IO异常：" + e.getMessage());
            return null;
        }
    }

    /**
     * 获取请求体内容
     */
    private String getRequestBody(HttpServletRequest request) {
        try {
            StringBuilder sb = new StringBuilder();
            String line;
            BufferedReader reader = request.getReader();
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
            return sb.toString();
        } catch (IOException e) {
            logger.error("读取请求体失败", e);
            return null;
        }
    }

    /**
     * 处理订单支付
     */
    private ResponseDto processOrderPayment(String orderNo, NotifyUtils.TransactionInfo notifyData) {
        try {
            AppOrders order = appOrdersDao.findByOrderNo(orderNo);
            if (order == null) {
                appOrdersLogService.createLog(null, null, null, "订单不存在，订单号：" + orderNo);
                return new ResponseDto(404, "订单不存在", null);
            }

            // 防止重复处理
            if (order.orderStatus == ORDER_STATUS_PAID) {
                appOrdersLogService.createLog(order.userId, order.id, order.companyId,
                        "订单已支付，无需重复处理，订单号：" + orderNo);
                return new ResponseDto(200, "通知成功", null);
            }

            // 更新订单状态
            order.orderStatus = ORDER_STATUS_PAID;
            order.transactionId = notifyData.transactionId;
            order.tradeType = notifyData.tradeType;

            // 解析支付时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss+08:00");
            try {
                order.paymentTime = sdf.parse(notifyData.successTime);
            } catch (ParseException e) {
                logger.warn("解析支付时间失败，使用当前时间，原时间：{}", notifyData.successTime);
                order.paymentTime = new Date();
            }

            appOrdersDao.save(order);

            // 记录成功日志
            appOrdersLogService.createLog(order.userId, order.id, order.companyId,
                    "订单支付成功，订单号：" + orderNo + "，微信交易号：" + notifyData.transactionId);

            return new ResponseDto(200, "通知成功", null);

        } catch (Exception e) {
            logger.error("处理订单支付异常，订单号：{}", orderNo, e);
            appOrdersLogService.createLog(null, null, null,
                    "处理订单支付异常，订单号：" + orderNo + "，错误：" + e.getMessage());
            throw e;
        }
    }

    /**
     * 查询订单列表
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDto findOrders(AppOrdersDto orderDto, Pageable pageable) {
        try {
            if (orderDto.userId == null) {
                return new ResponseDto(400, "用户ID不能为空", null);
            }
            if (orderDto.companyId == null) {
                return new ResponseDto(400, "公司ID不能为空", null);
            }

            // 查询订单列表
            List<AppOrders> orders = appOrdersDao.findByUserIdAndCompanyId(
                    orderDto.userId, orderDto.companyId);

            // 处理超时订单
            processExpiredOrders(orders);

            List<AppOrdersDto> orderDtos = BeanMapper.mapList(orders, AppOrdersDto.class);
            List<AppCompany> appCompanies = appCompanyDao.findByUserId(orderDto.userId);
            for (AppOrdersDto order : orderDtos) {
                // 填充公司名称
                AppCompany appCompany = order.getAppCompany(appCompanies, order.companyId);
                if (appCompany != null) {
                    order.companyName = appCompany.name;
                }

                // 格式化支付时间
                if (order.paymentTime != null) {
                    order.paymentTimeStr = FormatUtils.DATE_FORMAT_YMD_CN.get()
                            .format(order.paymentTime);
                }
            }

            // 分页处理
            RamPager<AppOrdersDto> pager = new RamPager<>(orderDtos, pageable);
            return new ResponseDto(200, "查询成功", pager.pageResult());

        } catch (Exception e) {
            logger.error("查询订单列表异常，用户ID：{}", orderDto.userId, e);
            return new ResponseDto(500, "查询失败", null);
        }
    }

    /**
     * 当前的处理方式是遍历所有订单，可以优化为只处理待支付订单
     */
    private void processExpiredOrders(List<AppOrders> orders) {
        Date now = new Date();
        List<AppOrders> ordersToUpdate = new ArrayList<>();

        for (AppOrders order : orders) {
            if (order.orderStatus == ORDER_STATUS_PENDING &&
                    order.gmtCreate != null &&
                    (now.getTime() - order.gmtCreate.getTime()) > ORDER_TIMEOUT_MILLIS) {

                order.orderStatus = ORDER_STATUS_CANCELLED;
                order.cancelTime = now;
                ordersToUpdate.add(order);
            }
        }

        // 批量更新，减少数据库操作
        if (!ordersToUpdate.isEmpty()) {
            try {
                appOrdersDao.saveAll(ordersToUpdate);
                // 批量记录日志
                for (AppOrders order : ordersToUpdate) {
                    appOrdersLogService.createLog(order.userId, order.id, order.companyId,
                            "订单超时自动取消，订单号：" + order.orderNo);
                }
            } catch (Exception e) {
                logger.error("批量处理超时订单异常", e);
            }
        }
    }

    /**
     * 设置报告信息
     */
    public ResponseDto setReportMessage(TaxReportDto taxReportDto) {
        // 根据订单id查询订单
        AppOrders order = appOrdersDao.findByOrderNo(taxReportDto.orderId);
        if (order == null) {
            return new ResponseDto(404, "订单不存在", null);
        }

        // 设置报告信息
        order.resultMsg = taxReportDto.resultMessage;
        order.reportUrl = taxReportDto.resultUrl;
        order.reportStatus = taxReportDto.state;
        appOrdersDao.save(order);
        return new ResponseDto(200, "设置成功", null);
    }
}
