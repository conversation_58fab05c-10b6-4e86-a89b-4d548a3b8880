package com.hongfund.efi.repository;

import com.hongfund.efi.domain.TaxReport;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TaxReportDao extends JpaRepository<TaxReport, Long> {
    TaxReport findTaxReportById(Long id);

    TaxReport findFirstByStateAndAreaIdInAndAccountBookIdAndActionIdOrderByGmtModifiedAsc(int waiting, List<Integer> areaIdList, Long id, Integer actionId);

    @Query("select g from TaxReport g where g.state = 2 and g.orderLevel >= 0 and g.areaId in ?1 and g.actionId = ?2 " +
            "and (g.resultMessage is null or g.resultMessage not like '%#runNum#5%') " +
            "and (coalesce(?3, 1) = 1 or g.taxPayerPhone is null or g.taxPayerPhone not in ?3) order by g.orderLevel desc,g.gmtModified asc")
    List<TaxReport> findCompanyWaiting(List<Integer> areaIdList, Integer actionId, List<String> phoneList, PageRequest of);

    @Query("select t from TaxReport t where t.taxCode = ?1 and t.state = 4 order by t.id desc")
    List<TaxReport> findData(String taxCode, Pageable pageable);

    @Query("select g from TaxReport g where g.accountBookId in ?1")
    List<TaxReport> findByAccountBookIdIn(List<Long> ids);
}
