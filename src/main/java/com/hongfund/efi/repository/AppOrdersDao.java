package com.hongfund.efi.repository;

import com.hongfund.efi.domain.AppOrders;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

/**
 * 订单表数据访问接口
 * 基于Spring Data JPA的Dao接口, 自动根据接口生成实现.
 */
public interface AppOrdersDao extends JpaRepository<AppOrders, Long> {

    @Query("select o from AppOrders o where o.id = ?1")
    AppOrders findOne(Long id);

    @Query("select o from AppOrders o where o.orderNo = ?1")
    AppOrders findByOrderNo(String orderNo);

    @Query("select o from AppOrders o where o.userId = ?1 and o.serviceId = ?2 and o.companyId = ?3 and o.orderStatus = ?4 and o.gmtModified between ?5 and ?6 order by o.gmtModified desc")
    AppOrders findAppOrders(Long userId, Long serviceId, Long companyId, int i, Date startDate, Date endDate);

    List<AppOrders> findByUserIdAndCompanyId(Long userId, Long companyId);
    
}
